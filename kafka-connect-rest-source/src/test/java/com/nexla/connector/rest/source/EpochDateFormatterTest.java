package com.nexla.connector.rest.source;

import com.nexla.common.time.EpochDateTimeFormatter;
import com.nexla.test.UnitTests;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.experimental.categories.Category;

import static org.junit.Assert.assertEquals;

@Category(UnitTests.class)
public class EpochDateFormatterTest {

	@Test
	public void testEpoch() {
		EpochDateTimeFormatter formatter = new EpochDateTimeFormatter("eeee");
		String result = formatter.print(DateTime.parse("2018-04-11T12:22:33.000Z"));
		assertEquals("1523449353000", result);
	}

	@Test
	public void testEpochSeconds() {
		EpochDateTimeFormatter formatter = new EpochDateTimeFormatter("ssss");
		String result = formatter.print(DateTime.parse("2018-04-11T12:22:33.000Z"));
		assertEquals("1523449353", result);
	}

	@Test
	public void testYyyyDdMm() {
		EpochDateTimeFormatter formatter = new EpochDateTimeFormatter("yyyy-MM-dd");
		String result = formatter.print(DateTime.parse("2018-04-11T12:22:33.000Z"));
		assertEquals("2018-04-11", result);
	}
}
