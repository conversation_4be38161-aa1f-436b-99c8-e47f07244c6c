package com.nexla.connector.rest.source;

import com.nexla.connect.common.connector.BaseSourceConnector;
import com.nexla.connector.config.rest.RestSourceConnectorConfig;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.connector.Task;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.Collections.singletonList;

public class RestSourceConnector extends BaseSourceConnector<RestSourceConnectorConfig> {

	@Override
	protected String telemetryAppName() {
		return "rest-source";
	}

	@Override
	protected RestSourceConnectorConfig parseConfig(Map<String, String> props) {
		return new RestSourceConnectorConfig(props);
	}

	@Override
	public Class<? extends Task> taskClass() {
		return RestSourceTask.class;
	}

	@Override
	public ConfigDef config() {
		return RestSourceConnectorConfig.configDef();
	}

	@Override
	public List<Map<String, String>> taskConfigs(int maxTasks) {
		return singletonList(new HashMap<>(config.originalsStrings()));
	}
}
