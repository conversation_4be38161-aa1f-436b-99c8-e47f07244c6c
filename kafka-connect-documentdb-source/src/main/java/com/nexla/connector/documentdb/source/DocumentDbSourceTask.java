package com.nexla.connector.documentdb.source;

import com.google.common.collect.Iterables;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.datetime.DateTimeUtils;
import com.nexla.common.tracker.SourceItem;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.BaseSourceTask;
import com.nexla.connect.common.CollectRecordsResult;
import com.nexla.connect.common.SourceRecordCreator;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connector.config.documentdb.DocumentDbSourceConnectorConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.ssh.tunnel.SshTunnelSupport;
import com.nexla.connector.properties.DocumentDbConfigAccessor;
import com.nexla.connector.properties.RestConfigAccessor;
import com.nexla.probe.documentdb.DocumentDbConnectorService;
import com.nexla.probe.documentdb.DocumentDbConnectorService.Document;
import com.nexla.probe.dynamodb.DynamoDbService;
import com.nexla.probe.firebase.FirebaseService;
import com.nexla.probe.mongodb.MongoDbService;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.errors.ConnectException;
import org.apache.kafka.connect.source.SourceRecord;
import org.joda.time.DateTime;
import scala.Function2;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.ConnectionType.*;
import static com.nexla.common.MetricUtils.calcBytes;
import static com.nexla.common.NexlaConstants.VERSION;
import static com.nexla.common.ResourceType.SOURCE;
import static java.util.Collections.emptyMap;
import static java.util.Collections.singletonMap;
import static org.apache.kafka.connect.data.Schema.STRING_SCHEMA;
import static org.joda.time.DateTimeZone.UTC;

public class DocumentDbSourceTask extends BaseSourceTask<DocumentDbSourceConnectorConfig> implements SshTunnelSupport {

    private DocumentDbConnectorService<BaseAuthConfig> documentDB;
    private FetchStrategy fetchStrategy;
    private String taskPartition;

    @Override
    public String version() {
        return VERSION;
    }

    @Override
    public ConfigDef configDef() {
        return DocumentDbSourceConnectorConfig.configDef();
    }

    @Override
    public void doStart(Map<String, String> props) throws ConnectException {
        this.sourceTelemetryReporter = Optional.of(new ConnectorTelemetryReporter(config.sourceId, config.connectionType.name(), SOURCE, true));
        this.taskPartition = config.collection;
        
        this.documentDB = newConnectorService();
    }

    private DocumentDbConnectorService<BaseAuthConfig> newConnectorService() {
        if (config.connectionType.equals(MONGO)) {
            return new MongoDbService();
        } else if (config.connectionType.equals(FIREBASE)) {
            return new FirebaseService();
        } else if (config.connectionType.equals(DYNAMODB)) {
            return new DynamoDbService();
        }
        throw new IllegalArgumentException("Not a document based database: " + config.connectionType);
    }

    @Override
    protected DocumentDbSourceConnectorConfig parseConfig(Map<String, String> props) {
        return new DocumentDbSourceConnectorConfig(props);
    }
    
    @Override
    public CollectRecordsResult collectRecords() {
        List<SourceRecordCreator> records;
        try (Stream<NexlaMessage> fetchStrategyStream = fetchStrategy().stream()) {
            records = fetchStrategyStream
                    .map(this::toSourceRecordCreator)
                    .collect(Collectors.toList());
        }

        List<SourceRecord> result = detectSchemaIfNecessary(config.schemaDetectionOnce, records, Optional.empty());
        sendNexlaMetrics(result);
        return new CollectRecordsResult(result);
    }

    private void sendNexlaMetrics(List<SourceRecord> messages) {
        long sizeBytes = StreamEx.of(messages)
                .mapToInt(r -> calcBytes(r))
                .sum();
        sendMetrics(taskPartition, messages.size(), sizeBytes, 0L, DateTimeUtils.nowUTC().getMillis());
    }

    private SourceRecordCreator toSourceRecordCreator(NexlaMessage message) {
        long now = DateTime.now(UTC).getMillis();

        return new SourceRecordCreator(
                message.getRawMessage(),
                (datasetId, datasetIdTopic) -> {
                    NexlaMessage m = ((Function2<Integer, String, NexlaMessage>) (dataSetId1, dataSetTopic2) -> {
                        SourceItem trackerId = SourceItem.fullTracker(
                                config.sourceId,
                                dataSetId1,
                                config.collection, // todo more specific?
                                0L, // todo offset
                                config.version,
                                now
                        );

                        NexlaMetaData meta = new NexlaMetaData(
                                config.connectionType,
                                now,
                                0L, // todo offset,
                                config.collection,
                                dataSetTopic2,
                                SOURCE,
                                config.sourceId,
                                false,
                                new Tracker(Tracker.TrackerMode.FULL, trackerId),
                                runId
                        );

                        Map<String, Object> tags = message.getNexlaMetaData() == null || message.getNexlaMetaData().getTags() == null
                                ? null
                                : message.getNexlaMetaData().getTags();

                        meta.setTags(tags);

                        return new NexlaMessage(message.getRawMessage(), meta);
                    }).apply(datasetId, datasetIdTopic);

                    return new SourceRecord(singletonMap(RestConfigAccessor.PARTITION_KEY, config.collection), emptyMap(), datasetIdTopic, null, null, null, STRING_SCHEMA, toJsonString(m));
                },
                (datasetId, datasetTopic) -> {
                    SourceItem trackerId = SourceItem.fullTracker(
                            config.sourceId,
                            datasetId,
                            config.collection, // todo more specific?
                            0L, // todo offset
                            config.version,
                            now
                    );

                    NexlaMetaData meta = new NexlaMetaData(
                            config.connectionType,
                            now,
                            0L, // todo offset,
                            config.collection,
                            datasetTopic,
                            SOURCE,
                            config.sourceId,
                            false,
                            new Tracker(Tracker.TrackerMode.FULL, trackerId),
                            runId
                    );

                    Map<String, Object> tags = message.getNexlaMetaData() == null
                            ? emptyMap()
                            : message.getNexlaMetaData().getTags();

                    if (meta.getTags() != null) {
                        meta.getTags().putAll(tags);
                    } else {
                        meta.setTags(tags);
                    }

                    return new NexlaMessage(message.getRawMessage(), meta);
                }
        );
    }

    private FetchStrategy fetchStrategy() {
        if (this.fetchStrategy != null) {
            return this.fetchStrategy;
        }

        if (DocumentDbConfigAccessor.MODE_TIMESTAMP.equalsIgnoreCase(this.config.mode)) {
            return this.fetchStrategy = new TimestampBasedFetchStrategy(documentDB, config);
        }

        return this.fetchStrategy = new ForceBatchesFetchStrategy(documentDB, config);
    }

    private static class ForceBatchesFetchStrategy implements FetchStrategy {
        private final DocumentDbConnectorService<BaseAuthConfig> documentDB;
        private final DocumentDbSourceConnectorConfig config;

        private Iterator<NexlaMessage> iterator;

        public ForceBatchesFetchStrategy(DocumentDbConnectorService<BaseAuthConfig> documentDB, DocumentDbSourceConnectorConfig config) {
            this.documentDB = documentDB;
            this.config = config;
        }

        @Override
        public Stream<NexlaMessage> stream() {
            if (this.iterator == null) {
                StreamEx<NexlaMessage> docDbStream = documentDB.stream(config)
                        .map(DocumentDbConnectorService.Document::toNexlaMessage);
                this.iterator = docDbStream.iterator();
            }
            return streamOfIteratorLimited();
        }
   
        private StreamEx<NexlaMessage> streamOfIteratorLimited() {
            return StreamEx.of(this.iterator)
                    .limit(config.batchSizeApprox);
        }
   
    }
    

    private static class TimestampBasedFetchStrategy implements FetchStrategy {
        private static final long MILLIS_IN_BATCH = TimeUnit.HOURS.toMillis(3);
        private static final long NOT_INITIALIZED = -1;

        private final DocumentDbConnectorService<BaseAuthConfig> documentDB;
        private final DocumentDbSourceConnectorConfig config;
        private final int batchSizeApprox;
        private final Optional<Long> timestampLoadTo;
        private final String timestampKey;

        private long lastMilli = NOT_INITIALIZED;

        public TimestampBasedFetchStrategy(DocumentDbConnectorService<BaseAuthConfig> documentDB, DocumentDbSourceConnectorConfig config) {
            this.documentDB = documentDB;
            this.config = config;
            this.batchSizeApprox = config.batchSizeApprox;
            this.timestampKey = config.timestampKey.get();
            this.timestampLoadTo = config.timestampLoadTo;
        }

        @Override
        public Stream<NexlaMessage> stream() {
            if (lastMilli == NOT_INITIALIZED) {
                if (config.timestampLoadFrom.isEmpty()) {
                    config.batchSizeApprox = 1;
                    try(StreamEx<? extends Document> docDBStream = documentDB.stream(config)) {
                        Optional<NexlaMessage> first = docDBStream
                                .limit(1)
                                .map(DocumentDbConnectorService.Document::toNexlaMessage)
                                .findFirst();

                        if (first.isEmpty()) { // no docs at all
                            return StreamEx.empty();
                        }

                        lastMilli = millis(timestampKey, first.get().getRawMessage());
                        config.batchSizeApprox = batchSizeApprox; // restore hacked config
                    }
                } else {
                    lastMilli = config.timestampLoadFrom.get();
                }
            }

            List<NexlaMessage> messages = new ArrayList<>();
            final long loadTo = timestampLoadTo.orElse(System.currentTimeMillis());

            while (messages.size() < batchSizeApprox && lastMilli < loadTo) {
                config.timestampLoadFrom = Optional.of(lastMilli);
                // Pick loadTo timestamp or 3-hour batch timestamp
                config.timestampLoadTo = Optional.of(loadToTimestampOrBatchValue(loadTo));

                try(StreamEx<? extends Document> docDBStream = documentDB.stream(config)) {
                    
                    try(StreamEx<NexlaMessage> stream = docDBStream.map(DocumentDbConnectorService.Document::toNexlaMessage)
                                .onClose(docDBStream::close)) {

                        // Set new lastMilli for next batch
                        lastMilli = config.timestampLoadTo.get();

                        // Clear first last from previous batch if It's present in new batch.
                        if (!messages.isEmpty()) {
                            NexlaMessage last = Iterables.getLast(messages);
                            messages.addAll(stream.dropWhile(m -> 
                                    millis(timestampKey, m.getRawMessage()) == millis(timestampKey, last.getRawMessage())
                            ).toList());
                        } else messages.addAll(stream.toList());
                    }
                }
            }
            return StreamEx.of(messages);
        }

        private long loadToTimestampOrBatchValue(long loadTo) {
            return lastMilli + Math.min(MILLIS_IN_BATCH, (loadTo - lastMilli));
        }

        private long millis(String key, LinkedHashMap<String, Object> message) {
            Object o = message.get(key);
            if (o instanceof Timestamp) {
                return ((Timestamp) o).getTime();
            }

            return Long.parseLong(String.valueOf(message.get(key)));
        }
    }

    public interface FetchStrategy {
        Stream<NexlaMessage> stream();
    }
}