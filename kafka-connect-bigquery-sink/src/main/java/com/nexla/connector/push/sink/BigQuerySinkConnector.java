package com.nexla.connector.push.sink;

import com.nexla.connect.common.connector.BaseSinkConnector;
import com.nexla.connector.config.big_query.BigQuerySinkConnectorConfig;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.connector.Task;
import org.apache.kafka.connect.errors.ConnectException;

import java.util.List;
import java.util.Map;

import static java.util.Collections.nCopies;

public class BigQuerySinkConnector extends BaseSinkConnector {
	public static final String BIGQUERY_SINK_TELEMETRY_NAME = "bigquery-sink";

	@Override
	protected String telemetryAppName() {
		return BIGQUERY_SINK_TELEMETRY_NAME;
	}

	@Override
	public Class<? extends Task> taskClass() {
		return BigQuerySinkTask.class;
	}

	@Override
	public ConfigDef config() {
		return BigQuerySinkConnectorConfig.configDef();
	}
}
