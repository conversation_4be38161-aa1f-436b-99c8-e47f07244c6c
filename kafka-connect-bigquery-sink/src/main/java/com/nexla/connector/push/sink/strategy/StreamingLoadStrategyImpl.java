package com.nexla.connector.push.sink.strategy;

import com.bazaarvoice.jolt.JsonUtils;
import com.google.api.core.ApiFuture;
import com.google.api.core.ApiFutureCallback;
import com.google.api.core.ApiFutures;
import com.google.cloud.bigquery.storage.v1.*;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.Descriptors;
import com.nexla.common.logging.NexlaLogger;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Phaser;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;

public class StreamingLoadStrategyImpl implements LoadStrategy {

	private final NexlaLogger logger;
	private final BigQueryWriteClient client;
	private final LoadLifecycleObserver lifecycleObserver;

	private final JsonStreamWriter streamWriter;
	private final Phaser inflightBatches = new Phaser(1);
	private final TypeConverter typeConverter;
	private final WriteStream writeStream;
	private boolean isClosed;

	@SneakyThrows
	public StreamingLoadStrategyImpl(NexlaLogger logger, BigQueryWriteClient client, TableName table, LoadLifecycleObserver lifecycleObserver) {
		// :(
		// we should investigate if we can do MDC.
		this.logger = logger;
		this.client = client;
		this.lifecycleObserver = lifecycleObserver;

		WriteStream stream = WriteStream.newBuilder().setType(WriteStream.Type.COMMITTED).build();

		CreateWriteStreamRequest createWriteStreamRequest =
				CreateWriteStreamRequest.newBuilder()
						.setParent(table.toString())
						.setWriteStream(stream)
						.build();

		writeStream = client.createWriteStream(createWriteStreamRequest);

		streamWriter = JsonStreamWriter.newBuilder(writeStream.getName(), writeStream.getTableSchema(), client).build();
		typeConverter = new TypeConverter(logger, writeStream.getTableSchema());
	}

	@SneakyThrows
	@Override
	public void append(List<LinkedHashMap<String, Object>> messages, BatchObserver batchObserver) {
		try {
			logger.debug("Pushing {} messages to stream", messages.size());

			if (messages.isEmpty()) {
				logger.warn("Batch is empty, won't push");
				return;
			}

			List<JSONObject> objects = messages
					.stream()
					.map(m -> {
						try {
							LinkedHashMap<String, Object> typeMapped = typeConverter.convert(m);

							if (typeMapped.isEmpty()) {
								logger.warn("Message is empty after type conversion, original message: {}", JsonUtils.toJsonString(m));
								// it will be skipped by filter op
								return null;
							}

							return typeMapped;
						} catch (Throwable t) {
							batchObserver.error(m, t);

							return null;
						}
					})
					.filter(Objects::nonNull)
					.map(JSONObject::new)
					.collect(toList());

			if (objects.isEmpty()) {
				logger.warn("Object lists is empty, reporting error. Table schema = {}", writeStream.getTableSchema());
				batchObserver.error(new RuntimeException("Message List is empty after type conversion. This might happen if message doesn't correspond to table's schema."));
				return;
			}

			ApiFuture<AppendRowsResponse> appendFut;
			try {
				appendFut = streamWriter.append(new JSONArray(objects));
			} catch (IOException e) {
				logger.error("Append failed with i/o error", e);
				throw e;
			} catch (Descriptors.DescriptorValidationException e) {
				logger.error("Append failed with serialization error:\nDiagnostics info: \nDescription: {}\nProblemProto: {}\nProblemSymbolName: {}",
						e.getDescription(),
						e.getProblemProto(),
						e.getProblemSymbolName(),
						e
				);

				if (e.getSuppressed() != null) {
					Arrays.stream(e.getSuppressed()).forEach(t -> logger.error("Suppressed exception: ", t));
				}

				throw e;
			} catch (Exceptions.AppendSerializtionError e) {
				logger.error("Append failed with serialization error. Issues: \n{}",
						JsonUtils.toPrettyJsonString(e.getRowIndexToErrorMessage()),
						e
				);

				logger.info("Table schema: {}", writeStream.getTableSchema());

				if (e.getSuppressed() != null) {
					Arrays.stream(e.getSuppressed()).forEach(t -> logger.error("Suppressed exception: ", t));
				}

				throw e;
			}

			inflightBatches.register();

			ApiFutures.addCallback(
					appendFut,
					newBatchHandler(batchObserver),
					MoreExecutors.directExecutor()
			);
			logger.debug("Successfully pushed messages & registered batch");
		} catch (Throwable t) {
			batchObserver.error(t);
		}
	}


	private ApiFutureCallback<AppendRowsResponse> newBatchHandler(BatchObserver batchObserver) {
		return new ApiFutureCallback<>() {
			@Override
			public void onFailure(Throwable t) {
				batchObserver.error(t);
				inflightBatches.arriveAndDeregister();
			}

			@Override
			public void onSuccess(AppendRowsResponse result) {
				batchObserver.success();
				inflightBatches.arriveAndDeregister();
			}
		};
	}

	private static class TypeConverter {
		private final boolean ignoreUnknownProperties = true;
		private final Map<String, Function<Object, Object>> fieldNameToConverter;
		private final Logger logger;

		public TypeConverter(Logger logger, TableSchema schema) {
			this(logger, schema.getFieldsList());
		}

		public TypeConverter(Logger logger, List<TableFieldSchema> fields) {
			this.logger = logger;

			this.fieldNameToConverter = StreamEx.of(fields)
					.toMap(f -> f.getName().toLowerCase(), this::toConverter);

			this.logger.debug("TypeConverter converters: {}", this.fieldNameToConverter.keySet());
		}

		public Object convert(String name, Object value) {
			if (value == null) return null;

			Function<Object, Object> converter = fieldNameToConverter.get(name.toLowerCase());
			if (converter == null) {
				logger.warn("Converter is not defined for field: {}", name);
				converter = Function.identity();
			}

			return converter
					.apply(value);
		}

		private LinkedHashMap<String, Object> convert(Map<String, Object> m) {
			LinkedHashMap<String, Object> result = new LinkedHashMap<>(m.size());
			m.forEach((k, v) -> {
				if (ignoreUnknownProperties && !fieldNameToConverter.containsKey(k.toLowerCase())) {
					return;
				}

				result.put(k, this.convert(k, v));
			});
			return result;
		}

		private Function<Object, Object> toConverter(TableFieldSchema f) {
			switch (f.getType()) {
				case STRING:
					return Object::toString;
				case INT64:
					return o -> Long.valueOf(o.toString());
				case DOUBLE:
					return o -> Double.valueOf(o.toString());
				case BOOL:
					return o -> Boolean.valueOf(o.toString());
				case BIGNUMERIC:
					return o -> new BigDecimal(o.toString());

				case STRUCT:
					TypeConverter nested = new TypeConverter(logger, f.getFieldsList());
					return o -> {
						if (o instanceof Map) {
							return nested.convert((Map<String, Object>) o);
						}
						return o;
					};

				case DATETIME:
					return new DateTimeConversionFn(logger);

				// handled by the streaming lib conversions. Supported type are strings (in format supported by Java Time API) and longs.
				case DATE:
				case TIME:
				case TIMESTAMP:
				case GEOGRAPHY:
				case NUMERIC:
				case INTERVAL:
				case JSON:
				case BYTES:
				case TYPE_UNSPECIFIED:
				case UNRECOGNIZED:

				default:
					return o -> o;
			}
		}
	}

	private static class DateTimeConversionFn implements Function<Object, Object> {

		private static final List<SimpleDateFormat> SUPPORTED_FORMATS = List.of(
				newSimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSSZ"),
				newSimpleDateFormat("yyyy-MM-dd HH:mm:ss"),
				newSimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
		);

		private final Logger logger;

		public DateTimeConversionFn(Logger logger) {
			this.logger = logger;
		}

		private static SimpleDateFormat newSimpleDateFormat(String x) {
			SimpleDateFormat format = new SimpleDateFormat(x);
			format.setTimeZone(TimeZone.getTimeZone("UTC"));
			format.setLenient(false);
			return format;
		}

		@Override
		public Object apply(Object o) {
			if (o instanceof String) {
				try {
					return LocalDateTime.parse((String) o).toString();
				} catch (Exception ignored) {}

				try {
					// this is for BBB datetime debugging
					return DateTimeFormatter.ISO_LOCAL_DATE_TIME.format(
							newSimpleDateFormat("yyyy-MM-dd HH:mm:ss")
									.parse(o.toString()).toInstant().atZone(ZoneId.of("UTC"))
					);
				} catch (Exception e) {
					logger.debug("Can't format {} as a datetime", o, e);
				}

				for (SimpleDateFormat sdf : SUPPORTED_FORMATS) {
					try {
						return DateTimeFormatter.ISO_LOCAL_DATE_TIME.format(
								sdf.parse(o.toString()).toInstant().atZone(ZoneId.of("UTC"))
						);
					} catch (Exception ignored) {}
				}
			}

			logger.debug("DateTime values were not converted in converter fn. Class {}, Value: {}", o == null ? "(null)" : o.getClass(), o);

			return o;
		}
	}

	@Override
	public void flush() {
		logger.info("Waiting inprogress batches to complete");
		inflightBatches.arriveAndAwaitAdvance();
	}

	@Override
	public void close() {
		this.isClosed = true;
		logger.info("Closing streaming load strategy...");
		flush();

		try {
			streamWriter.close();
			client.finalizeWriteStream(streamWriter.getStreamName());
		} catch (Throwable t) {
			logger.error("Error during stream finalization", t);
		}

		this.lifecycleObserver.onClose();
		logger.info("Streaming load strategy closed.");
	}

	@Override
	public boolean isClosed() {
		return isClosed;
	}
}
