package com.nexla.parser;

import name.neuhalfen.projects.crypto.bouncycastle.openpgp.BouncyGPG;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.algorithms.PGPAlgorithmSuite;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.algorithms.PGPCompressionAlgorithms;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.algorithms.PGPHashAlgorithms;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.algorithms.PGPSymmetricEncryptionAlgorithms;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.keys.callbacks.KeyringConfigCallbacks;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.keys.keyrings.KeyringConfig;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.keys.keyrings.KeyringConfigs;
import org.apache.commons.io.IOUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openpgp.PGPException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.attribute.PosixFilePermission;
import java.nio.file.attribute.PosixFilePermissions;
import java.security.GeneralSecurityException;
import java.security.Security;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

public class PGPEngine implements EncryptEngine {

	private static final Logger log = LoggerFactory.getLogger(PGPEngine.class);

	static {
		if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
			Security.addProvider(new BouncyCastleProvider());
		}
	}

	private String secretKeyRing;
	private String pubKeyRing;
	private String secKeyRingPassword;
	private String userId;
	private String recipientId;

	private PGPHashAlgorithms hashAlgorithm = PGPHashAlgorithms.SHA_256;
	private PGPSymmetricEncryptionAlgorithms encryptionAlgorithm = PGPSymmetricEncryptionAlgorithms.AES_256;
	private PGPCompressionAlgorithms compressionAlgorithm = PGPCompressionAlgorithms.BZIP2;

	public void init(String secretKeyRing, String pubKeyRing, String secKeyRingPassword, String userId, String recipientId) {
		this.secretKeyRing = secretKeyRing;
		this.pubKeyRing = pubKeyRing;
		this.secKeyRingPassword = secKeyRingPassword;
		this.userId = userId;
		this.recipientId = recipientId;
	}

	public PGPEngine initHashAlg(Optional<String> hashAlgorithm) {
		this.hashAlgorithm = PGPHashAlgorithms.valueOf(hashAlgorithm.orElse("SHA_256"));
		return this;
	}

	public PGPEngine initEncryptAlg(Optional<String> encryptionAlgorithm) {
		this.encryptionAlgorithm = PGPSymmetricEncryptionAlgorithms.valueOf(encryptionAlgorithm.orElse("AES_256"));
		return this;
	}

	public PGPEngine initCompressionAlg(Optional<String> compressionAlgorithm) {
		this.compressionAlgorithm = PGPCompressionAlgorithms.valueOf(compressionAlgorithm.orElse("BZIP2"));
		return this;
	}

	public InputStream readEncrypted(InputStream streamToDecrypt) throws IOException {
		log.info("PGPEngine.readEncrypted - userId: {}, recipientId: {}", userId, recipientId);
		
		// If public key is empty, user wants decryption-only mode with system GPG
		if (pubKeyRing == null || pubKeyRing.trim().isEmpty()) {
			log.debug("Public key is empty - using decryption-only mode with system GPG 2.4+");
			return decryptWithSystemGPG(streamToDecrypt);
		}
		
		// Original BouncyCastle implementation for backward compatibility
		InputStream streamToReturn;
		try (
			InputStream secretKey = new ByteArrayInputStream(secretKeyRing.getBytes(StandardCharsets.UTF_8));
			InputStream publicKey = new ByteArrayInputStream(pubKeyRing.getBytes(StandardCharsets.UTF_8))
		) {
			try {
				KeyringConfig encryptionKeyring = KeyringConfigs.withKeyRingsFromStreams(publicKey,
					secretKey, KeyringConfigCallbacks.withPassword(secKeyRingPassword));

				streamToReturn = BouncyGPG
					.decryptAndVerifyStream()
					.withConfig(encryptionKeyring)
					.andIgnoreSignatures()
					.fromEncryptedInputStream(streamToDecrypt);
			} catch (GeneralSecurityException | PGPException e) {
				log.error("Can't decrypt stream", e);
				throw new IllegalArgumentException(e);
			}
		}
		return streamToReturn;
	}

	/**
	 * Decrypt using system GPG 2.4+ for decryption-only mode
	 */
	private InputStream decryptWithSystemGPG(InputStream streamToDecrypt) throws IOException {
		log.debug("Starting system GPG decryption");

		// Detect GPG version for compatibility adjustments
		String gpgVersion = detectGPGVersion();
		log.debug("Detected GPG version: {}", gpgVersion);

		// Create temporary directory for GPG home
		File tempGpgHome = new File(System.getProperty("java.io.tmpdir"), "gpg_home_" + System.currentTimeMillis());
		if (!tempGpgHome.mkdirs()) {
			throw new IOException("Failed to create temporary GPG home directory: " + tempGpgHome.getAbsolutePath());
		}

		// Set proper permissions on the GPG home directory (700 - rwx------)
		try {
			Set<PosixFilePermission> permissions = PosixFilePermissions.fromString("rwx------");
			Files.setPosixFilePermissions(tempGpgHome.toPath(), permissions);
			log.debug("Set POSIX permissions 700 on GPG home directory");
		} catch (Exception e) {
			log.debug("POSIX permissions not supported, using Java File API");
			tempGpgHome.setReadable(false, false);
			tempGpgHome.setWritable(false, false);
			tempGpgHome.setExecutable(false, false);
			tempGpgHome.setReadable(true, true);
			tempGpgHome.setWritable(true, true);
			tempGpgHome.setExecutable(true, true);
		}

		File tempInput = File.createTempFile("pgp_input_", ".gpg");
		File tempOutput = File.createTempFile("pgp_output_", ".dec");
		File tempKey = File.createTempFile("pgp_key_", ".asc");

		log.debug("Created temporary files: input={}, output={}, key={}, gpg_home={}",
			tempInput.getAbsolutePath(), tempOutput.getAbsolutePath(), tempKey.getAbsolutePath(), tempGpgHome.getAbsolutePath());

		try {
			// Write input stream to temp file using IOUtils for binary data integrity
			log.debug("Writing encrypted data to temporary file");
			try (FileOutputStream fos = new FileOutputStream(tempInput)) {
				long totalBytes = IOUtils.copyLarge(streamToDecrypt, fos);
				fos.flush(); // Ensure all data is written
				fos.getFD().sync(); // Force OS to flush to disk before GPG reads
				log.debug("Wrote {} bytes to temporary input file", totalBytes);
			}

			// Write private key to temp file
			log.debug("Writing private key to temporary file");
			try (FileOutputStream fos = new FileOutputStream(tempKey)) {
				fos.write(secretKeyRing.getBytes(StandardCharsets.UTF_8));
			}
			log.debug("Private key written to temporary file");

			// Import key and decrypt using GPG with temporary home directory
			log.debug("Importing private key using GPG");
			ProcessBuilder pb = new ProcessBuilder(
					"gpg", "-vvv","--debug-all", "--batch", "--yes", "--pinentry-mode", "loopback",
					"--homedir", tempGpgHome.getAbsolutePath(),
					"--passphrase", secKeyRingPassword,
					"--import", tempKey.getAbsolutePath());

			log.debug("PB command: {}", pb.command());

			// Clear environment variables that might interfere with GPG
			pb.environment().remove("GNUPGHOME");
			pb.environment().remove("GPG_AGENT_INFO");

			Process process = pb.start();
			int importResult = process.waitFor();

			if (importResult != 0) {
				log.warn("GPG key import failed with exit code: {}, trying without import", importResult);
			} else {
				log.debug("GPG key import successful");
			}

			// Try multiple decryption strategies for maximum compatibility
			boolean decryptionSuccessful = false;
			String lastErrorMessage = "";

			// Strategy 1: Modern GPG with enhanced compatibility flags
			log.debug("Attempting decryption with enhanced compatibility flags");
			decryptionSuccessful = attemptDecryption(tempGpgHome, tempInput, tempOutput,
				buildModernGPGCommand(tempGpgHome, tempInput, tempOutput), "modern");

			if (!decryptionSuccessful) {
				// Strategy 2: Legacy GPG mode for older file formats
				log.debug("Modern decryption failed, trying legacy compatibility mode");
				decryptionSuccessful = attemptDecryption(tempGpgHome, tempInput, tempOutput,
					buildLegacyGPGCommand(tempGpgHome, tempInput, tempOutput), "legacy");
			}

			if (!decryptionSuccessful) {
				// Strategy 3: Minimal GPG flags for maximum compatibility
				log.debug("Legacy decryption failed, trying minimal compatibility mode");
				decryptionSuccessful = attemptDecryption(tempGpgHome, tempInput, tempOutput,
					buildMinimalGPGCommand(tempGpgHome, tempInput, tempOutput), "minimal");
			}

			if (!decryptionSuccessful) {
				log.error("All GPG decryption strategies failed for file");
				throw new IOException("GPG decryption failed with all attempted strategies. Last error: " + lastErrorMessage);
			}

			// Final validation that decryption actually produced output
			if (!tempOutput.exists() || tempOutput.length() == 0) {
				log.error("GPG decryption process completed but produced no output file or empty file");
				throw new IOException("GPG decryption produced no valid output");
			}

			log.debug("GPG decryption successful, output file size: {} bytes", tempOutput.length());

			// Return the decrypted content as input stream
			return new FileInputStream(tempOutput) {
				@Override
				public void close() throws IOException {
					super.close();
					// Clean up temp files
					log.debug("Cleaning up temporary files");
					tempInput.delete();
					tempOutput.delete();
					tempKey.delete();
					// Clean up GPG home directory
					deleteDirectory(tempGpgHome);
				}
			};

		} catch (InterruptedException e) {
			// Handle thread interruption
			Thread.currentThread().interrupt();
			log.debug("Cleaning up temporary files due to interruption");
			tempInput.delete();
			tempOutput.delete();
			tempKey.delete();
			deleteDirectory(tempGpgHome);
			throw new IOException("GPG decryption was interrupted", e);
		} catch (Exception e) {
			// Clean up temp files on error
			log.debug("Cleaning up temporary files due to error");
			tempInput.delete();
			tempOutput.delete();
			tempKey.delete();
			deleteDirectory(tempGpgHome);
			throw new IOException("GPG decryption failed: " + e.getMessage(), e);
		}
	}

	/**
	 * Detect GPG version for compatibility adjustments
	 */
	private String detectGPGVersion() {
		try {
			ProcessBuilder pb = new ProcessBuilder("gpg", "--version");
			Process process = pb.start();

			try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
				String line = reader.readLine();
				if (line != null && line.contains("gpg (GnuPG)")) {
					// Extract version number from line like "gpg (GnuPG) 2.4.3"
					String[] parts = line.split("\\s+");
					for (String part : parts) {
						if (part.matches("\\d+\\.\\d+.*")) {
							return part;
						}
					}
				}
			}

			process.waitFor(5, TimeUnit.SECONDS);
		} catch (Exception e) {
			log.debug("Failed to detect GPG version: {}", e.getMessage());
		}
		return "unknown";
	}

	/**
	 * Build modern GPG command with enhanced compatibility flags
	 */
	private ProcessBuilder buildModernGPGCommand(File tempGpgHome, File tempInput, File tempOutput) {
		ProcessBuilder pb = new ProcessBuilder(
			"gpg", "--batch", "--yes", "--pinentry-mode", "loopback",
			"--homedir", tempGpgHome.getAbsolutePath(),
			"--passphrase", secKeyRingPassword,
			"--output", tempOutput.getAbsolutePath(),
			"--trust-model", "always",           // Skip trust validation
			"--ignore-mdc-error",                // Ignore modification detection errors
			"--allow-non-selfsigned-uid",        // Allow non-self-signed UIDs
			"--disable-cipher-algo", "IDEA",     // Disable problematic legacy cipher
			"--disable-cipher-algo", "CAST5",    // Disable another problematic cipher
			"--ignore-time-conflict",            // Ignore timestamp conflicts
			"--ignore-valid-from",               // Ignore validity period
			"--debug-level", "basic",            // Add debug info for troubleshooting
			"--decrypt", tempInput.getAbsolutePath());

		// Clear environment variables that might interfere with GPG
		pb.environment().remove("GNUPGHOME");
		pb.environment().remove("GPG_AGENT_INFO");
		return pb;
	}

	/**
	 * Build legacy GPG command for older file formats
	 */
	private ProcessBuilder buildLegacyGPGCommand(File tempGpgHome, File tempInput, File tempOutput) {
		ProcessBuilder pb = new ProcessBuilder(
			"gpg", "--batch", "--yes", "--pinentry-mode", "loopback",
			"--homedir", tempGpgHome.getAbsolutePath(),
			"--passphrase", secKeyRingPassword,
			"--output", tempOutput.getAbsolutePath(),
			"--trust-model", "always",           // Skip trust validation
			"--ignore-mdc-error",                // Ignore modification detection errors
			"--allow-weak-digest-algos",         // Allow weak digest algorithms for legacy files
			"--allow-old-cipher-algos",          // Allow old cipher algorithms
			"--force-mdc",                       // Force modification detection code
			"--no-auto-key-retrieve",            // Don't try to retrieve keys automatically
			"--decrypt", tempInput.getAbsolutePath());

		// Clear environment variables that might interfere with GPG
		pb.environment().remove("GNUPGHOME");
		pb.environment().remove("GPG_AGENT_INFO");
		return pb;
	}

	/**
	 * Build minimal GPG command for maximum compatibility
	 */
	private ProcessBuilder buildMinimalGPGCommand(File tempGpgHome, File tempInput, File tempOutput) {
		ProcessBuilder pb = new ProcessBuilder(
			"gpg", "--batch", "--yes", "--pinentry-mode", "loopback",
			"--homedir", tempGpgHome.getAbsolutePath(),
			"--passphrase", secKeyRingPassword,
			"--output", tempOutput.getAbsolutePath(),
			"--trust-model", "always",           // Skip trust validation
			"--decrypt", tempInput.getAbsolutePath());

		// Clear environment variables that might interfere with GPG
		pb.environment().remove("GNUPGHOME");
		pb.environment().remove("GPG_AGENT_INFO");
		return pb;
	}

	/**
	 * Attempt GPG decryption with a specific command strategy
	 */
	private boolean attemptDecryption(File tempGpgHome, File tempInput, File tempOutput, ProcessBuilder pb, String strategy) {
		try {
			log.debug("Starting GPG decryption process with {} strategy", strategy);
			Process process = pb.start();

			// Wait for process with timeout to avoid hanging indefinitely
			boolean processCompleted = process.waitFor(30, TimeUnit.SECONDS);
			int decryptResult = processCompleted ? process.exitValue() : -1;

			// Check if output file was created successfully regardless of exit code
			boolean outputFileExists = tempOutput.exists() && tempOutput.length() > 0;

			if (!processCompleted) {
				log.warn("GPG process ({} strategy) did not complete within 30 seconds, terminating process", strategy);
				process.destroyForcibly();
				if (outputFileExists) {
					log.info("GPG process timed out but output file was created successfully with {} strategy", strategy);
					return true;
				}
				log.debug("{} strategy failed: process timeout with no output", strategy);
				return false;
			} else if (decryptResult != 0) {
				// Read both stderr and stdout for comprehensive error info
				StringBuilder errorOutput = new StringBuilder();
				StringBuilder standardOutput = new StringBuilder();

				try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
				     BufferedReader outputReader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {

					String line;
					while ((line = errorReader.readLine()) != null) {
						errorOutput.append(line).append("\n");
					}
					while ((line = outputReader.readLine()) != null) {
						standardOutput.append(line).append("\n");
					}
				}

				String errorMessage = errorOutput.toString();
				String outputMessage = standardOutput.toString();

				// If output file exists and has content, treat as successful despite non-zero exit code
				if (outputFileExists) {
					log.warn("GPG decryption ({} strategy) completed with exit code {} but output file was created successfully", strategy, decryptResult);
					log.debug("GPG stderr ({}): {}", strategy, errorMessage);
					if (!outputMessage.trim().isEmpty()) {
						log.debug("GPG stdout ({}): {}", strategy, outputMessage);
					}

					// Log specific guidance for common warnings that don't prevent successful decryption
					if (errorMessage.contains("partial length invalid for packet type")) {
						log.info("GPG reported 'partial length invalid for packet type' with {} strategy but decryption output was produced - this may be a signature verification issue that doesn't affect data integrity", strategy);
					}

					log.info("Proceeding with successful decryption using {} strategy despite GPG warnings", strategy);
					return true;
				} else {
					// No output file created, this is a failure for this strategy
					log.debug("GPG decryption ({} strategy) failed with exit code {} and no output file was created", strategy, decryptResult);
					log.debug("GPG stderr ({}): {}", strategy, errorMessage);
					if (!outputMessage.trim().isEmpty()) {
						log.debug("GPG stdout ({}): {}", strategy, outputMessage);
					}

					// Provide specific guidance for common errors
					if (errorMessage.contains("partial length invalid for packet type")) {
						log.debug("{} strategy failed: partial length invalid for packet type - trying next strategy", strategy);
					} else if (errorMessage.contains("bad passphrase")) {
						log.error("Incorrect passphrase provided for GPG key - this will likely fail for all strategies");
						return false; // No point trying other strategies if passphrase is wrong
					} else if (errorMessage.contains("no valid OpenPGP data found")) {
						log.error("File does not contain valid GPG encrypted data - this will likely fail for all strategies");
						return false; // No point trying other strategies if file is not GPG encrypted
					}

					return false;
				}
			} else {
				// Success with exit code 0
				if (outputFileExists) {
					log.info("GPG decryption successful with {} strategy", strategy);
					return true;
				} else {
					log.warn("GPG process ({} strategy) completed with exit code 0 but produced no output file", strategy);
					return false;
				}
			}

		} catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			log.debug("GPG decryption ({} strategy) was interrupted", strategy);
			return false;
		} catch (Exception e) {
			log.debug("GPG decryption ({} strategy) failed with exception: {}", strategy, e.getMessage());
			return false;
		}
	}

	/**
	 * Recursively delete a directory and its contents
	 */
	private void deleteDirectory(File directory) {
		if (directory.exists()) {
			File[] files = directory.listFiles();
			if (files != null) {
				for (File file : files) {
					if (file.isDirectory()) {
						deleteDirectory(file);
					} else {
						file.delete();
					}
				}
			}
			directory.delete();
		}
	}

	public ByteArrayOutputStream encrypt(InputStream streamToEncrypt) throws IOException {
		log.info("PGPEngine.encrypt - userId: {}, recipientId: {}", userId, recipientId);
		
		// Check if public key is available for encryption
		if (pubKeyRing == null || pubKeyRing.trim().isEmpty()) {
			log.error("Public key is required for encryption operations. This configuration is for decryption-only mode.");
			throw new IllegalArgumentException("Public key is required for encryption operations. This configuration is for decryption-only mode.");
		}
		
		ByteArrayOutputStream streamToReturn;
		try (
			InputStream secretKey = new ByteArrayInputStream(secretKeyRing.getBytes(StandardCharsets.UTF_8));
			InputStream publicKey = new ByteArrayInputStream(pubKeyRing.getBytes(StandardCharsets.UTF_8))
		) {
			try {
				KeyringConfig encryptionKeyring = KeyringConfigs.withKeyRingsFromStreams(publicKey,
					secretKey, KeyringConfigCallbacks.withPassword(secKeyRingPassword));
				streamToReturn = new ByteArrayOutputStream();
				OutputStream encryptionStream = BouncyGPG
					.encryptToStream()
					.withConfig(encryptionKeyring)
					.withAlgorithms(new PGPAlgorithmSuite(hashAlgorithm, encryptionAlgorithm, compressionAlgorithm))
					.toRecipient(recipientId)
					.andDoNotSign()
					.binaryOutput()
					.andWriteTo(streamToReturn);

				IOUtils.copyLarge(streamToEncrypt, encryptionStream);
				encryptionStream.close();
			} catch (PGPException | IOException | GeneralSecurityException e) {
				log.error("Can't encrypt stream", e);
				throw new IllegalArgumentException(e);
			}
		}
		return streamToReturn;
	}

}
