package com.nexla.parser;

import name.neuhalfen.projects.crypto.bouncycastle.openpgp.BouncyGPG;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.algorithms.PGPAlgorithmSuite;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.algorithms.PGPCompressionAlgorithms;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.algorithms.PGPHashAlgorithms;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.algorithms.PGPSymmetricEncryptionAlgorithms;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.keys.callbacks.KeyringConfigCallbacks;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.keys.keyrings.KeyringConfig;
import name.neuhalfen.projects.crypto.bouncycastle.openpgp.keys.keyrings.KeyringConfigs;
import org.apache.commons.io.IOUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openpgp.PGPException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.Security;
import java.util.Optional;

public class PGPEngine implements EncryptEngine {

	private static final Logger log = LoggerFactory.getLogger(PGPEngine.class);

	static {
		if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
			Security.addProvider(new BouncyCastleProvider());
		}
	}

	private String secretKeyRing;
	private String pubKeyRing;
	private String secKeyRingPassword;
	private String userId;
	private String recipientId;

	private PGPHashAlgorithms hashAlgorithm = PGPHashAlgorithms.SHA_256;
	private PGPSymmetricEncryptionAlgorithms encryptionAlgorithm = PGPSymmetricEncryptionAlgorithms.AES_256;
	private PGPCompressionAlgorithms compressionAlgorithm = PGPCompressionAlgorithms.BZIP2;

	public void init(String secretKeyRing, String pubKeyRing, String secKeyRingPassword, String userId, String recipientId) {
		this.secretKeyRing = secretKeyRing;
		this.pubKeyRing = pubKeyRing;
		this.secKeyRingPassword = secKeyRingPassword;
		this.userId = userId;
		this.recipientId = recipientId;
	}

	public PGPEngine initHashAlg(Optional<String> hashAlgorithm) {
		this.hashAlgorithm = PGPHashAlgorithms.valueOf(hashAlgorithm.orElse("SHA_256"));
		return this;
	}

	public PGPEngine initEncryptAlg(Optional<String> encryptionAlgorithm) {
		this.encryptionAlgorithm = PGPSymmetricEncryptionAlgorithms.valueOf(encryptionAlgorithm.orElse("AES_256"));
		return this;
	}

	public PGPEngine initCompressionAlg(Optional<String> compressionAlgorithm) {
		this.compressionAlgorithm = PGPCompressionAlgorithms.valueOf(compressionAlgorithm.orElse("BZIP2"));
		return this;
	}

	public InputStream readEncrypted(InputStream streamToDecrypt) throws IOException {
		log.info("PGPEngine.readEncrypted - userId: {}, recipientId: {}", userId, recipientId);
		InputStream streamToReturn;
		try (
			InputStream secretKey = new ByteArrayInputStream(secretKeyRing.getBytes(StandardCharsets.UTF_8));
			InputStream publicKey = new ByteArrayInputStream(pubKeyRing.getBytes(StandardCharsets.UTF_8))
		) {
			try {
				KeyringConfig encryptionKeyring = KeyringConfigs.withKeyRingsFromStreams(publicKey,
					secretKey, KeyringConfigCallbacks.withPassword(secKeyRingPassword));

				streamToReturn = BouncyGPG
					.decryptAndVerifyStream()
					.withConfig(encryptionKeyring)
					.andIgnoreSignatures()
					.fromEncryptedInputStream(streamToDecrypt);
			} catch (GeneralSecurityException | PGPException e) {
				log.error("Can't encrypt stream", e);
				throw new IllegalArgumentException(e);
			}
		}
		return streamToReturn;
	}

	public ByteArrayOutputStream encrypt(InputStream streamToEncrypt) throws IOException {
		log.info("PGPEngine.encrypt - userId: {}, recipientId: {}", userId, recipientId);
		ByteArrayOutputStream streamToReturn;
		try (
			InputStream secretKey = new ByteArrayInputStream(secretKeyRing.getBytes(StandardCharsets.UTF_8));
			InputStream publicKey = new ByteArrayInputStream(pubKeyRing.getBytes(StandardCharsets.UTF_8))
		) {
			try {
				KeyringConfig encryptionKeyring = KeyringConfigs.withKeyRingsFromStreams(publicKey,
					secretKey, KeyringConfigCallbacks.withPassword(secKeyRingPassword));
				streamToReturn = new ByteArrayOutputStream();
				OutputStream encryptionStream = BouncyGPG
					.encryptToStream()
					.withConfig(encryptionKeyring)
					.withAlgorithms(new PGPAlgorithmSuite(hashAlgorithm, encryptionAlgorithm, compressionAlgorithm))
					.toRecipient(recipientId)
					.andDoNotSign()
					.binaryOutput()
					.andWriteTo(streamToReturn);

				IOUtils.copyLarge(streamToEncrypt, encryptionStream);
				encryptionStream.close();
			} catch (PGPException | IOException | GeneralSecurityException e) {
				log.error("Can't encrypt stream", e);
				throw new IllegalArgumentException(e);
			}
		}
		return streamToReturn;
	}

}
