package com.nexla.connector.rest.source;

import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.google.common.collect.Maps;
import com.nexla.connector.config.rest.HttpCallParameters;
import com.nexla.connector.config.rest.RestAuthConfig;
import com.nexla.connector.config.rest.RestHeaders;
import com.nexla.probe.http.BaseRequestSender;
import com.nexla.test.UnitTests;
import lombok.SneakyThrows;
import org.apache.http.conn.DnsResolver;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.ssl.SSLContexts;
import org.junit.Before;
import org.junit.ClassRule;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import wiremock.com.google.common.io.Resources;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;

@Category(UnitTests.class)
public class BaseRequestSenderTest {

	@ClassRule
	public static WireMockRule wireMockServer = new WireMockRule(
		WireMockConfiguration
			.wireMockConfig()
			.httpDisabled(true)
			.dynamicHttpsPort()
			.enableBrowserProxying(true)
			.keystorePath(Resources.getResource("test_localhost_wildcard.jks").toString())
			.keystorePassword("password")
			.keyManagerPassword("password")
	);

	@Before
	public void onBefore() {
		wireMockServer.resetAll();
	}

	@Test
	@SneakyThrows
	public void wildcardSslVerifiedForSubdomainWithDot() {
		stubFor(
				get(urlEqualTo("/test")) //
						.withHost(equalTo("subdomain.nexla.com.s3.localhost"))
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody("{\"status\": \"ok\"}")
						)
		);

		String url = "https://subdomain.nexla.com.s3.localhost:" + wireMockServer.httpsPort() + "/test";

		var configMap = new HashMap<String, String>() {{
			put("cred_type", "rest");
			put("auth.type", "AWS_SIGNATURE");
			put("ignore.ssl.cert.validation", "false");
			put("test.method", "GET");

			put("test.content.type", "application/json");
			put("jwt.enabled", "false");
			put("skip.validation", "false");
			put("hmac.enabled", "false");

			put("ui.cert_signed", "false");
			put("aws.region", "us-west-1");
			put("aws.access.key", "AA");
			put("aws.service", "s3");
			put("test.url", url);
			put("aws.secret.key", "BB");
			put("credentials_type", "rest");
		}};

		var dnsResolver = new DnsResolver() {
			@Override
			public InetAddress[] resolve(String host) throws UnknownHostException {
				// Resolve all requests to localhost (including fake subdomains)
				InetAddress [] inetAddress = new InetAddress[]{ InetAddress.getLocalHost() };
				return inetAddress;
			}
		};

		var sslContext = SSLContexts.custom()
				.loadTrustMaterial(new TrustSelfSignedStrategy())
				.build();

		var authConfig = new RestAuthConfig(configMap, 1);
		var sender = new BaseRequestSender(new RestAuthConfig(Maps.newHashMap(), 123), null, false, false, sslContext, dnsResolver, null);

		RestHeaders headers = new RestHeaders(Collections.emptyMap(), ofNullable(authConfig.testContentType), authConfig.testAcceptHeader);
		HttpCallParameters cp = new HttpCallParameters(authConfig.testUrl, authConfig.skipUrlEncoding, authConfig.testMethod, authConfig.testBody, empty(), headers);

		sender.auth(cp); // expect no exceptions
	}
}