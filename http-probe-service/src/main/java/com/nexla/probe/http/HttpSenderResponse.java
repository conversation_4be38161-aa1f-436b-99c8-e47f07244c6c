package com.nexla.probe.http;

import lombok.Data;
import org.springframework.http.HttpHeaders;

import java.util.Optional;

@Data
public class HttpSenderResponse {

	private final Optional<byte[]> body;
	private final HttpHeaders headers;
	private final Integer statusCode;
	private final Optional<Throwable> exception;

	public HttpSenderResponse(Optional<byte[]> body, HttpHeaders headers, Integer statusCode) {
		this.body = body;
		this.headers = headers;
		this.statusCode = statusCode;
		this.exception = Optional.empty();
	}

	public HttpSenderResponse(Optional<byte[]> body, HttpHeaders headers, Integer statusCode, Optional<Throwable> exception) {
		this.body = body;
		this.headers = headers;
		this.statusCode = statusCode;
		this.exception = exception;
	}
}
