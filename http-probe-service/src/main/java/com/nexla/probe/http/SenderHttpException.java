package com.nexla.probe.http;

import lombok.Getter;
import org.springframework.http.HttpHeaders;
import org.springframework.web.client.HttpClientErrorException;

import java.nio.charset.StandardCharsets;

@Getter
public class SenderHttpException extends HttpClientErrorException {

	private final String url;
	private final HttpHeaders headers;

	public SenderHttpException(String url, HttpClientErrorException e) {
		super(e.getStatusCode(), e.getStatusText(), e.getResponseHeaders(), e.getResponseBodyAsString().getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
		this.headers = e.getResponseHeaders();
		this.url = url;
	}

}
