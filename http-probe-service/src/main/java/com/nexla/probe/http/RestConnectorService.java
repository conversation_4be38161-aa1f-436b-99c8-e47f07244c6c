package com.nexla.probe.http;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.client.ScriptEvalClient;
import com.nexla.common.NexlaBucket;
import com.nexla.common.NexlaFile;
import com.nexla.common.ResourceType;
import com.nexla.common.io.SourceAwareInputStream;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.parse.SampleContentType;
import com.nexla.common.pool.SimplePool;
import com.nexla.common.probe.ProbeSampleResult;
import com.nexla.common.probe.ProbeSampleResultEntry;
import com.nexla.common.probe.StringSampleResult;
import com.nexla.connector.ConnectorService;
import com.nexla.connector.config.rest.*;
import com.nexla.parser.JsonParser;
import com.nexla.parser.xml.AdditionalPathsParser;
import com.nexla.parser.xml.XmlParser;
import com.nexla.rest.RestIteration;
import com.nexla.rest.RestIterationChainBuilder;
import com.nexla.rest.RestIterationContext;
import com.nexla.rest.pojo.RestIterationResult;
import com.nexla.rest.pojo.ResultEntry;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.AbstractConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestClientResponseException;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static com.nexla.admin.client.oauth2.NexlaTokenProvider.sameToken;
import static com.nexla.common.StreamUtils.OBJECT_MAPPER;
import static com.nexla.common.StreamUtils.lhm;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.parse.ParserConfigs.CHARSET_DETECTION_CONFIDENCE_THRESHOLD;
import static com.nexla.common.parse.ParserConfigs.DETECTED_CHARSET;
import static com.nexla.common.parse.ParserConfigs.Json.*;
import static com.nexla.common.parse.ParserConfigs.Unstructured.MODE_ENTIRE_FILE;
import static com.nexla.common.parse.ParserConfigs.Xml.*;
import static com.nexla.connector.ConnectorService.AuthResponse.SUCCESS;
import static com.nexla.connector.ConnectorService.AuthResponse.authError;
import static com.nexla.probe.http.BaseRequestSender.createSender;
import static com.nexla.probe.http.BaseRequestSender.createSenderPool;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;
import static org.springframework.http.MediaType.*;

public class RestConnectorService extends ConnectorService<RestAuthConfig> {

    public static final List<MediaType> XML_TYPES = Lists.newArrayList(APPLICATION_XML, APPLICATION_XHTML_XML, TEXT_HTML, TEXT_XML);
    public static final List<MediaType> JSON_TYPES = Lists.newArrayList(APPLICATION_JSON, APPLICATION_JSON_UTF8);
    public static final List<MediaType> PLAIN = Lists.newArrayList(TEXT_PLAIN);
    public static final String DEFAULT_FORMAT = "json";
    public static final String EMPTY_JSON_ARRAY = "[]";
    public static final int MAX_ERRORS_TO_LOG = 5;

    private final ScriptEvalClient scriptEvalClient;
    private final Optional<AdminApiClient> adminApiClient;
    private Logger logger;

    public RestConnectorService(ScriptEvalClient scriptEvalClient) {
        this.scriptEvalClient = scriptEvalClient;
        this.logger = LoggerFactory.getLogger(RestConnectorService.class);
        this.adminApiClient = Optional.empty();
    }

    public RestConnectorService(ScriptEvalClient scriptEvalClient, Optional<AdminApiClient> adminApiClient) {
        this.scriptEvalClient = scriptEvalClient;
        this.logger = LoggerFactory.getLogger(RestConnectorService.class);
        this.adminApiClient = adminApiClient;
    }

    @Override
    public void initLogger(ResourceType resourceType, Integer resourceId, Optional<Integer> taskId) {
        this.logger = new NexlaLogger(logger, new NexlaLogKey(resourceType, resourceId, taskId));
    }

    @Override
    public AuthResponse authenticate(RestAuthConfig auth) {
        try {
            if (!auth.skipValidation) {
                RestHeaders headers = new RestHeaders(Collections.emptyMap(), ofNullable(auth.testContentType), auth.testAcceptHeader);
                HttpCallParameters cp = new HttpCallParameters(auth.testUrl, auth.skipUrlEncoding, auth.testMethod, auth.testBody, empty(), headers);

                createSender(auth, sameToken(), scriptEvalClient, false, auth.skipUrlEncoding, empty())
                        .auth(cp);
            }
            return SUCCESS;
        } catch (RestClientResponseException e) {
            logger.error("[creds-{}] Exception while authenticating", auth.getCredsId(), e);
            return authError(e, Optional.ofNullable(e.getResponseBodyAsString()));
        } catch (Exception e) {
            logger.error("[creds-{}] Exception while authenticating", auth.getCredsId(), e);
            return authError(e);
        }
    }

    public static boolean isMimeType(List<MediaType> types, MediaType contentType) {
        return types.stream().anyMatch(x -> x.isCompatibleWith(contentType));
    }

    @SneakyThrows
    public static StreamEx<LinkedHashMap<String, Object>> readData(
            String responseFormat,
            String responseDataPath,
            Optional<String> additionalDataPath,
            Integer charsetDetectionThreshold,
            Optional<String> detectedCharset,
            byte[] responseBytes
    ) {
        String additionalPaths = additionalDataPath.orElse(EMPTY_JSON_ARRAY);
        switch (responseFormat) {
            case "json":
                return new AdditionalPathsParser(new JsonParser())
                        .option(JSON_PATH, responseDataPath)
                        .option(ADDITIONAL_JSON_PATHS, additionalPaths)
                        .option(JSON_MODE, MODE_ENTIRE_FILE)
                        .option(CHARSET_DETECTION_CONFIDENCE_THRESHOLD, charsetDetectionThreshold.toString())
                        .option(DETECTED_CHARSET, detectedCharset.orElse(null))
                        .parseMessages(() ->
                                new SourceAwareInputStream<>(responseBytes, new ByteArrayInputStream(responseBytes)), true)
                        .map(nexlaMessage -> nexlaMessage.get().getRawMessage());
            case "xml":
                return new AdditionalPathsParser(new XmlParser())
                        .option(XML_XPATH, responseDataPath)
                        .option(ADDITIONAL_XML_XPATHS, additionalPaths)
                        .option(XML_MODE, MODE_ENTIRE_FILE)
                        .option(CHARSET_DETECTION_CONFIDENCE_THRESHOLD, charsetDetectionThreshold.toString())
                        .option(DETECTED_CHARSET, detectedCharset.orElse(null))
                        .parseMessages(() ->
                                new SourceAwareInputStream<>(responseBytes, new ByteArrayInputStream(responseBytes)), true)
                        .map(nexlaMessage -> nexlaMessage.get().getRawMessage());
            case "plain":
                return StreamEx.of(lhm("value", new String(responseBytes)));
            default:
                throw new IllegalArgumentException();
        }
    }

    @Override
    public StreamEx<NexlaBucket> listBuckets(AbstractConfig config) {
        return StreamEx.empty();
    }

    @Override
    public StreamEx<NexlaFile> listBucketContents(AbstractConfig config) {
        return StreamEx.empty();
    }

    @Override
    public boolean checkWriteAccess(AbstractConfig config) {
        return false;
    }

    @Override
    public ProbeSampleResult readSample(AbstractConfig c, boolean raw) {
        RestSourceConnectorConfig config = (RestSourceConnectorConfig) c;
        NexlaLogger logger = new NexlaLogger(this.logger, new NexlaLogKey(ResourceType.SOURCE, config.sourceId, Optional.empty()));

        RestIterationChainBuilder builder = new RestIterationChainBuilder(logger, false, empty(), adminApiClient,Optional.empty(),
                Optional.empty(), Optional.empty(), Optional.empty(), config, empty());
        List<RestIteration> restIterations = builder.buildIterations(config.restIterationConfigs);
        List<RestIterationContext> callerContexts = builder.buildContexts(restIterations, nowUTC());
        RestIterationContext lastContext = callerContexts.get(callerContexts.size() - 1);

        // when using sampling with a code container, pass the previous iteration step's output into the code container
        // todo support multi-step sampling more generally
        Stream<String> lines;
        if (config.restIterationConfigs.stream().anyMatch(f -> f.iterationType == IterationType.CODE_CONTAINER_ITERATION)) {
            Optional<RestIterationResult<?>> result = lastContext.iterateOverContext();
            lines = result.stream().flatMap(r -> r.getEntries().stream()
                    .map(ResultEntry::getDataMap)
                    .map(m -> {
                        try {
                            return OBJECT_MAPPER.writeValueAsString(m);
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }
                    }));
        } else {
            lines = lastContext.readFirst()
                    .map(this::getStringStream)
                    .orElse(Stream.empty());
        }

        List<ProbeSampleResultEntry<String>> dataStream = StreamEx
            .of(lines)
            .map(ProbeSampleResultEntry::new)
            .toList();

        return new StringSampleResult(dataStream, empty(), true);
    }

    @SneakyThrows
    private Stream<String> getStringStream(byte[] responseBytes) {
        return new BufferedReader(new InputStreamReader(new ByteArrayInputStream(responseBytes))).lines();
    }

    @Override
    public StreamEx<NexlaFile> listTopLevelBuckets(AbstractConfig config) {
        return StreamEx.of(new NexlaFile("api", 0L, "/", "", null, null, null));
    }

    public static String getResponseFormat(Optional<MediaType> contentType) {
        return contentType.map(ct -> {
            if (isMimeType(XML_TYPES, ct)) {
                return "xml";
            }
            if (isMimeType(JSON_TYPES, ct)) {
                return "json";
            }
            if (isMimeType(PLAIN, ct)) {
                return "plain";
            }
            return null;
        }).orElse(DEFAULT_FORMAT);
    }


    public static String getResponseFormat(SampleContentType sampleContentType) {
        switch (sampleContentType) {
            case TXT:
                return "plain";
            case JSON:
                return "json";
            case BINARY:
                return "binary";
            case XML:
                return "xml";
            default:
                return DEFAULT_FORMAT;
        }
    }


}