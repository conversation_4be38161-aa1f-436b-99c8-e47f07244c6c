package com.nexla.sinkcaller;

import com.bazaarvoice.jolt.JsonUtils;
import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.base.Supplier;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.pool.NexlaPool;
import com.nexla.common.time.VarUtils;
import com.nexla.common.transform.Flattener;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.rest.BatchResponseStrategy;
import com.nexla.connector.config.rest.HttpCallParameters;
import com.nexla.connector.config.rest.RestAuthConfig;
import com.nexla.connector.config.rest.RestHeaders;
import com.nexla.connector.config.rest.RestSinkBatchResponseMessageFilter;
import com.nexla.connector.config.rest.RestSinkCallerConfig;
import com.nexla.connector.config.rest.RestSinkMode;
import com.nexla.json.XML;
import com.nexla.parser.JsonParser;
import com.nexla.probe.http.HttpSenderResponse;
import com.nexla.probe.http.RequestSender;
import com.nexla.writer.XmlFileWriter;
import lombok.Getter;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.io.JsonStringEncoder;
import org.slf4j.Logger;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.util.UriUtils;
import scala.Function0;
import scala.compat.java8.OptionConverters;
import scala.io.Codec;
import scala.io.Source;
import scala.util.Either;
import scala.util.Try;

import java.io.File;
import java.io.InputStream;
import java.io.StringReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.google.common.base.Suppliers.memoize;
import static com.nexla.common.MetricUtils.calcBytes;
import static com.nexla.common.NexlaMetaData.NX_REQUEST_URL;
import static com.nexla.common.NexlaMetaData.NX_RESPONSE_HEADERS;
import static com.nexla.common.StreamUtils.toLinkedHashMap;
import static com.nexla.common.time.VarUtils.replaceVars;
import static com.nexla.connector.properties.RestConfigAccessor.VAR_MESSAGE_JSON;
import static com.nexla.probe.http.RestConnectorService.getResponseFormat;
import static com.nexla.probe.http.RestConnectorService.readData;
import static java.util.Collections.emptyMap;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static org.springframework.http.MediaType.APPLICATION_FORM_URLENCODED_VALUE;
import static org.springframework.http.MediaType.APPLICATION_XML_VALUE;

@Getter
public class RestSinkCaller {

	public static final String VAR_MESSAGE_JSON_QUOTED = "{" + VAR_MESSAGE_JSON + "}";
	public static final String PROPERTY_NAME = "attachment_name";
	public static final String PROPERTY_ATTACHMENT_URL = "attachment_url";
	public static final String RAW_MESSAGE = "raw_message";

	public final NexlaPool<RequestSender> senderPool;
	public final Optional<RestIngestionCallback> ingester;
	public final Logger logger;
	public final boolean logVerboseWithSinkBody;
	public final boolean logVerbose;
	public final Integer charsetDetectionThreshold;
	public final RestSinkCallerConfig config;
	public final JsonParser jsonParser;

	public RestSinkCaller(
		NexlaPool<RequestSender> senderPool,
		Optional<RestIngestionCallback> ingester,
		RestSinkCallerConfig config,
		Integer charsetDetectionThreshold,
		Logger logger,
		boolean logVerboseWithSinkBody,
		boolean logVerbose
	) {
		this.senderPool = senderPool;
		this.config = config;
		this.logger = logger;
		this.logVerboseWithSinkBody = logVerboseWithSinkBody;
		this.ingester = ingester;
		this.charsetDetectionThreshold = charsetDetectionThreshold;
		this.jsonParser = new JsonParser();
		this.logVerbose = logVerbose;
	}

	public RestSinkCaller(
		NexlaPool<RequestSender> senderPool,
		Optional<RestIngestionCallback> ingester,
		RestSinkCallerConfig config,
		Integer charsetDetectionThreshold,
		Logger logger,
		boolean logVerboseWithSinkBody
	) {
		this(senderPool, ingester, config, charsetDetectionThreshold, logger, logVerboseWithSinkBody, false);
	}

	public SingleRequestReadyIngestion createSingleDataReadyIngestion(
		String url,
		LinkedHashMap<String, Object> mappedRawMessage,
		NexlaMessage originalForIngest,
		RestSinkMode mode,
		RestHeaders restHeaders
	) {
		// var replacement is only for single message requests
		Supplier<LinkedHashMap<String, Object>> flattenedData = memoize(() -> Flattener.INSTANCE.flatten(mappedRawMessage));
		Supplier<Map<String, String>> flattenedAndEncodedData = memoize(() -> encode(flattenedData.get()));

		if (mode == RestSinkMode.FILE_UPLOAD) {
			return new SingleRequestReadyIngestion(originalForIngest, url, empty(), of(formBody(mappedRawMessage)), emptyMap(), restHeaders);
		} else {
			Optional<String> body = makeBody(flattenedData, flattenedAndEncodedData, config.contentType.orElse(null), mappedRawMessage);
			Map<String, Object> input = getInputMapFromSingleBodyRequest(body, mappedRawMessage);
			return new SingleRequestReadyIngestion(originalForIngest, url, body, empty(), input, restHeaders);
		}
	}

	public BatchRequestReadyIngestion createBatchDataReadyIngestion(
		List<NexlaMessageContext> messageList,
		String url,
		Optional<String> body,
		RestHeaders restHeaders
	) {
		List<Map<String, Object>> request = getInputMapFromBatchBodyRequest(body);
		return new BatchRequestReadyIngestion(messageList, url, body, request, restHeaders);
	}

	public Map<String, Object> getInputMapFromSingleBodyRequest(Optional<String> body,
																															LinkedHashMap<String, Object> mappedRawMessage) {
		return body.map(b ->
				tryOptional(() -> JsonUtils.jsonToMap(b))
					.orElseGet(() ->
						tryOptional(() -> XML.toJSONObject(new StringReader(b), true).toMap())
							.orElse(Map.of(RAW_MESSAGE, JsonUtils.toJsonString(mappedRawMessage)))))
			.orElse(emptyMap());
	}

	public List<Map<String, Object>> getInputMapFromBatchBodyRequest(Optional<String> body) {
		return body.map(b ->
				tryOptional(() -> {
					List<Object> jsonList = JsonUtils.jsonToList(b);
					return jsonList.stream()
						.filter(item -> item instanceof Map)
						.map(item -> (Map<String, Object>) item)
						.collect(Collectors.toList());
				}).orElse(new ArrayList<>())).orElse(new ArrayList<>());
	}

	@SneakyThrows
	private MultiValueMap<String, Object> formBody(LinkedHashMap<String, Object> rawMessage) {

		LinkedHashMap<String, Object> m = new LinkedHashMap<>(rawMessage);
		List<LinkedHashMap<String, Object>> attachments = (List<LinkedHashMap<String, Object>>) m.remove("attachments");

		Path tempDirectory = Files.createTempDirectory("rest-sink");
		MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

		config
			.attachmentsField
			.ifPresent(att -> addAttachments(attachments, tempDirectory, body, att));

		EntryStream.of(m).forKeyValue((key, value) -> {
			if (value instanceof List || value instanceof Map) {
				body.add(key, JsonUtils.toJsonString(value));
			} else {
				body.add(key, value);
			}
		});
		return body;
	}

	@SneakyThrows
	private void addAttachments(List<LinkedHashMap<String, Object>> attachments, Path tempDirectory, MultiValueMap<String, Object> body, String att) {
		for (LinkedHashMap<String, Object> at : attachments) {
			String fileName = at.get(PROPERTY_NAME).toString();
			String attachmentUrl = at.get(PROPERTY_ATTACHMENT_URL).toString();

			InputStream in = new URL(attachmentUrl).openStream();
			File file = new File(tempDirectory.toFile(), fileName);
			Files.copy(in, file.toPath(), StandardCopyOption.REPLACE_EXISTING);
			body.add(att, new FileSystemResource(file.getAbsolutePath()));
		}
	}

	public HttpSenderResponse makeSingleCallWithIngestion(SingleRequestReadyIngestion data, HttpResponseState varState) {
		// XXX: does URL encoding as expected.
		HttpCallParameters cp = new HttpCallParameters(data.url, false, config.method, data.body, data.formBody, data.headers);
		HttpSenderResponse response = senderPool.withPooledObject(sender -> makeCall(cp, sender));
		Optional<String> responseBody = response.getBody().isPresent() ? Optional.of(new String(response.getBody().get(), StandardCharsets.UTF_8)) : Optional.empty();

		if (response.getException().isEmpty()) {
			IngestFeedMessage ingestFeedMessage = new SingleIngestFeedMessage(data.original, data.request, getBodyMap(response));
			ingestFeedMessage.getNexlaMessageList().forEach(m -> addHeadersToMeta((NexlaMessage) m, data.url, response));
			ingester.ifPresent(ing -> ingestResponse(ingestFeedMessage, ing));
		}

		setVarState(varState, data.url, data.body, responseBody, response.getStatusCode(),
			response.getException().isPresent() ? new ArrayList<>() : List.of(data.original),
			response.getException().isPresent() ? List.of(new HttpResponseErrorState(response.getException().get(), data.original)) : new ArrayList<>());
		return response;
	}

	public HttpSenderResponse makeBatchCallWithIngestion(BatchRequestReadyIngestion data, HttpResponseState varState) {
		validateBatchCall();

		// XXX: does URL encoding as expected.
		HttpCallParameters cp = new HttpCallParameters(data.url, false, config.method, data.body, empty(), data.headers);
		HttpSenderResponse response = senderPool.withPooledObject(sender -> makeCall(cp, sender));
		boolean isFailed = response.getException().isPresent();

		if (isFailed) {
			handleFailedBatchResponse(data, varState, response);
		} else if (config.batchResponseStrategy.equals(BatchResponseStrategy.NONE) ) {
			handleSucceedBatchResponseWithStrategyNone(data, varState, response);
		} else if (config.batchResponseStrategy.equals(BatchResponseStrategy.ALL_OR_NOTHING)) {
			handleSucceedBatchResponseWithStrategyAllOrNothing(data, varState, response);
		} else if (config.batchResponseStrategy.equals(BatchResponseStrategy.BY_ORDERED_ITEMS)) {
			handleSucceedBatchResponseWithStrategyByOrderedItems(data, varState, response);
		} else if (config.batchResponseStrategy.equals(BatchResponseStrategy.BY_ITEM_WITH_ID)) {
			handleSucceedBatchResponseWithStrategyByItemWithId(data, varState, response);
		} else if (config.batchResponseStrategy.equals(BatchResponseStrategy.BY_ITEM_WITH_NO_ID)) {
			handleSucceedBatchResponseWithStrategyByItemWithNoId(data, varState, response);
		} else {
			throw new UnsupportedOperationException("Batch response strategy not found.");
		}

		return response;
	}

	private void validateBatchCall() {
		if (config.batchResponseStrategy.equals(BatchResponseStrategy.ALL_OR_NOTHING)) {
			validateBatchResponseParam(config.batchResponseStatusJsonPath, config.BATCH_RESPONSE_STATUS_JSON_PATH,
				config.batchResponseStrategy.getKey());
			validateBatchResponseParam(config.batchResponseFailureEnum, config.BATCH_RESPONSE_FAILURE_ENUM,
				config.batchResponseStrategy.getKey());
		} else if (config.batchResponseStrategy.equals(BatchResponseStrategy.BY_ORDERED_ITEMS)) {
			validateBatchResponseParam(config.batchResponseStatusJsonPath, config.BATCH_RESPONSE_STATUS_JSON_PATH,
				config.batchResponseStrategy.getKey());
			validateBatchResponseParam(config.batchResponseFailureEnum, config.BATCH_RESPONSE_FAILURE_ENUM,
				config.batchResponseStrategy.getKey());
		} else if (config.batchResponseStrategy.equals(BatchResponseStrategy.BY_ITEM_WITH_ID)) {
			validateBatchResponseParam(config.batchResponseFailedItemsJsonPath, config.BATCH_RESPONSE_FAILED_ITEMS_JSON_PATH,
				config.batchResponseStrategy.getKey());
			validateBatchResponseParam(config.batchResponseRequestIdField, config.BATCH_RESPONSE_REQUEST_ID_FIELD,
				config.batchResponseStrategy.getKey());
			validateBatchResponseParam(config.batchResponseResponseIdField, config.BATCH_RESPONSE_RESPONSE_ID_FIELD,
				config.batchResponseStrategy.getKey());
		} else if (config.batchResponseStrategy.equals(BatchResponseStrategy.BY_ITEM_WITH_NO_ID)) {
			validateBatchResponseParam(config.batchResponseErrorMessagesJsonPath, config.BATCH_RESPONSE_ERROR_MESSAGES_JSON_PATH,
				config.batchResponseStrategy.getKey());
			validateBatchResponseParam(config.batchResponseErrorMessageFilterList, config.BATCH_RESPONSE_ERROR_MESSAGE_FILTER,
				config.batchResponseStrategy.getKey());
		} else if (config.batchResponseStrategy.equals(BatchResponseStrategy.CODE)) {
			throw new UnsupportedOperationException("Batch response strategy CODE is not implemented.");
		}
	}

	private void handleSucceedBatchResponseWithStrategyByItemWithNoId(BatchRequestReadyIngestion data, HttpResponseState varState, HttpSenderResponse response) {
		logVerboseInfo("Handling succeed batch response with strategy 'By Item with no Id'.");
		Optional<String> responseBodyOpt = response.getBody().map(b -> new String(b, StandardCharsets.UTF_8));
		if (responseBodyOpt.isPresent()) {
			String responseBody = responseBodyOpt.get();
			String failuresJsonPath = config.batchResponseErrorMessagesJsonPath.get();
			List<RestSinkBatchResponseMessageFilter> messageFilterList = config.batchResponseErrorMessageFilterList;

			List<String> failedResponseErrorMessageList = jsonParser.findListByJsonPath(failuresJsonPath, responseBody)
				.stream().map(Object::toString).collect(toList());
			if (failedResponseErrorMessageList != null) {
				logVerboseInfo(String.format("Finding failed response items in response body. failuresJsonPath: %s. responseBody: %s. messageFilterList: %s. failedResponseErrorMessageList: %s",
					failuresJsonPath, responseBody, messageFilterList, failedResponseErrorMessageList));
				List<NexlaMessageContext> failedMessageList = new ArrayList<>();
				List<NexlaMessageContext> succeedMessageList = new ArrayList<>();

				Set<String> uniqueFailedResponseErrorMessageList = new HashSet<>(failedResponseErrorMessageList);
				Map<String, List<String>> originalValuesMapByRequestField = new HashMap<>();
				for (String uniqueFailedResponseErrorMessage : uniqueFailedResponseErrorMessageList) {
					for (RestSinkBatchResponseMessageFilter messageFilter : messageFilterList) {
						Pattern pattern = Pattern.compile(messageFilter.getMessageRegex());
						Matcher matcher = pattern.matcher(uniqueFailedResponseErrorMessage);
						String requestFilter = messageFilter.getMessageRequestField();
						if (matcher.find()) {
							List<String> originalValues = new ArrayList<>();
							if(originalValuesMapByRequestField.containsKey(requestFilter)) {
								originalValues = originalValuesMapByRequestField.get(requestFilter);
							}
							originalValues.add(matcher.group(1));
							originalValuesMapByRequestField.put(requestFilter, originalValues);
						}
					}
				}
				logVerboseInfo(String.format("Grouping failed original values by request field. originalValuesMapByRequestField: %s.",
					originalValuesMapByRequestField));

				for (String requestField : originalValuesMapByRequestField.keySet()) {
					if (!data.messageList.stream().allMatch(m -> m.original.getRawMessage().containsKey(requestField))) {
						throw new IllegalArgumentException(
							String.format("Request field was not found in original message. Field: %s.", requestField));
					}
				}

				for (NexlaMessageContext originalMessageContext : data.messageList) {
					if (originalValuesMapByRequestField.keySet().stream().anyMatch(filterField ->
						originalValuesMapByRequestField.get(filterField)
							.contains(originalMessageContext.original.getRawMessage().get(filterField).toString()))) {
						failedMessageList.add(originalMessageContext);
					} else {
						succeedMessageList.add(originalMessageContext);
					}
				}

				for (NexlaMessage originalMessage : succeedMessageList.stream().map(m -> m.original).collect(toList())) {
					IngestFeedMessage ingestFeedMessage = new BatchItemIngestFeedMessage(List.of(originalMessage), getBodyMap(response));
					ingester.ifPresent(restIngestionCallback -> ingestResponse(ingestFeedMessage, restIngestionCallback));
				}

				setVarState(varState, data.url, data.body, responseBodyOpt, response.getStatusCode(),
					succeedMessageList.stream().map(m -> m.original).collect(toList()),
					failedMessageList.stream().map(m -> new HttpResponseErrorState(new RuntimeException(
						String.format("Response failed using '%s' batch response strategy. failuresJsonPath: %s. messageFilterList: %s. responseBody: %s",
							config.batchResponseStrategy.getKey(),
							failuresJsonPath, messageFilterList, responseBody)), m.original)).collect(toList()));
				return;
			}
		}

		sendIndividualIngestionResponsesAndSetSucceedBatchResponse(data, varState, response);
	}

	private void handleSucceedBatchResponseWithStrategyByItemWithId(BatchRequestReadyIngestion data, HttpResponseState varState, HttpSenderResponse response) {
		logVerboseInfo("Handling succeed batch response with strategy 'By Item with Id'.");
		Optional<String> responseBodyOpt = response.getBody().map(b -> new String(b, StandardCharsets.UTF_8));
		if (responseBodyOpt.isPresent()) {
			String responseBody = responseBodyOpt.get();
			String failedItemsJsonPath = config.batchResponseFailedItemsJsonPath.get();
			String requestIdField = config.batchResponseRequestIdField.get();
			String responseIdField = config.batchResponseResponseIdField.get();

			List<Object> failedResponseItems = jsonParser.findListByJsonPath(failedItemsJsonPath, responseBody);
			if (failedResponseItems != null) {
				logVerboseInfo(String.format("Finding failed response items in response body. failedItemsJsonPath: %s. responseBody: %s. requestIdField: %s. responseIdField: %s. failedResponseItems: %s",
					failedItemsJsonPath, responseBody, requestIdField, responseIdField, failedResponseItems));
				List<NexlaMessageContext> failedMessageList = new ArrayList<>();
				List<NexlaMessageContext> succeedMessageList = new ArrayList<>();
				for (NexlaMessageContext originalMessageContext : data.messageList) {
					if (!originalMessageContext.original.getRawMessage().containsKey(requestIdField)) {
						throw new IllegalArgumentException(
							String.format("Request field was not found in original message. Field: %s. Message: %s.",
								requestIdField, originalMessageContext.original.getRawMessage()));
					}
					String originalMessageId = originalMessageContext.original.getRawMessage().get(requestIdField).toString();
					if (failedResponseItems.stream().anyMatch(i -> i instanceof Map && ((Map<String, Object>)i).containsKey(responseIdField)
						&& originalMessageId.equalsIgnoreCase(((Map<String, Object>)i).get(responseIdField).toString()))) {
						failedMessageList.add(originalMessageContext);
					} else {
						succeedMessageList.add(originalMessageContext);
					}
				}

				for (NexlaMessage originalMessage : succeedMessageList.stream().map(m -> m.original).collect(toList())) {
					IngestFeedMessage ingestFeedMessage = new BatchItemIngestFeedMessage(List.of(originalMessage), getBodyMap(response));
					ingester.ifPresent(restIngestionCallback -> ingestResponse(ingestFeedMessage, restIngestionCallback));
				}

				setVarState(varState, data.url, data.body, responseBodyOpt, response.getStatusCode(),
					succeedMessageList.stream().map(m -> m.original).collect(toList()),
					failedMessageList.stream().map(m -> new HttpResponseErrorState(new RuntimeException(
						String.format("Response failed using '%s' batch response strategy. failedItemsJsonPath: %s. requestIdField: %s. responseIdField: %s. responseBody: %s",
							config.batchResponseStrategy.getKey(),
							failedItemsJsonPath, requestIdField, responseIdField, responseBody)), m.original)).collect(toList()));
				return;
			}
		}

		sendIndividualIngestionResponsesAndSetSucceedBatchResponse(data, varState, response);
	}

	private void handleSucceedBatchResponseWithStrategyAllOrNothing(BatchRequestReadyIngestion data, HttpResponseState varState, HttpSenderResponse response) {
		logVerboseInfo("Handling succeed batch response with strategy 'All or Nothing'.");
		Optional<String> responseBodyOpt = response.getBody().map(b -> new String(b, StandardCharsets.UTF_8));
		if (responseBodyOpt.isPresent()) {
			String responseBody = responseBodyOpt.get();
			String statusJsonPath = config.batchResponseStatusJsonPath.get();
			List<String> failureEnum = config.batchResponseFailureEnum;
			StreamEx<Object> status = jsonParser.find(statusJsonPath, responseBody, true);
			logVerboseInfo(String.format("Finding status json path in response body. statusJsonPath: %s. responseBody: %s. failureEnum: %s",
				statusJsonPath, responseBody, failureEnum));
			if (status.anyMatch(s -> failureEnum.stream().anyMatch(failureEnumItem -> failureEnumItem.equalsIgnoreCase(s.toString())))) {
				logVerboseInfo(String.format("Failure found in response body. statusJsonPath: %s. responseBody: %s. failureEnum: %s",
					statusJsonPath, responseBody, failureEnum));
				setVarState(varState, data.url, data.body, responseBodyOpt, response.getStatusCode(),
					new ArrayList<>(),
					data.messageList.stream().map(m -> new HttpResponseErrorState(new RuntimeException(
						String.format("Response failed using '%s' batch response strategy. statusJsonPath: %s. failureEnum: %s. responseBody: %s",
							config.batchResponseStrategy.getKey(), statusJsonPath, failureEnum, responseBody)), m.original)).collect(toList()));
				return;
			}
		}

		sendIndividualIngestionResponsesAndSetSucceedBatchResponse(data, varState, response);
	}

	private void handleSucceedBatchResponseWithStrategyByOrderedItems(BatchRequestReadyIngestion data, HttpResponseState varState, HttpSenderResponse response) {
		logVerboseInfo("Handling succeed batch response with strategy 'By Ordered Items'.");
		Optional<String> responseBodyOpt = response.getBody().map(b -> new String(b, StandardCharsets.UTF_8));
		if (responseBodyOpt.isPresent()) {
			String responseBody = responseBodyOpt.get();
			String statusJsonPath = config.batchResponseStatusJsonPath.get();
			List<String> failureEnum = config.batchResponseFailureEnum;

			List<Map<String, Object>> responseItems;
			try {
				responseItems = JsonUtils.jsonToList(responseBody).stream().map(i -> (LinkedHashMap<String, Object>) i).collect(toList());
			} catch (Exception e) {
				String errorMessage = "Response can not be converted to an array. It is not allowed in 'By Ordered Items' strategy.";
				String errorMessageWithResponse = errorMessage + String.format("responseBody: %s", responseBody);
				logger.error(errorMessageWithResponse, e);
				throw new RuntimeException(errorMessage);
			}

			if (responseItems.isEmpty() || responseItems.size() != data.messageList.size()) {
				String errorMessage = "Size of response items is not equals to the size of request items. It is not allowed in 'By Ordered Items' strategy.";
				String errorMessageWithResponse = errorMessage + String.format("messageList.size: %d, responseBody: %s", data.messageList.size(), responseBody);
				logger.error(errorMessageWithResponse);
				throw new RuntimeException(errorMessage);
			}

			List<HttpResponseErrorState> httpResponseErrorStateList = new ArrayList<>();
			List<NexlaMessageContext> succeedMessageList = new ArrayList<>();

			for (int i = 0; i < responseItems.size(); i++) {
				NexlaMessageContext originalMessageContext = data.messageList.get(i);
				Map<String, Object> responseItem = responseItems.get(i);

				String responseItemJsonStr = JsonUtils.toJsonString(responseItem);
				StreamEx<Object> status = jsonParser.find(statusJsonPath, responseItemJsonStr, false);
				if (status.anyMatch(s -> failureEnum.stream().anyMatch(failureEnumItem -> failureEnumItem.equalsIgnoreCase(s.toString())))) {
					httpResponseErrorStateList.add(new HttpResponseErrorState(new RuntimeException(
						String.format("Response item failed using '%s' batch response strategy. statusJsonPath: %s. failureEnum: %s. responseItemBody: %s",
							config.batchResponseStrategy.getKey(),
							statusJsonPath, failureEnum, responseItemJsonStr)), originalMessageContext.original));
				} else {
					succeedMessageList.add(originalMessageContext);
					IngestFeedMessage ingestFeedMessage = new BatchItemIngestFeedMessage(List.of(originalMessageContext.original), responseItem);
					ingester.ifPresent(restIngestionCallback -> ingestResponse(ingestFeedMessage, restIngestionCallback));
				}
			}

			setVarState(varState, data.url, data.body, responseBodyOpt, response.getStatusCode(),
				succeedMessageList.stream().map(m -> m.original).collect(toList()),
				httpResponseErrorStateList);
			return;
		}

		sendIndividualIngestionResponsesAndSetSucceedBatchResponse(data, varState, response);
	}

	private void sendIndividualIngestionResponsesAndSetSucceedBatchResponse(BatchRequestReadyIngestion data,
																																					HttpResponseState varState,
																																					HttpSenderResponse response) {
		logVerboseInfo("Batch response is succeed.");
		Optional<String> responseBodyOpt = response.getBody().map(b -> new String(b, StandardCharsets.UTF_8));
		for (NexlaMessage originalMessage : data.messageList.stream().map(m -> m.original).collect(toList())) {
			IngestFeedMessage ingestFeedMessage = new BatchItemIngestFeedMessage(List.of(originalMessage), getBodyMap(response));
			ingester.ifPresent(restIngestionCallback -> ingestResponse(ingestFeedMessage, restIngestionCallback));
		}

		setVarState(varState, data.url, data.body, responseBodyOpt, response.getStatusCode(),
			data.messageList.stream().map(m -> m.original).collect(toList()),
			new ArrayList<>());
	}

	private void logVerboseInfo(String info) {
		if (logVerbose) {
			logger.info(info);
		}
	}

	private void handleFailedBatchResponse(BatchRequestReadyIngestion data, HttpResponseState varState, HttpSenderResponse response) {
		logVerboseInfo("Handling failed batch response.");
		Optional<String> responseBody = response.getBody().map(b -> new String(b, StandardCharsets.UTF_8));
		setVarState(varState, data.url, data.body, responseBody, response.getStatusCode(),
			new ArrayList<>(),
			data.messageList.stream().map(m ->
				new HttpResponseErrorState(response.getException().get(), m.original)).collect(toList()));
	}

	private void handleSucceedBatchResponseWithStrategyNone(BatchRequestReadyIngestion data, HttpResponseState varState, HttpSenderResponse response) {
		logVerboseInfo("Handling succeed batch response with strategy none.");
		Optional<String> responseBody = response.getBody().map(b -> new String(b, StandardCharsets.UTF_8));

		IngestFeedMessage ingestFeedMessage = new BatchIngestFeedMessage(
			data.messageList.stream().map(m -> m.original).collect(toList()), getBodyMap(response));
		ingester.ifPresent(restIngestionCallback -> ingestResponse(ingestFeedMessage, restIngestionCallback));

		setVarState(varState, data.url, data.body, responseBody, response.getStatusCode(),
			data.messageList.stream().map(m -> m.original).collect(toList()),
			new ArrayList<>());
	}

	private void validateBatchResponseParam(Optional<?> paramValue, String paramKey, String batchResponseStrategy) {
		if (paramValue.isEmpty()) {
			throw new IllegalArgumentException(
				String.format("Param '%s' is required when batch response strategy is equals to '%s'.",
					paramKey, batchResponseStrategy));
		}
	}

	private void validateBatchResponseParam(List listValue, String paramKey, String batchResponseStrategy) {
		if (listValue.isEmpty()) {
			throw new IllegalArgumentException(
				String.format("Param '%s' can not be empty when batch response strategy is equals to '%s'.",
					paramKey, batchResponseStrategy));
		}
	}

	public void setVarState(HttpResponseState varState, String url,
													Optional<String> body, Optional<String> responseBody, Integer statusCode,
													List<NexlaMessage> succeedMessageList, List<HttpResponseErrorState> failedMessageList) {
		varState.setUrl(url);
		varState.setBody(body);
		varState.setResponse(responseBody);
		varState.setStatusCode(statusCode);

		varState.setSentBytes(succeedMessageList.stream().map(m -> calcBytes(m.toJsonString())).mapToInt(m -> m).sum());
		varState.setSentRecords(succeedMessageList.size());
		varState.setErrorRecords(failedMessageList.size());
		varState.getErrorList().addAll(failedMessageList);
	}

	public static <T> Optional<T> tryOptional(Function0<T> mapFunction0) {
		return OptionConverters.toJava(Try.apply(mapFunction0).toOption());
	}

	@SneakyThrows
	public HttpSenderResponse makeCall(
		HttpCallParameters cp,
		RequestSender sender
	) {
		logRequest(cp.getUrl(), cp.getBody(), sender.getAuthConfig());
		Callable<Either<HttpSenderResponse, HttpClientErrorException>> sinkCall = () -> sender.sendErrorAsValue(cp);

		Retryer<Either<HttpSenderResponse, HttpClientErrorException>> retryer = RetryerBuilder.<Either<HttpSenderResponse, HttpClientErrorException>>newBuilder()
			.retryIfException(this::doNotSkipException)
			.withWaitStrategy(WaitStrategies.fixedWait(config.retryDelay, TimeUnit.MILLISECONDS))
			.withStopStrategy(StopStrategies.stopAfterAttempt(config.retryThreshold))
			.build();

		try {
			Either<HttpSenderResponse, HttpClientErrorException> r = retryer.call(sinkCall);

			if (r.isLeft()) {
				HttpSenderResponse response = r.left().get();
				logResponse(response);
				return response;
			} else {
				HttpClientErrorException e = r.right().get();
				return new HttpSenderResponse(of(e.getResponseBodyAsByteArray()), e.getResponseHeaders(),
					e.getRawStatusCode(), of(e));
			}
		} catch (RetryException e) {
			// if retry still has no data, guava throws RetryException
			throw e.getCause();
		}
	}

	private boolean doNotSkipException(Throwable exc) {
		boolean noRetry = exc instanceof HttpClientErrorException &&
			config.notRetryCodes.contains(((HttpClientErrorException) exc).getRawStatusCode());
		return !noRetry;
	}

	public void ingestResponse(
		IngestFeedMessage ingestFeedMessage,
		RestIngestionCallback ingester
	) {
		try {
			ingester.ingest(ingestFeedMessage.getNewBody());
		} catch (Exception e) {
			// catch exception in order to not break Sink process
			// otherwise original message will be sent to quarantine
			logger.error("Could not ingest message", e);
		}
	}

	private void addHeadersToMeta(NexlaMessage originalNexlaMessage, String url, HttpSenderResponse resp) {
		if (config.addHeadersToMeta) {
				Map<String, Object> tagsMap = ofNullable(originalNexlaMessage.getNexlaMetaData().getTags())
					.orElse(Maps.newHashMap());
				originalNexlaMessage.getNexlaMetaData().setTags(tagsMap);
				tagsMap.put(NX_RESPONSE_HEADERS, resp.getHeaders());
				tagsMap.put(NX_REQUEST_URL, url);
		}
	}

	private Map<String, Object> getBodyMap(HttpSenderResponse response) {
		byte[] bytes = response.getBody().orElse("".getBytes());
		try {
			MediaType ct = ofNullable(response.getHeaders().getContentType()).orElse(MediaType.APPLICATION_JSON);
			Optional<String> charset = ofNullable(ct).map(x -> x.getParameter("charset"));
			String responseFormat = getResponseFormat(ofNullable(ct));
			String responseDataPath = "json".equals(responseFormat) ? "$" : null;
			return readData(responseFormat, responseDataPath, empty(), charsetDetectionThreshold, charset, bytes)
				.findFirst()
				.get();
		} catch (Exception e) {
			// swallow exception to proceed to default plain text result
		}
		return ImmutableMap.of("value", new String(bytes));
	}

	private Optional<String> makeBody(
		Supplier<LinkedHashMap<String, Object>> flattenedData,
		Supplier<Map<String, String>> flattenedAndEncodedData,
		String contentType,
		LinkedHashMap<String, Object> rawMessage
	) {
		// if body is present let's use it,
		// otherwise we can make body out of entire message in url-form-encoded assuming that's what user wants
		String template = config.body
			.map(VarUtils.VarInfo::getTemplate)
			.orElseGet(() ->
				APPLICATION_FORM_URLENCODED_VALUE.equals(contentType)
					? EntryStream.of(flattenedAndEncodedData.get()).mapKeyValue((k, v) -> encode(k) + "=" + v).joining("&")
					: null);

		return ofNullable(template)
			.map(t -> replaceComplexVars(t, contentType, rawMessage))
			.map(t ->
				config.body
					.map(body -> {
						Map<String, String> replacementMap = APPLICATION_FORM_URLENCODED_VALUE.equals(contentType)
							? flattenedAndEncodedData.get()
							: toLinkedHashMap(EntryStream.of(flattenedData.get()).mapValues(v -> ObjectUtils.toString(v, null)));

						return replaceVars(
							new VarUtils.VarInfo(t, body.variables, body.defaults),
							replacementMap);
					})
					.orElse(t));
	}

	public Map<String, String> encode(Map<String, ?> dataMap) {
		Map<String, String> result = Maps.newHashMap();
		EntryStream.of(dataMap)
				.mapValues(v -> v != null ? encode(v.toString()) : null)
				.forEach(k -> result.put(k.getKey(), k.getValue()));
		return result;
	}

	@SneakyThrows
	private String encode(String s) {
		return UriUtils.encode(s, "UTF-8");
	}

	@SneakyThrows
	private String replaceComplexVars(String template, String contentType, LinkedHashMap<String, Object> rawMessage) {
		if (template.contains(VAR_MESSAGE_JSON_QUOTED)) {
			String replacement = getReplacement(contentType, rawMessage);
			return template.replace(VAR_MESSAGE_JSON_QUOTED, replacement);
		} else {
			return template;
		}
	}

	@SneakyThrows
	private String getReplacement(String contentType, LinkedHashMap<String, Object> rawMessage) {
		if (APPLICATION_FORM_URLENCODED_VALUE.equals(contentType)) {
			return escapeJson(toJsonString(rawMessage));
		} else if (APPLICATION_XML_VALUE.equals(contentType)) {
			XmlFileWriter writer = new XmlFileWriter();
			writer.options(config.originals());
			writer.useTempFile();
			writer.append(rawMessage);
			writer.close();
			String xmlValue = Source.fromFile(writer.getOutputPath(), new Codec(StandardCharsets.UTF_8))
				.getLines()
				.mkString("\n");
			new File(writer.getOutputPath()).delete();
			return xmlValue;
		} else {
			return toJsonString(rawMessage);
		}
	}

	private String escapeJson(String s) {
		return new String(JsonStringEncoder.getInstance().quoteAsUTF8(s));
	}

	@SneakyThrows
	private void logResponse(HttpSenderResponse response) {
		if (logVerboseWithSinkBody) {
			String responseBody = response.getBody().isPresent() ? IOUtils.toString(response.getBody().get(), "UTF-8") : "";
			logger.info(String.format("HTTP Response {statusCode: %s; responseBody: %s; responseHeaders: %s}",
				response.getStatusCode(), responseBody, response.getHeaders()));
		}
	}

	private void logRequest(String url, Optional<String> body, RestAuthConfig authConfig) {
		logger.info("Making request to url={}", StringUtils.abbreviate(url, 64));

		if (logVerboseWithSinkBody) {
			logger.info("Request full details: {} {} \nheaders={} \nbody={}", config.method.name(), url, authConfig.restHeaders, body);
		}
	}

}
