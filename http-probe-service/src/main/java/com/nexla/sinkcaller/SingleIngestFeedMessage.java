package com.nexla.sinkcaller;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.common.NexlaMessage;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public class SingleIngestFeedMessage extends IngestFeedMessage<NexlaMessage, Map<String, Object>> {

	public SingleIngestFeedMessage(NexlaMessage input, Map<String, Object> request, Map<String, Object> response) {
		super(input, request, response);
	}

	@Override
	public List<NexlaMessage> getNexlaMessageList() {
		return List.of(input);
	}

	@Override
	public Optional<String> getNewBody() {
		Map<String, Object> newBodyMap = Map.of(
			"input", this.input,
			"request", this.request,
			"response", this.response
		);
		return Optional.of(JsonUtils.toJsonString(newBodyMap));
	}
}