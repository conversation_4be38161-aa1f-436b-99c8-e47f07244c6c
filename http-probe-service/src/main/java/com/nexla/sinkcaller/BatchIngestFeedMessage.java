package com.nexla.sinkcaller;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.common.NexlaMessage;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public class BatchIngestFeedMessage extends IngestFeedMessage<List<NexlaMessage>, List<Map<String, Object>>> {

	@Data
	private class CustomNexlaMessage {
		private String startTrackerId;
		private String endTrackerId;
		private int recordCount;
		private Long startOffset;
		private Long endOffset;

		public CustomNexlaMessage(List<NexlaMessage> messageList) {
			this.recordCount = messageList.size();

			NexlaMessage firstMessage = messageList.get(0);
			NexlaMessage lastMessage = messageList.get(messageList.size() - 1);
			this.startOffset = firstMessage.getNexlaMetaData().getSourceOffset();
			this.endOffset = lastMessage.getNexlaMetaData().getSourceOffset();
			this.startTrackerId = String.valueOf(firstMessage.getNexlaMetaData().getTrackerId());
			this.endTrackerId = String.valueOf(lastMessage.getNexlaMetaData().getTrackerId());
		}
	}

	public BatchIngestFeedMessage(List<NexlaMessage> input, Map<String, Object> response) {
		super(input, null, response);
	}

	@Override
	public List<NexlaMessage> getNexlaMessageList() {
		return this.input;
	}

	@Override
	public Optional<String> getNewBody() {
		Map<String, Object> newBodyMap = Map.of(
			"input", Map.of(
				"nexlaMetaData", new CustomNexlaMessage(getNexlaMessageList()),
				"rawMessage", Map.of()
			),
			"request", Map.of(),
			"response", this.response
		);
		return Optional.of(JsonUtils.toJsonString(newBodyMap));
	}
}