package com.nexla.rest;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.rholder.retry.*;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.nexla.common.NexlaMessage;
import com.nexla.common.exception.ProbeRetriableException;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.parse.FilePropertiesDetector;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.HttpCallParameters;
import com.nexla.connector.config.rest.RestHeaders;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.parser.JsonParser;
import com.nexla.probe.http.HttpSenderResponse;
import com.nexla.probe.http.RequestSender;
import com.nexla.probe.http.RestConnectorService;
import com.nexla.rest.encode.URIUtil;
import com.nexla.rest.pojo.ErrorWithDescription;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.RestIterationResult;
import com.nexla.rest.pojo.UrlBody;
import lombok.Getter;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResourceAccessException;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.common.StreamUtils.zipWithIndices;
import static com.nexla.probe.http.RestConnectorService.getResponseFormat;
import static java.util.Optional.*;

@Getter
public abstract class RestIteration<T extends RestIterationOffset> {

	public static final String REPLACED_MAP = "!!!===26===!!!";
	public static final String ENCODED_MAP = "%26";
	public static final String NX_HEADER = "nx.header.";
	public static final HttpSenderResponse EMPTY_RESPONSE = new HttpSenderResponse(empty(), new HttpHeaders(), HttpStatus.OK.value());

	protected final RestIterationConfig config;
	protected final NexlaLogger logger;
	protected final NexlaPool<RequestSender> senderPool;
	protected final VarReplacer varReplacer;

	public RestIteration(RestIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		this.config = config;
		this.logger = logger;
		this.senderPool = senderPool;
		this.varReplacer = new VarReplacer(config);
	}

	protected abstract Class<T> getOffsetClass();

	protected String getRequestUrl(T offset, String url) {
		return url;
	}

	protected Map<String, ?> getParameters(T offset) {
		return Collections.emptyMap();
	}

	protected abstract ParsedData createCallResult(
		Optional<byte[]> responseBytes,
		HttpHeaders headers,
		EntryStream<Integer, NexlaMessage> recordStream,
		T prevCtx,
		String url);

	protected abstract T createStartOffset(Integer parentMessageNumber, DateTime dateTime);

	public boolean urlDateIsChanged() {
		return false;
	}

	public RestIterationConfig getConfig() {
		return config;
	}

	public String getPartition() {
		return config.url != null ? config.url.template : "";
	}

	public NexlaLogger getLogger() {
		return logger;
	}

	public String getCode() {
		return config.key;
	}

	public RestIterationResult readRecords(Map<String, String> substituteMap, Optional<RestIterationResult> lastResult, T offset) {
		return readRecords(substituteMap, offset);
	}

	@SneakyThrows
	public RestIterationResult readRecords(Map<String, String> substituteMap, T offset) {
		Map<String, String> finalSubstituteMap = Maps.newHashMap(config.url.defaults);
		finalSubstituteMap.putAll(substituteMap);

		Set<String> substituteKeys = substituteMapKeys(finalSubstituteMap, offset);
		// so we won't have urlFilled as false because macro
		if (!Objects.isNull(config.paramIdMacro)) {
			config.paramIdMacro.map(substituteKeys::add);
		}
		boolean urlFilled = substituteKeys.containsAll(config.url.variables);

		if (!urlFilled) {
			Set<String> absentKeys = Sets.newHashSet(config.url.variables);
			absentKeys.removeAll(substituteKeys);
			String errorMsg = "Skipping call '" + config.url.template + "' because those variables are not filled: " + absentKeys;
			logger.error(errorMsg);
			return emptyResult(offset, Optional.of(new ErrorWithDescription(ErrorWithDescription.Level.WARN, errorMsg)));
		} else {
			UrlBody urlBody = getUrlBody(substituteMap, offset);
			try {
				HttpSenderResponse result = senderPool.withPooledObject(sender -> readResponse(substituteMap, sender, urlBody));
				return processResponse(offset, urlBody, result);
			} catch (ProbeRetriableException pr) {
				Throwable exp = pr.getCause();

				// TODO: it looks like a temporary solution and need to be refactored
				boolean ignoreError = config.ignoreError.orElse(
						exp instanceof ResourceAccessException ||
								(exp instanceof HttpClientErrorException && config.notRetryCodes.contains(((HttpClientErrorException) exp).getRawStatusCode()))
				);

				if (ignoreError || config.surfaceNotifications) {
					String errorMessage = String.format("Error while executing request to %s, cause: %s", pr.getMessage(), pr.getCause().getMessage());
					return emptyResult(offset, Optional.of(new ErrorWithDescription(errorMessage)));
				}
				throw pr;
			}

		}
	}

	private RestIterationResult emptyResult(T offset, Optional<ErrorWithDescription> errorMessage) {
		return processResponse(offset, new UrlBody(config.url.template, null, null), EMPTY_RESPONSE, errorMessage);
	}

	private Set<String> substituteMapKeys(Map<String, String> substituteMap, T offset) {
		var result = Sets.newHashSet(substituteMap.keySet());
		Set<String> datesKeys = varReplacer.getSubstituteMapWithDates(offset.getDateTime()).keySet();
		result.addAll(datesKeys);
		return result;
	}

	@SneakyThrows
	Optional<byte[]> readBytes(Map<String, String> substituteMap, T offset) {
		UrlBody urlBody = getUrlBody(substituteMap, offset);
		return senderPool.withPooledObject(sender -> readResponse(substituteMap, sender, urlBody)).getBody();
	}

	/**
	 * Method to be overridden by all macro implementations.
	 * @param offset to pick the page from.
	 * @return substituteMap to be used in RestIteration#getUrlBody
	 */
	public Map<String, String> macroSubstitutionMap(T offset) {
		return new HashMap<>();
	}

	@SneakyThrows
	public UrlBody getUrlBody(Map<String, String> substituteMap, T offset) {
		Map<String, String> substituteMapWithDates = Maps.newHashMap(varReplacer.getSubstituteMapWithDates(offset.getDateTime()));
		substituteMapWithDates.putAll(substituteMap);
		substituteMapWithDates.putAll(this.macroSubstitutionMap(offset));

		String replacedUrl = replaceWithInitRequestIfNeeded(replaceQuoteSensitive(substituteMapWithDates), offset);
		String body = varReplacer.getBody(substituteMapWithDates);
		String responseDataAdditional = varReplacer.getResponseDataAdditional(substituteMapWithDates);
		Map<String, ?> params = getParameters(offset);
		boolean postMethod = config.method == HttpMethod.POST || config.method == HttpMethod.PUT;
		String url = getRequestUrl(offset, replacedUrl);
		if (config.paramsInBody && postMethod) {
			Map<String, Object> jsonMap = StringUtils.isEmpty(body) ? Maps.newHashMap() : JsonUtils.jsonToMap(body);
			jsonMap.putAll(params);
			return new UrlBody(url, JsonUtils.toJsonString(jsonMap), responseDataAdditional);
		} else {
			String constructedUrl = url;
			if (!config.skipUrlEncoding && !config.supportDuplicateQueryParams) {
				URIBuilder builder = new URIBuilder(url);
				Map<String, Object> resultParameters = new LinkedHashMap<>();
				StreamEx.of(builder.getQueryParams())
						.forEach(x -> resultParameters.put(x.getName(), x.getValue()));
				resultParameters.putAll(params);
				builder.clearParameters();
				EntryStream.of(resultParameters).forKeyValue((param, value) ->
						builder.addParameter(param, ofNullable(value)
						.map((Object::toString))
						.orElse(null)
				));
				constructedUrl = builder.build().toString();
			}

			return new UrlBody(constructedUrl, body, responseDataAdditional);
		}
	}

	@SneakyThrows
	private String replaceQuoteSensitive(Map<String, String> substituteMapWithDates) {
		String url = varReplacer.getUrl(substituteMapWithDates);

		if (!config.skipUrlEncoding) {
			return encodeIfNeeded(url);
		} else {
			return url;
		}
	}

	@SneakyThrows
	public static String encodeIfNeeded(String url) {
		if (!validUrl(url)) {
			String replace = url.replaceAll(ENCODED_MAP, REPLACED_MAP);
			String decode = URIUtil.decode(replace);
			String encode = URIUtil.encodeQuery(decode);
			return encode.replaceAll(REPLACED_MAP, ENCODED_MAP);
		} else {
			return url;
		}
	}

	private static boolean validUrl(String url) {
		try {
			new URIBuilder(url);
		} catch (Exception e) {
			return false;
		}
		return true;
	}

	protected RestIterationResult processResponse(T offset, UrlBody urlBody, HttpSenderResponse response, Optional<ErrorWithDescription> errorMessage) {
		Optional<byte[]> responseBytes = response.getBody();
		String url = urlBody.getUrl();
		return responseBytes
			.map(bytes -> readData(bytes, ofNullable(response.getHeaders()).map(HttpHeaders::getContentType), urlBody.getResponseDataAdditional()))
			.map(this::filterErrors)
			.map(r -> filterMessagesByNum(r, offset))
			.map(r -> createCallResult(responseBytes, response.getHeaders(), r, offset, url))
			.map(r -> new RestIterationResult(r.getEntries(), r.getOffset(), r.getNextOffset(), url))
						.orElse(new RestIterationResult(Collections.emptyList(), offset, Optional.empty(), url, errorMessage));
	}

	protected RestIterationResult processResponse(T offset, UrlBody urlBody, HttpSenderResponse response) {
		return processResponse(offset, urlBody, response, Optional.empty());
	}

	private EntryStream<Integer, NexlaMessage> filterMessagesByNum(
		StreamEx<NexlaMessage> recordStream,
		T offset
	) {
		return zipWithIndices(recordStream)
			.mapKeys(key -> key + 1) // messages numeration on page starts from 1
			.filterKeys(offsetOnPage -> {
				Integer messageNumber = ofNullable(offset.getMessageNumber()).orElse(0);
				return offsetOnPage >= messageNumber;
			});
	}

	protected StreamEx<NexlaMessage> readData(byte[] bytes, Optional<MediaType> contentType, String responseDataAdditional) {
				Optional<String> detectedCharset = config.overrideWithCharset.or(
			() -> contentType.map(x -> x.getParameter("charset"))
		);

		String content = new String(
			bytes,
			detectedCharset.map(Charset::forName).orElse(StandardCharsets.UTF_8)
		);

		String responseFormat = config.responseFormat
			.or(() -> FilePropertiesDetector.detectContentType(content, true).map(RestConnectorService::getResponseFormat))
			.orElseGet(() -> getResponseFormat(contentType));

		Optional<LinkedHashMap<String, Object>> optionalResponseDataAdditionalMap = Optional.ofNullable(responseDataAdditional)
				.filter(StringUtils::isNotBlank)
				.map(it -> JsonUtils.stringToType(it, new TypeReference<>(){}));

		return RestConnectorService
			.readData(
				responseFormat,
				config.responseDataPath,
				config.responseDataPathAdditional,
				config.charsetDetectionThreshold,
				detectedCharset,
				bytes)
			.map(it -> addAdditionalDataIfPresent(it, optionalResponseDataAdditionalMap))
			.map(NexlaMessage::new);
	}

	public LinkedHashMap<String, Object> addAdditionalDataIfPresent(Map<String, Object> realData, Optional<LinkedHashMap<String, Object>> responseDataAdditional) {
		if (responseDataAdditional.isPresent()) {
			LinkedHashMap<String, Object> result = Maps.newLinkedHashMap(responseDataAdditional.get());
			result.putAll(realData);
			return result;
		}
		return Maps.newLinkedHashMap(realData);
	}

	protected StreamEx<Object> extractMatchesByPath(Optional<byte[]> responseBytes, String responsePath) {
		JsonParser parser = (JsonParser) new JsonParser().config(getConfig().originalsStrings());
		return parser.find(responsePath, responseBytes.get());
	}

	public StreamEx<NexlaMessage> filterErrors(StreamEx<NexlaMessage> recordStream) {
		return recordStream;
	}

	@SneakyThrows
	protected HttpSenderResponse readResponse(Map<String, String> substituteMap, RequestSender sender, UrlBody urlBody) {

		Callable<HttpSenderResponse> call = () -> {
			RestHeaders headers = config.restHeaders;
			if (config.defaultHeadersFallback) {
				headers = headers.withDefaultHeaders();
			}

			RestHeaders restHeaders = headers.withSubstitutions(substituteMap);
			HttpCallParameters cp = new HttpCallParameters(urlBody.getUrl(), config.skipUrlEncoding, config.method, ofNullable(urlBody.getBody()), empty(), restHeaders);
			return onResponse(sender.send(cp));
		};

		Retryer<HttpSenderResponse> retryer = RetryerBuilder.<HttpSenderResponse>newBuilder()
			.retryIfException(this::doNotSkipException)
			.withWaitStrategy(WaitStrategies.fixedWait(config.retryDelay, TimeUnit.MILLISECONDS))
			.withStopStrategy(StopStrategies.stopAfterDelay(config.authConfig.errorRetryWindow, TimeUnit.MILLISECONDS))
			.build();

		try {
			return retryer.call(call);
		} catch (Exception e) {
			if (e instanceof RetryException || e.getCause() instanceof HttpClientErrorException ) {
				// if retry still has no data, guava throws RetryException
				Throwable exp = e.getCause();
				logException(exp, urlBody.getUrl(), ofNullable(urlBody.getBody()));
				throw new ProbeRetriableException(urlBody.getUrl(), exp);
			} else throw e;
		}
	}

	protected HttpSenderResponse onResponse(HttpSenderResponse response) {
		return response;
	}

	private boolean doNotSkipException(Throwable exc) {
		boolean noRetry = exc instanceof HttpClientErrorException &&
			config.notRetryCodes.contains(((HttpClientErrorException) exc).getRawStatusCode());
		return !noRetry;
	}

	protected void logException(Throwable e, String url, Optional<String> body) {
		logger.error("Failed to send request for resource_type={} resource_id={} url={} body={}", SOURCE, config.sourceId, url, body, e);
	}

	T offsetFromJson(String json) {
		return RestIterationOffset.fromJson(json, getOffsetClass());
	}

	public Integer getRetryDelay() {
		return config.retryDelay;
	}

	public Integer getRetryNum() {
		return config.retryNum;
	}

	public String replaceWithInitRequestIfNeeded(String url, RestIterationOffset offset) {
		return url;
	}
}