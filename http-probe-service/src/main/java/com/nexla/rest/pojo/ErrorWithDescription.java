package com.nexla.rest.pojo;

import lombok.Data;

@Data
public class ErrorWithDescription {
    private final String error;
    private final Level level;

    public ErrorWithDescription(String error) {
        this(Level.ERROR, error);
    }

    public ErrorWithDescription(Level level, String error) {
        this.level = level;
        this.error = error;
    }

    public enum Level {
        WARN, ERROR
    }
}
