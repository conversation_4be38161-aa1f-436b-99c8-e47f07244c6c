package com.nexla.rest.pojo;

import com.nexla.rest.RestIterationOffset;
import lombok.Data;
import one.util.streamex.EntryStream;

import java.util.LinkedHashMap;
import java.util.Map;

import static com.nexla.rest.RestIteration.NX_HEADER;

@Data
public class ResultEntry<T extends RestIterationOffset> {

	private final LinkedHashMap<String, Object> dataMap;
	private final Integer offsetOnPage;
	private final Object nexlaMetaRangeFrom;
	private final T sourceOffset;
	private final Map<String, String> headers;
	private final Map<String, Object> passthroughTags;

	public LinkedHashMap<String, Object> getDataMapWithHeaders() {
		LinkedHashMap<String, Object> result = new LinkedHashMap<>();
		EntryStream.of(headers)
			.mapKeys(x -> NX_HEADER + x)
			.forKeyValue(result::put);
		result.putAll(dataMap);
		return result;
	}

}