package com.nexla.rest.iterations;

import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.RestIteration;
import com.nexla.rest.iterations.offsets.StaticUrlOffset;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.ResultEntry;
import one.util.streamex.EntryStream;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.util.Optional.empty;

public class StaticUrl extends RestIteration<StaticUrlOffset> {

	public StaticUrl(RestIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		super(config, senderPool, logger);
	}

	@Override
	public Class<StaticUrlOffset> getOffsetClass() {
		return StaticUrlOffset.class;
	}

	@Override
	public ParsedData createCallResult(
		Optional<byte[]> responseBytes,
		HttpHeaders headers,
		EntryStream<Integer, NexlaMessage> recordStream,
		StaticUrlOffset offset,
		String url
	) {
		List<ResultEntry> records = recordStream
			.mapKeyValue((offsetOnPage, data) -> new ResultEntry(data.getRawMessage(), offsetOnPage, null, offset, headers.toSingleValueMap(), null))
			.toList();

		return new ParsedData(records, offset, empty());
	}

	@Override
	public StaticUrlOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		return new StaticUrlOffset(0, empty(), parentMessageNumber, dateTime);
	}

}
