package com.nexla.rest.iterations.graphql;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.RestIterationOffset;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.ResultEntry;
import lombok.Getter;
import one.util.streamex.EntryStream;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;

import java.util.*;

import static java.util.Optional.empty;
import static java.util.Optional.of;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

public class GraphqlCursorIteration extends BaseGraphqlPagination<GraphqlCursorIteration.GraphqlCursorOffset> {
	private static final String FALLBACK_CURSOR = null;

	private final String cursorName;
	private final String cursorPath;

	public GraphqlCursorIteration(RestIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		super(config, senderPool, logger);

		this.cursorName = config.graphqlCursorName;
		this.cursorPath = config.graphqlCursorResponsePath;// orElse("$..cursor");
	}

	@Override
	public Class<GraphqlCursorOffset> getOffsetClass() {
		return GraphqlCursorOffset.class;
	}

	@Override
	public ParsedData<?> createCallResult(
		Optional<byte[]> responseBytes,
		HttpHeaders headers,
		EntryStream<Integer, NexlaMessage> recordStream,
		GraphqlCursorOffset cursorOffset,
		String url
	) {
		Map<Integer, NexlaMessage> recordStreamMap = recordStream.toMap();

		List<ResultEntry> records = EntryStream.of(recordStreamMap)
			.mapKeyValue((offsetOnPage, data) -> {
				GraphqlCursorOffset off = new GraphqlCursorOffset(
					offsetOnPage,
					of(recordStreamMap.size()),
					cursorOffset.getCursor(),
					cursorOffset.getParentMessageNumber(),
					cursorOffset.getDateTime(),
					false
				);
				return new ResultEntry(data.getRawMessage(), offsetOnPage, cursorOffset.getCursor().orElse(FALLBACK_CURSOR), off, headers.toSingleValueMap(), null);
			})
			.toList();

		Optional<GraphqlCursorOffset> nextCallContext =
			extractMatchesByPath(responseBytes, cursorPath)
				.filter(Objects::nonNull)
				.map(Object::toString)
				.findFirst()
				.filter(nextToken -> !records.isEmpty() && isValidToken(cursorOffset, nextToken))
				.map(nextToken -> new GraphqlCursorOffset(
					0,
					empty(),
					of(nextToken),
					cursorOffset.getParentMessageNumber(),
					cursorOffset.getDateTime(),
					false)
				);

		return new ParsedData<>(records, cursorOffset, nextCallContext);
	}

	private boolean isValidToken(GraphqlCursorOffset offset, String nextToken) {
		return isNotEmpty(nextToken) && offset
			.getCursor()
			.map(tok -> !nextToken.equals(tok))
			.orElse(true);
	}

	@Override
	public GraphqlCursorOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		return new GraphqlCursorOffset(0, empty(), empty(), parentMessageNumber, dateTime, true);
	}

	@Override
	protected Map<String, Object> variables(GraphqlCursorOffset offset) {
		return Collections.singletonMap(
				cursorName,
				offset.getCursor().orElse(FALLBACK_CURSOR)
		);
	}

	@Getter
	public static class GraphqlCursorOffset extends RestIterationOffset {
		private final Optional<String> cursor;
		private final Boolean isInitialIteration;

		public GraphqlCursorOffset(
			@JsonProperty("messageNumber") Integer messageNumber,
			@JsonProperty("pageSize") Optional<Integer> pageSize,
			@JsonProperty("responseToken") Optional<String> cursor,
			@JsonProperty("parentMessageNumber") Integer parentMessageNumber,
			@JsonProperty("dateTime") DateTime dateTime,
			@JsonProperty("isInitialIteration") Boolean isInitialIteration
		) {
			super(messageNumber, pageSize, parentMessageNumber, dateTime);

			this.cursor = cursor;
			this.isInitialIteration = isInitialIteration;
		}

		public static RestIterationOffset withCursor(GraphqlCursorOffset original, Optional<String> cursor) {
			return new GraphqlCursorOffset(
					original.getMessageNumber(),
					original.getPageSize(),
					cursor,
					original.getParentMessageNumber(),
					original.getDateTime(),
					original.getIsInitialIteration()
			);
		}
	}
}
