package com.nexla.rest.iterations.graphql;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.RestIterationOffset;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.ResultEntry;
import lombok.Getter;
import one.util.streamex.EntryStream;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;

import java.util.*;

import static com.nexla.rest.iterations.PagingNextUrl.FAKE_URL;
import static java.util.Optional.empty;
import static java.util.Optional.of;

public class GraphqlPageIteration extends BaseGraphqlPagination<GraphqlPageIteration.GraphqlPageOffset> {
	private static final Integer DEFAULT_PAGE = 1;

	private final String pageVariableName;
	private final Integer pageStart;
	private final Integer pageLimit;

	public GraphqlPageIteration(RestIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		super(config, senderPool, logger);

		this.pageVariableName = config.graphqlPageVariableName;
		this.pageStart = config.graphqlPageStart.orElse(DEFAULT_PAGE);
		this.pageLimit = config.graphqlPageLimit.orElse(Integer.MAX_VALUE);
	}

	@Override
	public Class<GraphqlPageOffset> getOffsetClass() {
		return GraphqlPageOffset.class;
	}

	@Override
	public ParsedData<?> createCallResult(
		Optional<byte[]> responseBytes,
		HttpHeaders headers,
		EntryStream<Integer, NexlaMessage> recordStream,
		GraphqlPageOffset pageOffset,
		String url
	) {
		Map<Integer, NexlaMessage> recordStreamMap = recordStream.toMap();

		int currentPage = pageOffset.getPage()
				.orElse(this.pageStart);

		List<ResultEntry> records = EntryStream.of(recordStreamMap)
			.mapKeyValue((offsetOnPage, data) -> {
				GraphqlPageOffset off = new GraphqlPageOffset(
					offsetOnPage,
					of(recordStreamMap.size()),
					pageOffset.getPage(),
					pageOffset.getParentMessageNumber(),
					pageOffset.getDateTime()
				);
				return new ResultEntry(data.getRawMessage(), offsetOnPage, currentPage, off, headers.toSingleValueMap(), null);
			})
			.toList();

		int totalPages = this.pageLimit;
		if (config.responseTotalPagesPath.isPresent()) {
			String path = config.responseTotalPagesPath.get();

			totalPages = extractMatchesByPath(responseBytes, path)
					.filter(Objects::nonNull)
					.map(Object::toString)
					.filter(StringUtils::isNumeric)
					.map(Integer::parseInt)
					.findFirst()
					.orElse(totalPages);
		}

		if (records.isEmpty() || currentPage >= this.pageLimit || currentPage >= totalPages) {
			return new ParsedData<>(records, pageOffset, empty());
		}

		return new ParsedData<>(records, pageOffset, of(new GraphqlPageOffset(
				0,
				empty(),
				of(currentPage + 1),
				pageOffset.getParentMessageNumber(),
				pageOffset.getDateTime()
		)));
	}

	@Override
	public GraphqlPageOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		return new GraphqlPageOffset(0, empty(), empty(), parentMessageNumber, dateTime);
	}

	@Override
	protected Map<String, Object> variables(GraphqlPageOffset offset) {
		return Collections.singletonMap(
				this.pageVariableName,
				offset.getPage().orElse(this.pageStart)
		);
	}

	@Getter
	public static class GraphqlPageOffset extends RestIterationOffset {
		private final Optional<Integer> page;

		public GraphqlPageOffset(
			@JsonProperty("messageNumber") Integer messageNumber,
			@JsonProperty("pageSize") Optional<Integer> pageSize,
			@JsonProperty("responseToken") Optional<Integer> page,
			@JsonProperty("parentMessageNumber") Integer parentMessageNumber,
			@JsonProperty("dateTime") DateTime dateTime
		) {
			super(messageNumber, pageSize, parentMessageNumber, dateTime);

			this.page = page;
		}
	}
}
