package com.nexla.rest.iterations;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.HttpSenderResponse;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.RestIteration;
import com.nexla.rest.RestIterationOffset;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.ResultEntry;
import lombok.Getter;
import one.util.streamex.EntryStream;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.util.List;
import java.util.Optional;

import static java.util.Optional.empty;

// emits as response what was passed in response.data in source config
public class EmitOnce extends RestIteration<EmitOnce.Offset> {

	public EmitOnce(RestIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		super(config, senderPool, logger);
	}

	@Override
	public Class<Offset> getOffsetClass() {
		return Offset.class;
	}

	@Override
	public ParsedData createCallResult(
		Optional<byte[]> responseBytes,
		HttpHeaders headers,
		EntryStream<Integer, NexlaMessage> recordStream,
		Offset offset,
		String url
	) {
		List<ResultEntry> records = recordStream
			.mapKeyValue((offsetOnPage, data) -> new ResultEntry(data.getRawMessage(), offsetOnPage, null, offset, headers.toSingleValueMap(), null))
			.toList();

		return new ParsedData(records, offset, empty());
	}

	@Override
	protected HttpSenderResponse onResponse(HttpSenderResponse response) {
		return new HttpSenderResponse(
				config.responseData.map(String::getBytes).or(response::getBody),
				response.getHeaders(),
				HttpStatus.OK.value()
		);
	}

	@Override
	public Offset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		return new Offset(0, empty(), parentMessageNumber, dateTime);
	}

	@Getter
	public class Offset extends RestIterationOffset {

		public Offset(
				@JsonProperty("messageNumber") Integer messageNumber,
				@JsonProperty("pageSize") Optional<Integer> pageSize,
				@JsonProperty("parentMessageNumber") Integer parentMessageNumber,
				@JsonProperty("dateTime") DateTime dateTime
		) {
			super(messageNumber, pageSize, parentMessageNumber, dateTime);
		}
	}
}
