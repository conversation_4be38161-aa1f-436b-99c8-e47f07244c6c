package com.nexla.rest.iterations;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.common.time.VarUtils;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.RestIteration;
import com.nexla.rest.RestIterationOffset;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.ResultEntry;
import lombok.Getter;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.http.client.utils.URIBuilder;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;

import java.net.URISyntaxException;
import java.util.*;

import static java.util.Optional.empty;

public class HorizontalIteration extends RestIteration<HorizontalIteration.HorizontalOffset> {

	public HorizontalIteration(RestIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		super(config, senderPool, logger);
	}

	@Override
	public Class getOffsetClass() {
		return HorizontalOffset.class;
	}

	@Override
	@SneakyThrows
	protected String getRequestUrl(HorizontalOffset prevCtx, String url) {
		return prevCtx
			.replacement
			.map(replace -> VarUtils.replaceVars(config.pagingHorizontalUrl.get(), replace))
			.map(RestIteration::encodeIfNeeded)
			.orElse(url);
	}

	@Override
	public ParsedData createCallResult(
		Optional<byte[]> responseBytes,
		HttpHeaders headers,
		EntryStream<Integer, NexlaMessage> recordStream,
		HorizontalOffset offset,
		String url
	) {
		List<ResultEntry> records = recordStream
			.mapKeyValue((offsetOnPage, data) -> {
				HorizontalOffset off = new HorizontalOffset(
					Optional.of(headers.toSingleValueMap()),
					offset.getParentMessageNumber(),
					offset.getDateTime()
				);
				return new ResultEntry(data.getRawMessage(), offsetOnPage, url, off, headers.toSingleValueMap(), null);
			})
			.toList();

		boolean empty = records.isEmpty() || (records.size() == 1 &&
			(records.get(0).getDataMap().isEmpty() || (records.get(0).getDataMap().get("value") instanceof Collection && ((Collection<?>) records.get(0).getDataMap().get("value")).isEmpty())));

		if (empty) {
			return new ParsedData(Lists.newArrayList(), offset, empty());
		} else {
			Optional<HorizontalOffset> nextOffset = offset.replacement
					.map(x -> Optional.of(offset))
					.orElseGet(() -> {
						Map<String, String> replacement = Maps.newHashMap();

						headers.toSingleValueMap()
								.forEach((k, v) -> replacement.put("nx.header." + k, v));

						config.responseNextTokenDataPath.ifPresent(path -> extractMatchesByPath(responseBytes, path)
								.filter(Objects::nonNull)
								.map(Object::toString)
								.findFirst()
								.ifPresent(nextToken -> {
									String[] pathArr = path.split("\\.");
									replacement.put("nx.body." + pathArr[pathArr.length - 1], nextToken);
								}));

						getQueryParams(url).forEach((k, v) -> replacement.put("nx.query." + k, v));

						return Optional.of(new HorizontalOffset(Optional.of(replacement), offset.getParentMessageNumber(), offset.getDateTime()));
					});
			return new ParsedData(records, offset, nextOffset);
		}
	}

	private Map<String, String> getQueryParams(String url) {
		Map<String, String> resultParameters = new LinkedHashMap<>();
		URIBuilder builder;
		try {
			builder = new URIBuilder(url);
			StreamEx.of(builder.getQueryParams())
					.forEach(x -> resultParameters.put(x.getName(), x.getValue()));
		} catch (URISyntaxException e) {
			logger.error("Can't get QueryParams from url = {}: {}", url, e.getMessage(), e);
		}
		return resultParameters;
	}

	@Override
	public HorizontalOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		return new HorizontalOffset(empty(), parentMessageNumber, dateTime);
	}

	@Getter
	public static class HorizontalOffset extends RestIterationOffset {

		private final Optional<Map<String, String>> replacement;

		public HorizontalOffset(
			@JsonProperty("replacement") Optional<Map<String, String>> replacement,
			@JsonProperty("parentMessageNumber") Integer parentMessageNumber,
			@JsonProperty("dateTime") DateTime dateTime
		) {
			super(0, empty(), parentMessageNumber, dateTime);
			this.replacement = replacement;
		}

	}

}