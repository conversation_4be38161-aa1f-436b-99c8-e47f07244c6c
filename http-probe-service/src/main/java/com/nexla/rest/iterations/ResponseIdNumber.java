package com.nexla.rest.iterations;

import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.RestIteration;
import com.nexla.rest.RestIterationOffset;
import com.nexla.rest.iterations.offsets.ResponseIdNumberOffset;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.ResultEntry;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static com.nexla.common.ConverterUtils.toLong;
import static com.nexla.probe.http.RestConnectorService.MAX_ERRORS_TO_LOG;

public class ResponseIdNumber extends RestIteration<ResponseIdNumberOffset> {

	public static final long FAKE_ID = -1L;

	public ResponseIdNumber(RestIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		super(config, senderPool, logger);
	}

	@Override
	public Class<ResponseIdNumberOffset> getOffsetClass() {
		return ResponseIdNumberOffset.class;
	}

	@Override
	public Map<String, String> macroSubstitutionMap(ResponseIdNumberOffset offset) {
		Map<String, String> substitutionMacroMap = new HashMap<>();
		if (config.paramIdMacro.isEmpty()) {
			return substitutionMacroMap;
		}
		substitutionMacroMap.put(config.paramIdMacro.get(), number(offset).toString());
		return substitutionMacroMap;
	}

	// todo-mike double check.
	private Long number(ResponseIdNumberOffset offset) {
		return config.paramIdInclusive
				? offset.getLongId().orElse(config.startIncrementingIdFrom.orElseThrow()) + 1
				: offset.getLongId().orElse(config.startIncrementingIdFrom.orElseThrow());
	}

	@Override
	@SneakyThrows
	public Map<String, String> getParameters(ResponseIdNumberOffset prevCtx) {
		Map<String, String> params = Maps.newHashMap();

		if (config.paramIdMacro.isPresent()) {
			return params;
		}

		prevCtx.getLongId().ifPresent(id -> {
			long idFrom = config.paramIdInclusive ? id + 1 : id;
			params.put(config.paramId, Long.toString(idFrom));
		});

		return params;
	}

	@Override
	public ParsedData createCallResult(
		Optional<byte[]> responseBytes,
		HttpHeaders headers,
		EntryStream<Integer, NexlaMessage> recordStream,
		ResponseIdNumberOffset offset,
		String url
	) {
		AtomicReference<Long> maxId = new AtomicReference<>();

		List<ResultEntry> records = recordStream
			.mapKeyValue((offsetOnPage, data) -> {
				Long recordId = toLong(data.getRawMessage().get(config.responseId.get()));

				Long currMax = maxId.get();
				maxId.set(currMax != null ? Math.max(currMax, recordId) : recordId);

				RestIterationOffset newOffset = getOffsetData(offset.getParentMessageNumber(), Optional.of(recordId), offset.getDateTime());
				return new ResultEntry(data.getRawMessage(), offsetOnPage, offset.getLongId().orElse(FAKE_ID), newOffset, headers.toSingleValueMap(), null);
			})
			.toList();

		Optional<ResponseIdNumberOffset> nextCallContext = Optional
			.ofNullable(maxId.get())
			.map(Optional::of)
			.map(id -> getOffsetData(offset.getParentMessageNumber(), id, offset.getDateTime()))
			.filter(o -> o.getLongId().orElse(FAKE_ID) < config.endIncrementingIdTo.orElse(Long.MAX_VALUE));

		return new ParsedData(records, offset, nextCallContext);
	}

	@Override
	public ResponseIdNumberOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		return getOffsetData(parentMessageNumber, config.startIncrementingIdFrom, dateTime);
	}

	private ResponseIdNumberOffset getOffsetData(Integer parentMessageNumber, Optional<Long> id, DateTime dateTime) {
		return new ResponseIdNumberOffset(id, parentMessageNumber, dateTime);
	}

	@Override
	public StreamEx<NexlaMessage> filterErrors(StreamEx<NexlaMessage> recordStream) {
		AtomicInteger skippedCount = new AtomicInteger(0);
		return recordStream
			.filter(record -> {
				Object rawId = record.getRawMessage().get(config.responseId.get());
				Long id = toLong(rawId);
				if (id == null) {
					if ((skippedCount.get() < MAX_ERRORS_TO_LOG)) {
						getLogger().warn("Skipping record without id: id={} record={}", rawId, record);
						skippedCount.incrementAndGet();
					}
					return false;
				}
				return true;
			});
	}

}
