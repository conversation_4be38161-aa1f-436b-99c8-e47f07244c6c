package com.nexla.rest.iterations;

import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.RestIteration;
import com.nexla.rest.iterations.offsets.PagingNextUrlOffset;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.ResultEntry;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import org.apache.commons.httpclient.util.URIUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;

import java.net.URI;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static java.util.Optional.empty;
import static java.util.Optional.of;

public class PagingNextUrl extends RestIteration<PagingNextUrlOffset> {

	public static final String FAKE_URL = "";

	public PagingNextUrl(RestIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		super(config, senderPool, logger);
	}

	@Override
	public Class getOffsetClass() {
		return PagingNextUrlOffset.class;
	}

	@Override
	@SneakyThrows
	protected String getRequestUrl(PagingNextUrlOffset prevCtx, String url) {
		return prevCtx.getResponseUrl()
			.map(s -> nextUrl(s, url).toString())
			.orElse(url);
	}

	@Override
	public ParsedData createCallResult(
		Optional<byte[]> responseBytes,
		HttpHeaders headers,
		EntryStream<Integer, NexlaMessage> recordStream,
		PagingNextUrlOffset offset,
		String url
	) {
		var recordStreamMap = recordStream.toMap();
		List<ResultEntry> records = EntryStream.of(recordStreamMap)
			.mapKeyValue((offsetOnPage, data) -> {
				PagingNextUrlOffset off = new PagingNextUrlOffset(
					offsetOnPage,
					of(recordStreamMap.size()),
					offset.getResponseUrl(),
					offset.getParentMessageNumber(),
					offset.getDateTime()
				);
				return new ResultEntry(data.getRawMessage(), offsetOnPage, offset.getResponseUrl().orElse(FAKE_URL), off, headers.toSingleValueMap(), null);
			})
			.toList();

		Optional<PagingNextUrlOffset> nextCallContext =
			extractMatchesByPath(responseBytes, config.responseNextUrlDataPath.get())
				.filter(Objects::nonNull)
				.filter(e -> !url.equals(e))
				.map(Object::toString)
				.findFirst()
				.filter(StringUtils::isNotEmpty)
				.map(nextUrl -> new PagingNextUrlOffset(
					0,
					empty(),
					of(nextUrl),
					offset.getParentMessageNumber(),
					offset.getDateTime())
				)
				.filter(o -> !offset.getResponseUrl().orElse(FAKE_URL).equals(config.endUrlTo.orElse(null)));

		return new ParsedData(records, offset, nextCallContext);
	}

	@Override
	public PagingNextUrlOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		return new PagingNextUrlOffset(0, empty(), empty(), parentMessageNumber, dateTime);
	}

	@SneakyThrows
	private static URIBuilder nextUrl(String nextUrl, String urlTemplate) {
		boolean fullUrl = urlTemplate.startsWith(nextUrl);
		if (fullUrl) {
			return new URIBuilder(URIUtil.decode(nextUrl));
		} else {
			URIBuilder uriBuilder = new URIBuilder(nextUrl);
			URI baseUri = new URI(urlTemplate);

			uriBuilder.setScheme(baseUri.getScheme());
			uriBuilder.setHost(baseUri.getHost());
			uriBuilder.setPort(baseUri.getPort());
			return uriBuilder;
		}
	}

}
