package com.nexla.rest.iterations;

import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.probe.http.RestConnectorService;
import com.nexla.rest.RestIteration;
import com.nexla.rest.iterations.offsets.AsyncWaitOffset;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.ResultEntry;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.nexla.probe.http.RestConnectorService.getResponseFormat;
import static java.util.Optional.empty;
import static java.util.Optional.of;

public class AsyncWaitIteration extends RestIteration<AsyncWaitOffset> {

	private final Optional<Runnable> heartbeat;

	public AsyncWaitIteration(RestIterationConfig config,
							  NexlaPool<RequestSender> senderPool,
							  NexlaLogger logger,
							  Optional<Runnable> heartbeat) {
		super(config, senderPool, logger);
		this.heartbeat = heartbeat;
	}

	@Override
	public Class<AsyncWaitOffset> getOffsetClass() {
		return AsyncWaitOffset.class;
	}

	@Override
	public ParsedData createCallResult(
		Optional<byte[]> responseBytes,
		HttpHeaders headers,
		EntryStream<Integer, NexlaMessage> recordStream,
		AsyncWaitOffset offset,
		String url
	) {
		List<ResultEntry> records = recordStream
			.mapKeyValue((offsetOnPage, data) -> new ResultEntry(data.getRawMessage(), offsetOnPage, null, offset, headers.toSingleValueMap(), null))
			.toList();

		Optional<MediaType> contentType = of(headers.getContentType());

		String responseFormat = config.responseFormat
			.orElseGet(() -> getResponseFormat(contentType));

		Optional<String> detectedCharset = contentType.map(x -> x.getParameter("charset"));

		List<LinkedHashMap<String, Object>> dataRead = RestConnectorService.readData(
			responseFormat,
			config.asyncIterationPath.get(),
			empty(),
			config.charsetDetectionThreshold,
			detectedCharset,
			responseBytes.get()
		).toList();

		boolean conditionMet = dataRead
			.stream()
			.flatMap(x -> x.values().stream())
			.filter(Objects::nonNull)
			.map(Object::toString)
			.findFirst()
			.filter(nextToken -> nextToken.equals(config.asyncIterationValue.get()))
			.isPresent();

		if (conditionMet) {
			return new ParsedData(records, offset, empty());
		} else {
			logger.info("[" + config.key + "]: Await condition was not met. " + (dataRead.isEmpty() ? config.asyncIterationPath.get() + " was not found" : ""));
			boolean conditionStopMet = config.asyncIterationStopPath.map(stopPath ->
				extractMatchesByPath(responseBytes, stopPath)
					.filter(Objects::nonNull)
					.map(Object::toString)
					.findFirst()
					.filter(nextToken -> nextToken.equals(config.asyncIterationStopValue.get()))
					.isPresent())
				.orElse(false);

			boolean stopProcessing = conditionStopMet || offset.getRetryNum() >= config.asyncWaitRetryNum;
			if (stopProcessing) {
				return new ParsedData(Collections.emptyList(), offset, empty());
			} else {
				waitBeforeRetry();
				return new ParsedData(Collections.emptyList(), offset, Optional.of(offset.nextRetry()));
			}
		}
	}

	@SneakyThrows
	private void waitBeforeRetry() {
		heartbeat.ifPresent(Runnable::run);
		Thread.sleep(config.asyncWaitRetryMs);
		heartbeat.ifPresent(Runnable::run);
	}

	@Override
	public AsyncWaitOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		return new AsyncWaitOffset(0, empty(), parentMessageNumber, dateTime, 0);
	}

}
