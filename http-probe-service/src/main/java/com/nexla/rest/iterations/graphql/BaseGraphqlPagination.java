package com.nexla.rest.iterations.graphql;

import com.bazaarvoice.jolt.JsonUtils;
import com.bazaarvoice.jolt.exception.JsonUnmarshalException;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.RestIteration;
import com.nexla.rest.RestIterationOffset;
import com.nexla.rest.pojo.UrlBody;
import lombok.Builder;
import lombok.Data;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;

import java.util.*;

public abstract class BaseGraphqlPagination<T extends RestIterationOffset> extends RestIteration<T> {
	private static final String VARIABLES = "variables";


	public BaseGraphqlPagination(RestIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		super(config, senderPool, logger);
	}

	@SneakyThrows
	@Override
	public UrlBody getUrlBody(Map<String, String> substituteMap, T offset) {
		Map<String, String> substitutions = EntryStream
				.of(varReplacer.getSubstituteMapWithDates(offset.getDateTime()))
				.append(substituteMap)
				.toMap();

		T overriddenOffset = offset;

		if (offset instanceof GraphqlCursorIteration.GraphqlCursorOffset
				&& config.graphqlCursorTemplate.isPresent()
				&& ((GraphqlCursorIteration.GraphqlCursorOffset) offset).getIsInitialIteration()) {
			String cursor = substitutions.get(config.graphqlCursorTemplate.get());
			overriddenOffset = (T) GraphqlCursorIteration.GraphqlCursorOffset.withCursor((GraphqlCursorIteration.GraphqlCursorOffset) offset, Optional.of(cursor));
		}

		String rawPayload = varReplacer.getBody(substitutions);
		Object payload = null;
		try {
			Map<String, Object> parsedPayload = JsonUtils.jsonToMap(rawPayload);

			Map<String, Object> variables = (Map<String, Object>) parsedPayload.get(VARIABLES);
			if (variables == null) {
				variables = new HashMap<>();
				parsedPayload.put(VARIABLES, variables);
			}

			variables.putAll(variables(overriddenOffset));

			payload = parsedPayload;
		} catch (JsonUnmarshalException t) {
			payload = GraphqlPayload.builder()
					.query(rawPayload)
					.variables(variables(overriddenOffset))
					.build();
		}

		String url = RestIteration.encodeIfNeeded(varReplacer.getUrl(substitutions));
		String responseDataAdditional = varReplacer.getResponseDataAdditional(substitutions);
		return new UrlBody(url, JsonUtils.toJsonString(payload), responseDataAdditional);
	}


	protected abstract Map<String, Object> variables(T offset);

	@Builder
	@Data
	static
	class GraphqlPayload {
		private String query;
		private Map<String, Object> variables;
	}
}
