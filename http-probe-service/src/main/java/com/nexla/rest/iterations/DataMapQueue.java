package com.nexla.rest.iterations;

import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.redis.LookupUtils;
import com.nexla.redis.RedisAccessor;
import com.nexla.redis.RedisCreds;
import com.nexla.redis.RedisUtils;
import com.nexla.redis.dto.MasterEntry;
import com.nexla.rest.RestIteration;
import com.nexla.rest.iterations.offsets.DataMapQueueOffset;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.RestIterationResult;
import com.nexla.rest.pojo.ResultEntry;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;

import java.util.Map;
import java.util.Optional;

import static com.nexla.common.StreamUtils.batches;
import static com.nexla.redis.RedisUtils.BATCH_SIZE;
import static com.nexla.redis.RedisUtils.NIL_VALUE;
import static java.util.Collections.emptyMap;
import static java.util.Collections.singletonList;
import static java.util.Optional.ofNullable;

public class DataMapQueue extends RestIteration<DataMapQueueOffset> {

	public DataMapQueue(RestIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		super(config, senderPool, logger);
	}

	@Override
	@SneakyThrows
	public RestIterationResult<DataMapQueueOffset> readRecords(Map<String, String> substituteMap, DataMapQueueOffset offset) {
		RedisCreds redisCreds = new RedisCreds(config.redisAuthConfig.hosts, config.redisAuthConfig.redisClusterEnabled,
				config.redisAuthConfig.password, config.redisAuthConfig.tlsContext);

		return RedisUtils.withRedis(redisCreds, (jedis) -> {
			Map<String, String> data = getNextDataMap(jedis, offset);
			ResultEntry<DataMapQueueOffset> resultEntry = new ResultEntry<>(Maps.newLinkedHashMap(data), 0, null, offset, emptyMap(), null);

			Optional<DataMapQueueOffset> newOffset = ofNullable(jedis.lindex(config.queueName, 0))
					.filter(key -> !NIL_VALUE.equalsIgnoreCase(key))
					.map(key -> new DataMapQueueOffset(offset.getQueueName(), offset.getParentMessageNumber(), offset.getDateTime()));

			return new RestIterationResult<>(
					singletonList(resultEntry),
					offset,
					newOffset,
					offset.getQueueName()
			);
		});
	}

	private Map<String, String> getNextDataMap(RedisAccessor jedis, DataMapQueueOffset offset) {
		String nextKey = jedis.rpop(offset.getQueueName());
		if (nextKey == null || NIL_VALUE.equals(nextKey)) {
			logger.warn("null key found, returning empty map");
			return emptyMap();
		} else {
			logger.debug("Fetching lookup key {}", nextKey);
			return jedis.hgetAll(nextKey);
		}
	}

	@Override
	public DataMapQueueOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		if (config.createQueue) {
			createDataMapQueue(config);
		}
		return new DataMapQueueOffset(config.queueName, parentMessageNumber, dateTime);
	}

	@Override
	public String getPartition() {
		return config.queueName;
	}

	@Override
	public Class<DataMapQueueOffset> getOffsetClass() {
		return DataMapQueueOffset.class;
	}

	private void createDataMapQueue(RestIterationConfig config) {
		RedisCreds redisCreds = new RedisCreds(config.redisAuthConfig.hosts, config.redisAuthConfig.redisClusterEnabled,
				config.redisAuthConfig.password, config.redisAuthConfig.tlsContext);
		LookupUtils.withLookupAndRedis(redisCreds, config.redisMapId, (lookup, jedis) -> {

			MasterEntry masterEntry = lookup.getMasterEntry();
			jedis.del(config.queueName);

			String version = masterEntry.currentVersion != null ? masterEntry.currentVersion : "1";

			logger.info("Creating data map queue for lookup {}. Putting all entry keys to {}",
					lookup.getId(), config.queueName);

			lookup.getPrimaryKeys(masterEntry, version)
					.chain(batches(BATCH_SIZE))
					.filter(batch -> !batch.isEmpty())
					.map(batch -> batch.toArray(new String[0]))
					.forEach(batch -> {
						logger.debug("Pushing keys to {}. Keys: {}", config.queueName, batch);
						jedis.lpush(config.queueName, batch);
					});
		});
	}

	@Override
	public ParsedData createCallResult(
			Optional<byte[]> responseBytes,
			HttpHeaders headers,
			EntryStream<Integer, NexlaMessage> records,
			DataMapQueueOffset offset,
			String url
	) {
		throw new UnsupportedOperationException();
	}

	@Override
	public boolean urlDateIsChanged() {
		return false;
	}

}
