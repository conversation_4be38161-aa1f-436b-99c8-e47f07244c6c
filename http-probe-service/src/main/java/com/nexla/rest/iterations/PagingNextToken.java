package com.nexla.rest.iterations;

import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.common.time.VarUtils;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.RestIteration;
import com.nexla.rest.RestIterationOffset;
import com.nexla.rest.VarReplacer;
import com.nexla.rest.iterations.offsets.PagingNextTokenOffset;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.ResultEntry;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

import static java.util.Optional.empty;
import static java.util.Optional.of;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

public class PagingNextToken extends RestIteration<PagingNextTokenOffset> {

	public static final String FAKE_TOKEN = "";

	public PagingNextToken(RestIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		super(config, senderPool, logger);
	}

	@Override
	public Class<PagingNextTokenOffset> getOffsetClass() {
		return PagingNextTokenOffset.class;
	}

	@Override
	@SneakyThrows
	public Map<String, String> getParameters(PagingNextTokenOffset prevCtx) {
		Map<String, String> params = Maps.newHashMap();

		if (config.paramIdMacro.isPresent()) {
			return params;
		}

		prevCtx.getResponseToken().ifPresent(token -> params.put(config.paramId, token));
		return params;
	}

	@Override
	public String replaceWithInitRequestIfNeeded(String url, RestIterationOffset offset) {
		Optional<String> urlTemplateInitRequest = config.urlTemplateInitRequest;
		PagingNextTokenOffset nextTokenOffset = (PagingNextTokenOffset) offset;
		AtomicBoolean isFirstRequest = nextTokenOffset.getIsFirstRequest();

		if (urlTemplateInitRequest.isPresent() && isFirstRequest.get()) {
			// apply macro replacement to the initial URL
			VarUtils.VarInfo initialUrl = VarUtils.processStringWithVars(urlTemplateInitRequest.get());
			Map<String,String> substitutionMap = macroSubstitutionMap(nextTokenOffset);
			substitutionMap.putAll(VarUtils.getSubstitutionDates(config.dateTimeUnit, config.dateFormat, initialUrl.variables, offset.getDateTime()));
			String replacedUrl = VarUtils.replaceVars(initialUrl, substitutionMap);

			isFirstRequest.set(false);
			if (!config.skipUrlEncoding) {
				return encodeIfNeeded(replacedUrl);
			} else {
				return replacedUrl;
			}
		}
		return super.replaceWithInitRequestIfNeeded(url, offset);
	}

	@Override
	public Map<String, String> macroSubstitutionMap(PagingNextTokenOffset offset) {
		Map<String, String> substitutionMacroMap = new HashMap<>();
		if (config.paramIdMacro.isEmpty()) {
			return substitutionMacroMap;
		}
		Optional<String> responseToken = offset.getResponseToken();
		responseToken.ifPresent(token -> substitutionMacroMap.put(config.paramIdMacro.get(), token));
		return substitutionMacroMap;
	}

	@Override
	public ParsedData createCallResult(
		Optional<byte[]> responseBytes,
		HttpHeaders headers,
		EntryStream<Integer, NexlaMessage> recordStream,
		PagingNextTokenOffset offset,
		String url
	) {
		var recordStreamMap = recordStream.toMap();

		List<ResultEntry> records = EntryStream.of(recordStreamMap)
			.mapKeyValue((offsetOnPage, data) -> {
				PagingNextTokenOffset off = new PagingNextTokenOffset(
					offsetOnPage,
					of(recordStreamMap.size()),
					offset.getResponseToken(),
					offset.getParentMessageNumber(),
					offset.getDateTime()
				);
				return new ResultEntry(data.getRawMessage(), offsetOnPage, offset.getResponseToken().orElse(FAKE_TOKEN), off, headers.toSingleValueMap(), null);
			})
			.toList();

		Optional<PagingNextTokenOffset> nextCallContext =
			extractMatchesByPath(responseBytes, config.responseNextTokenDataPath.get())
				.filter(Objects::nonNull)
				.map(Object::toString)
				.findFirst()
				.filter(nextToken -> !records.isEmpty() && isValidToken(offset, nextToken))
				.map(nextToken -> new PagingNextTokenOffset(
					0,
					empty(),
					of(nextToken),
					offset.getParentMessageNumber(),
					offset.getDateTime()))
				.filter(o -> !offset.getResponseToken().orElse(FAKE_TOKEN).equals(config.endTokenTo.orElse(null)));

		return new ParsedData(records, offset, nextCallContext);
	}

	private boolean isValidToken(PagingNextTokenOffset offset, String nextToken) {
		return isNotEmpty(nextToken) && offset
			.getResponseToken()
			.map(tok -> !nextToken.equals(tok))
			.orElse(true);
	}

	@Override
	public PagingNextTokenOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		return new PagingNextTokenOffset(0,
				empty(),
				empty(),
				new AtomicBoolean(true),
				parentMessageNumber,
				dateTime);
	}

}
