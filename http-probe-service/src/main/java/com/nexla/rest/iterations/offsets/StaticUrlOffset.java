package com.nexla.rest.iterations.offsets;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.rest.RestIterationOffset;
import lombok.Getter;
import org.joda.time.DateTime;

import java.util.Optional;

@Getter
public class StaticUrlOffset extends RestIterationOffset {

	public StaticUrlOffset(
		@JsonProperty("messageNumber") Integer messageNumber,
		@JsonProperty("pageSize") Optional<Integer> pageSize,
		@JsonProperty("parentMessageNumber") Integer parentMessageNumber,
		@JsonProperty("dateTime") DateTime dateTime
	) {
		super(messageNumber, pageSize, parentMessageNumber, dateTime);
	}
}