package com.nexla.rest.iterations.offsets;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.rest.RestIterationOffset;
import lombok.Getter;
import org.joda.time.DateTime;

import java.util.Optional;

@Getter
public class ResponseIdStringOffset extends RestIterationOffset {

	private final Optional<String> stringId;

	public ResponseIdStringOffset(
		@JsonProperty("stringId") Optional<String> stringId,
		@JsonProperty("parentMessageNumber") Integer parentMessageNumber,
		@JsonProperty("dateTime") DateTime dateTime
	) {
		super(0, Optional.empty(), parentMessageNumber, dateTime);
		this.stringId = stringId;
	}

}