package com.nexla.soap;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.joda.JodaModule;
import lombok.Getter;
import lombok.SneakyThrows;
import org.joda.time.DateTime;

import java.beans.Transient;
import java.util.Optional;

import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.datetime.DateTimeUtils.validateTZ;
import static java.util.Optional.empty;
import static java.util.Optional.of;

@Getter
public class SoapIterationOffset {

	public final static Optional<SoapIterationOffset> NO_OFFSET_DATA = empty();
	public final static ObjectMapper OBJECT_MAPPER = new ObjectMapper();
	public final static SoapIterationOffset TERMINAL_OFFSET = new SoapIterationOffset();

	static {
		Jdk8Module module = new Jdk8Module();
		module.configureAbsentsAsNulls(true);
		OBJECT_MAPPER.registerModule(module);
		OBJECT_MAPPER.registerModule(new JodaModule());
		OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
	}

	private final DateTime dateTime;
	private final Integer parentMessageNumber;
	private final Integer messageNumber;
	private final Optional<Integer> pageSize;
	private final Optional<Boolean> isTerminal;

	public SoapIterationOffset(Integer messageNumber, Optional<Integer> pageSize, Integer parentMessageNumber, DateTime dateTime) {
		this.messageNumber = messageNumber;
		this.pageSize = pageSize;
		this.parentMessageNumber = parentMessageNumber;
		this.dateTime = validateTZ(dateTime);
		this.isTerminal = empty();
	}

	private SoapIterationOffset() {
		this.parentMessageNumber = -1;
		this.messageNumber = -1;
		this.pageSize = empty();
		this.dateTime = nowUTC();
		this.isTerminal = of(true);
	}

	@SneakyThrows
	public String toJson() {
		return OBJECT_MAPPER.writeValueAsString(this);
	}

	@SneakyThrows
	protected static <T extends SoapIterationOffset> T fromJson(String json, Class<T> valueType) {
		return OBJECT_MAPPER.readValue(json, valueType);
	}

	@Transient
	public boolean isTerminal() {
		return isTerminal.orElse(false);
	}

}

