package com.nexla.soap;

import com.nexla.common.NexlaMessage;
import com.nexla.common.exception.ProbeRetriableException;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.HttpCallParameters;
import com.nexla.connector.config.rest.RestHeaders;
import com.nexla.connector.config.soap.SoapIterationConfig;
import com.nexla.probe.http.HttpSenderResponse;
import com.nexla.probe.http.RequestSender;
import com.nexla.probe.http.SoapConnectorService;
import com.nexla.rest.pojo.UrlBody;
import com.nexla.soap.pojo.SoapIterationResult;
import com.nexla.soap.pojo.SoapParsedData;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.Getter;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.joda.time.DateTime;
import org.springframework.http.MediaType;

import java.util.Map;
import java.util.Optional;

import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.common.StreamUtils.zipWithIndices;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;

@Getter
public abstract class SoapIteration<T extends SoapIterationOffset> {

	protected final SoapIterationConfig config;
	protected final NexlaLogger logger;
	protected final NexlaPool<RequestSender> senderPool;

	public SoapIteration(SoapIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		this.config = config;
		this.logger = logger;
		this.senderPool = senderPool;
	}

	protected abstract Class<T> getOffsetClass();

	protected abstract UrlBody getRequestUrlBody(Map<String, String> fullSubstituteMap);

	protected abstract SoapParsedData createCallResult(
		EntryStream<Integer, NexlaMessage> recordStream,
		T prevCtx
	);

	protected abstract T createStartOffset(Integer parentMessageNumber, DateTime dateTime);

	public boolean urlDateIsChanged() {
		return false;
	}

	public SoapIterationConfig getConfig() {
		return config;
	}

	public String getPartition() {
		return config.soapConf.getWsdlUrl();
	}

	public NexlaLogger getLogger() {
		return logger;
	}

	public String getCode() {
		return config.key;
	}

	@SneakyThrows
	public SoapIterationResult readRecords(Map<String, String> substituteMap, T offset) {
		UrlBody urlBody = getUrl(substituteMap, offset);
		Optional<byte[]> result = senderPool.withPooledObject(sender -> readResponse(sender, urlBody)).getBody();
		return bytesToResult(offset, urlBody.getUrl(), result);
	}

	@SneakyThrows
	public Optional<byte[]> readBytes(Map<String, String> substituteMap, T offset) {
		UrlBody urlBody = getUrl(substituteMap, offset);
		return senderPool.withPooledObject(sender -> readResponse(sender, urlBody)).getBody();
	}

	public UrlBody getUrl(Map<String, String> substituteMap, T offset) {
		return getRequestUrlBody(substituteMap);
	}

	public SoapIterationResult bytesToResult(T offset, String url, Optional<byte[]> responseBytes) {
		return responseBytes
			.map(this::readData)
			.map(this::filterErrors)
			.map(r -> filterMessagesByNum(r, offset))
			.map(r -> createCallResult(r, offset))
			.map(r -> new SoapIterationResult(r.getEntries(), r.getOffset(), r.getNextOffset(), url))
			.orElse(new SoapIterationResult(Collections.emptyList(), offset, Optional.empty(), url));
	}

	private EntryStream<Integer, NexlaMessage> filterMessagesByNum(
		StreamEx<NexlaMessage> recordStream,
		T offset
	) {
		return zipWithIndices(recordStream)
			.mapKeys(key -> key + 1) // messages numeration on page starts from 1
			.filterKeys(offsetOnPage -> {
				Integer messageNumber = ofNullable(offset.getMessageNumber()).orElse(0);
				return offsetOnPage >= messageNumber;
			});
	}

	private StreamEx<NexlaMessage> readData(byte[] bytes) {
		return SoapConnectorService
			.readData(
				config.responseDataPath,
				config.charsetDetectionThreshold,
				bytes
			)
			.map(NexlaMessage::new);
	}

	private StreamEx<NexlaMessage> filterErrors(StreamEx<NexlaMessage> recordStream) {
		return recordStream;
	}

	public HttpSenderResponse readResponse(RequestSender sender, UrlBody urlBody) {
		try {
			RestHeaders headers = new RestHeaders(Collections.emptyMap(), ofNullable(MediaType.APPLICATION_XML_VALUE), empty());
			// XXX: always does URL encoding
			HttpCallParameters cp = new HttpCallParameters(urlBody.getUrl(), false, config.method, ofNullable(urlBody.getBody()), empty(), headers);
			return sender.send(cp);
		} catch (Exception exp) {
			logException(exp, urlBody.getUrl(), ofNullable(urlBody.getBody()));
			throw new ProbeRetriableException(urlBody.getUrl(), exp);
		}
	}

	public void logException(Exception exp, String url, Optional<String> body) {
		logger.error("Failed to send request for resource_type={} resource_id={} url={} body={} error: ",
			SOURCE, config.sourceId, url, body, exp);
	}

	public T offsetFromJson(String json) {
		return SoapIterationOffset.fromJson(json, getOffsetClass());
	}

	public Integer getRetryDelay() {
		return config.retryDelay;
	}

	public Integer getRetryThreshold() {
		return config.retryThreshold;
	}
}