package com.nexla.soap.wsdl;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;
import java.util.Optional;

@Data
@JsonInclude(JsonInclude.Include.NON_ABSENT)
public class WsdlParameter {

	private final String path;
	private final String type;
	private final Optional<List<String>> possibleValues;
	private final Optional<String> pattern;

	private final Optional<Integer> maxLength;
	private final Optional<Integer> maxDigits;
	private final Optional<Integer> fractionDigits;
	private final Optional<Integer> minLength;
	private final Optional<Integer> minInclusive;
	private final Optional<Integer> minExclisive;
	private final Optional<Integer> maxInclusive;
	private final Optional<Integer> maxExclusive;
	private final Optional<Integer> minOccurs;
	private final Optional<Integer> maxOccurs;

}
