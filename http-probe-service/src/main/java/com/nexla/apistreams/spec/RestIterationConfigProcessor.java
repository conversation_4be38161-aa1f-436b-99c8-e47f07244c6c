package com.nexla.apistreams.spec;

import com.google.common.collect.Maps;
import com.nexla.connector.config.rest.RestIterationConfig;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.nexla.connector.properties.RestConfigAccessor.*;

public class RestIterationConfigProcessor {
    
    public static RestIterationConfig substituteRestIterationConfigUrlParams(RestIterationConfig config, Map<String, String> replacementParams) {
        return new RestIterationConfig(replaceParamsInUrl(config.originals(), replacementParams),
                config.credentialsDecryptKey,
                config.unitTest,
                config.sourceId,
                config.charsetDetectionThreshold,
                config.authConfig);
    }

    public static RestIterationConfig substituteRestIterationResponseDataPath(RestIterationConfig config, Map<String, String> replacementParams) {
        return new RestIterationConfig(replaceParamsForResponseParams(config.originals(), replacementParams),
                config.credentialsDecryptKey,
                config.unitTest,
                config.sourceId,
                config.charsetDetectionThreshold,
                config.authConfig);
    }

    public static Map<String, Object> replaceParamsInUrl(Map<String, Object> originals, Map<String, String> replacementMap) {
        if (!originals.containsKey(URL_TEMPLATE)) {
            return originals; // code.container steps may not have URL_TEMPLATE defined
        }

        Map<String, Object> updatedOriginals = new HashMap<>(originals);
        String url = originals.get(URL_TEMPLATE).toString();
        
        for (Map.Entry<String, String> entry : replacementMap.entrySet()) {
            String placeholder = "{" + entry.getKey() + "}";
            if (url.contains(placeholder)) {
                url = url.replace(placeholder, entry.getValue());
            }
        }
        updatedOriginals.put(URL_TEMPLATE, url);
        return updatedOriginals;
    }

    public static Map<String, Object> replaceParamsForResponseParams(Map<String, Object> originals, Map<String, String> replacementMap) {
        Optional<String> maybeUrl = Optional.ofNullable(originals.get(RESPONSE_DATA_PATH)).map(Object::toString);
        Optional<String> maybeResponseDataAdditional = Optional.ofNullable(originals.get(RESPONSE_DATA_ADDITIONAL)).map(Object::toString);
        Map<String, Object> updatedResponseDataPath = replacePlaceholders(maybeUrl, replacementMap)
            .map(str -> {
                Map<String, Object> updatedOriginals = Maps.newHashMap(originals);
                updatedOriginals.put(RESPONSE_DATA_PATH, str);
                return updatedOriginals;
            })
            .orElse(originals);

        return replacePlaceholders(maybeResponseDataAdditional, replacementMap)
            .map(str -> {
                Map<String, Object> updatedOriginals = Maps.newHashMap(updatedResponseDataPath);
                updatedOriginals.put(RESPONSE_DATA_ADDITIONAL, str);
                return updatedOriginals;
            })
            .orElse(updatedResponseDataPath);
    }

    private static Optional<String> replacePlaceholders(Optional<String> placeholdersString, Map<String, String> replacementMap) {
        if (placeholdersString.isPresent()) {
            String placeholders = placeholdersString.get();

            for (Map.Entry<String, String> entry : replacementMap.entrySet()) {
                String placeholder = "{" + entry.getKey() + "}";
                if (placeholders.contains(placeholder)) {
                    placeholders = placeholders.replace(placeholder, String.valueOf(entry.getValue()));
                }
            }
            return Optional.of(placeholders);
        }

        return Optional.empty();
    }
}
