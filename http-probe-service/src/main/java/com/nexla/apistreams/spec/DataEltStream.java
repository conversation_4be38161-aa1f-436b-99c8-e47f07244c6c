package com.nexla.apistreams.spec;

import com.nexla.connect.common.SourceRecordCreator;
import com.nexla.connector.config.api_streams.ApiStreamsSourceConnectorConfig;
import com.nexla.rest.pojo.RestIterationResult;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public class DataEltStream extends BaseEltStream {

  private final BaseLineEltStream refBaseStream;
  private final List<String> primaryKeys;

  private final Optional<Object> metadata;

  public DataEltStream(Map<String, String> streamParams,
                       BaseLineEltStream refBaseStream,
                       String streamName,
                       ApiStreamsSourceConnectorConfig rootConfig,
                       Long runId,
                       List<String> primaryKeys,
                       Optional<Object> metadata) {
    super(streamParams, streamName, rootConfig, runId);
    this.refBaseStream = refBaseStream;
    this.primaryKeys = primaryKeys;
    this.metadata = metadata;
  }

  @Override
  public List<SourceRecordCreator> execute() {
    return refBaseStream.execute(streamName, primaryKeys, metadata);
  }

  @Override
  public Optional<RestIterationResult> getLastResult() {
    return this.refBaseStream.getLastResult();
  }
}
