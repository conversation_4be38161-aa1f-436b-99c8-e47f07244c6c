package com.nexla.apistreams.spec;

import com.bazaarvoice.jolt.JsonUtils;
import com.google.common.collect.Maps;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.datetime.DateTimeUtils;
import com.nexla.common.tracker.SourceItem;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.SourceRecordCreator;
import com.nexla.connector.config.api_streams.ApiStreamsSourceConnectorConfig;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.rest.RestIteration;
import com.nexla.rest.RestIterationChainBuilder;
import com.nexla.rest.RestIterationContext;
import com.nexla.rest.RestIterationOffset;
import com.nexla.rest.pojo.RestIterationResult;
import com.nexla.rest.pojo.ResultEntry;
import lombok.Getter;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.net.util.Base64;
import org.apache.kafka.connect.source.SourceRecord;
import org.joda.time.DateTime;
import scala.Function2;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.nexla.common.MetricUtils.calcBytes;
import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.connector.properties.FileConfigAccessor.PARTITION_KEY;
import static com.nexla.rest.RestIterationOffset.TERMINAL_OFFSET;
import static java.util.Collections.EMPTY_LIST;
import static org.apache.kafka.connect.data.Schema.STRING_SCHEMA;
import static org.joda.time.DateTime.now;
import static org.joda.time.DateTimeZone.UTC;

public class BaseLineEltStream extends BaseEltStream {

    private List<RestIteration> restIterations;
    private RestIterationChainBuilder chainBuilder;
    private List<RestIterationContext> callerContexts;
    private RestIterationContext lastContext;
    @Getter
    private List<RestIterationConfig> restIterationConfigs;
    public static final String VALUE = "value";

    public BaseLineEltStream(Map<String, String> streamParams,
                             RestIterationChainBuilder chainBuilder,
                             List<RestIterationConfig> restIterationConfigs,
                             String streamName,
                             ApiStreamsSourceConnectorConfig rootConfig,
                             Long runId) {
        super(streamParams, streamName, rootConfig, runId);
        this.chainBuilder = chainBuilder;
        this.restIterations = chainBuilder.buildIterations(restIterationConfigs);
        this.restIterationConfigs = restIterationConfigs;
        initContextChain(nowUTC());
    }

    public BaseLineEltStream(BaseLineEltStream source, RestIterationChainBuilder chainBuilder) {
        super(source.streamParams, source.streamName, source.rootConfig, source.runId);
        this.chainBuilder = chainBuilder;
        this.restIterations = new ArrayList<>(source.restIterations);
        this.callerContexts = new ArrayList<>(source.callerContexts);
        this.lastContext = source.lastContext;
    }

    private void initContextChain(DateTime dateTime) {
        this.callerContexts = chainBuilder.buildContexts(restIterations, dateTime);
        this.lastContext = callerContexts.get(callerContexts.size() - 1);
    }

    @Override
    @SneakyThrows
    public List<SourceRecordCreator> execute() {
        return this.execute(streamName, Collections.emptyList(), Optional.empty());
    }

    @Override
    public Optional<RestIterationResult> getLastResult() {
        return lastContext.getLastResult();
    }

    public List<SourceRecordCreator> execute(String streamName, List<String> primaryKeys, Optional<Object> metadata) {
        return lastContext
                .iterateOverContext()
                .flatMap(result -> processResult(lastContext, streamName, primaryKeys, metadata))
                .orElse(EMPTY_LIST);
    }

    private Optional<List<SourceRecordCreator>> processResult(RestIterationContext lastContext,
                                                              String callerStreamName,
                                                              List<String> primaryKeys,
                                                              Optional<Object> metadata) {
        return lastContext.getLastResult().map(result -> {
            Map<String, String> contextOffsets = StreamEx.of(callerContexts)
                    .filter(context -> context.getLastResult().isPresent())
                    .toMap(
                            context -> context.getRestIteration().getCode(),
                            context -> context.getLastResult().get().getOffset().toJson()
                    );
            List<ResultEntry> entries = result.getEntries();

            Stream<SourceRecordCreator> sourceRecordsStream = entries.stream()
                    .flatMap(entry -> {
                        Map<String, String> sourceOffset = Maps.newHashMap(contextOffsets);

                        RestIterationOffset nextOffset = lastContext.getNextOffset().orElse(TERMINAL_OFFSET);
                        sourceOffset.put(lastContext.getRestIteration().getCode(), nextOffset.toJson());

                        if (isMultiResponse(entry.getDataMap())) {
                            List<LinkedHashMap<String, Object>> innerMaps = (List<LinkedHashMap<String, Object>>) entry.getDataMap().get(VALUE);
                            return innerMaps.stream().map(innerMap -> createRecord(
                                    lastContext.getRestIteration(),
                                    toEltRecordData(innerMap, result.getUrl(), callerStreamName, primaryKeys, metadata, rootConfig.singleSchema),
                                    Long.valueOf(entry.getOffsetOnPage()),
                                    entry.getNexlaMetaRangeFrom(),
                                    result.getUrl(),
                                    sourceOffset,
                                    result.getByteCounter()
                            ));
                        } else {
                            return Stream.of(createRecord(
                                    lastContext.getRestIteration(),
                                    toEltRecordData(entry.getDataMap(), result.getUrl(), callerStreamName, primaryKeys, metadata, rootConfig.singleSchema),
                                    Long.valueOf(entry.getOffsetOnPage()),
                                    entry.getNexlaMetaRangeFrom(),
                                    result.getUrl(),
                                    sourceOffset,
                                    result.getByteCounter()
                            ));
                        }
                    });

            return sourceRecordsStream.collect(Collectors.toList());
        });
    }

    private boolean isMultiResponse(LinkedHashMap<String, Object> original) {
        return original.size() == 1 && original.containsKey(VALUE);
    }

    private SourceRecordCreator createRecord(
            RestIteration caller,
            LinkedHashMap<String, Object> dataMap,
            Long offset,
            Object nexlaMetaRangeFrom,
            String url,
            Map<String, ?> sourceOffset,
            AtomicLong byteCounter
    ) {
        String sourceKey = nexlaMetaRangeFrom != null ? String.format("%s, from=%s", url, nexlaMetaRangeFrom) : url;
        long now = now(UTC).getMillis();

        Function2<Integer, String, NexlaMessage> nexlaMessageCreator = (dataSetId, dataSetTopic) -> {
            SourceItem nexlaSourceTrackerIdItem = SourceItem.fullTracker(
                    rootConfig.sourceId,
                    dataSetId,
                    new String(Base64.encodeBase64(url.getBytes())),
                    offset != null ? offset : 0,
                    rootConfig.version,
                    now);

            NexlaMetaData metaData = new NexlaMetaData(
                    ConnectionType.API_STREAMS,
                    now,
                    offset,
                    sourceKey,
                    dataSetTopic,
                    SOURCE,
                    rootConfig.sourceId,
                    false,
                    new Tracker(Tracker.TrackerMode.FULL, nexlaSourceTrackerIdItem),
                    runId);

            return new NexlaMessage(dataMap, metaData);
        };

        Function2<Integer, String, SourceRecord> sourceRecordCreator = (dataSetId, dataSetTopic) -> {
            NexlaMessage message = nexlaMessageCreator.apply(dataSetId, dataSetTopic);

            byteCounter.addAndGet(calcBytes(message.toJsonString()));

            return new SourceRecord(
                    Collections.singletonMap(PARTITION_KEY, caller.getPartition()),
                    sourceOffset,
                    dataSetTopic,
                    STRING_SCHEMA, JsonUtils.toJsonString(message));
        };
        return new SourceRecordCreator(dataMap, sourceRecordCreator, nexlaMessageCreator);
    }

    protected DateTime nowUTC() {
        return DateTimeUtils.nowUTC();
    }
}
