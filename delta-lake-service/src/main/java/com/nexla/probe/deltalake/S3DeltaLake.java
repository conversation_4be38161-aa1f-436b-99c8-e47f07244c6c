package com.nexla.probe.deltalake;

import com.nexla.connector.config.deltalake.DeltaLakeAuthConfig;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.file.service.FileConnectorService;
import com.nexla.probe.s3.S3ConnectorService;

import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

import static com.nexla.connector.config.file.AWSAuthConfig.toBucketPrefix;
import static com.nexla.connector.config.file.FileSourceConnectorConfig.normalizePath;

public class S3DeltaLake implements DeltaLakeAware<AWSAuthConfig> {

    @Override
    public FileConnectorService<AWSAuthConfig> getFileConnectorService() {
        return new S3ConnectorService();
    }

    @Override
    public BaseAuthConfig getDataLakeAuthConfig(DeltaLakeAuthConfig config) {
        return config.asAWS();
    }

    @Override
    public Map<String, String> getDataLakeConfig(DeltaLakeAuthConfig config) {
        Map<String, String> configs = new HashMap<>();
        final AWSAuthConfig awsAuthConfig = config.asAWS();
        configs.put("spark.hadoop.fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem");
        configs.put("spark.hadoop.fs.s3a.aws.credentials.provider", "org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider");
        configs.put("fs.s3a.access.key", awsAuthConfig.accessKeyId);
        configs.put("fs.s3a.secret.key", awsAuthConfig.secretKey);

        return configs;
    }

    @Override
    public String getDeltaLakePath(FileConnectorAuth connectorConfig, String path) {
        AWSAuthConfig.BucketPrefix bucketPrefix = toBucketPrefix(connectorConfig.getPath(), true);
        return "s3a://" + normalizePath(Paths.get(bucketPrefix.bucket, path).toString());
    }
}
