package com.nexla.connector.iceberg.sink;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.nexla.common.logging.TimeLogger;
import com.nexla.connect.common.cdc.DebeziumData;
import com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig;
import com.nexla.probe.iceberg.IcebergS3TablesConnectorService;

import io.tabular.iceberg.connect.data.Operation;
import io.tabular.iceberg.connect.data.SchemaUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.iceberg.PartitionSpec;
import org.apache.iceberg.Schema;
import org.apache.iceberg.Table;
import org.apache.iceberg.UpdatePartitionSpec;
import org.apache.iceberg.UpdateSchema;
import org.apache.iceberg.catalog.Catalog;
import org.apache.iceberg.catalog.Namespace;
import org.apache.iceberg.catalog.TableIdentifier;
import org.apache.iceberg.expressions.Expressions;
import org.apache.iceberg.transforms.Transform;
import org.apache.iceberg.types.Type;
import org.apache.iceberg.types.Types;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.slf4j.Logger;
import org.sparkproject.guava.base.Objects;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.nexla.connector.iceberg.sink.IcebergCdcSinkTask.NEXLA_CDC_PRIMARY_KEY;
import static com.nexla.connector.iceberg.sink.IcebergCdcSinkTask.SPARK_CATALOG_TABLE_PREFIX;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@AllArgsConstructor
public class IcebergCdcTableManager {

  private final Cache<String, Boolean> existingTables = CacheBuilder
      .newBuilder()
      .expireAfterWrite(12, TimeUnit.HOURS)
      .build();
  private final Cache<String, Schema> tableSchema = CacheBuilder
      .newBuilder()
      .expireAfterWrite(15, TimeUnit.MINUTES)
      .build();
  private final Cache<String, PartitionSpec> tablePartitionSpec = CacheBuilder
      .newBuilder()
      .expireAfterWrite(15, TimeUnit.MINUTES)
      .build();

  private final Map<String, LinkedList<String>> tableIdsByRecordValue = Maps.newConcurrentMap();

  private final Set<String> processedTableIds = Sets.newConcurrentHashSet();

  private final IcebergSinkConnectorConfig config;
  private final Catalog catalog;

  private final SparkSession sparkSession;
  private final Logger logger;

  @SneakyThrows
  public void processTable(String fullTableName, DebeziumData debeziumData, Optional<IcebergS3TablesConnectorService> s3TablesProbe) {
    boolean tableExists = tableExists(fullTableName);
    logger.info("M=processTable, tableName={}, tableExists={}", fullTableName, tableExists);

    if (!tableExists) {
      try (TimeLogger ignored = new TimeLogger(logger, "createDestination")) {
        createDestination(fullTableName, debeziumData.getSchema(), debeziumData.getPrimaryKey(), s3TablesProbe);
        existingTables.put(fullTableName, true);
      }
    } else {
      try (TimeLogger ignored = new TimeLogger(logger, "processSchemaEvolution")) {
        processSchemaEvolution(fullTableName, debeziumData);
      }
      try (TimeLogger ignored = new TimeLogger(logger, "processPartitionEvolution")) {
        processPartitionEvolution(fullTableName);
      }
    }
  }

  @SneakyThrows
  private Schema getTableSchema(String fullTableName) {
    return tableSchema.get(fullTableName, () -> catalog.loadTable(getTableIdentifier(fullTableName)).schema());
  }

  @SneakyThrows
  private boolean tableExists(String fullTableName) {
    return existingTables.get(fullTableName, () -> catalog.tableExists(getTableIdentifier(fullTableName)));
  }

  private TableIdentifier getTableIdentifier(String fullTableName) {
    String[] tableNameList = fullTableName.split("\\.");
    if (tableNameList.length == 1) {
      return TableIdentifier.of(tableNameList[0]);
    }
    return TableIdentifier.of(Namespace.of(tableNameList[0]), tableNameList[1]);
  }

  @SneakyThrows
  private PartitionSpec getPartitionSpec(String fullTableName) {
    return tablePartitionSpec.get(fullTableName, () -> catalog.loadTable(getTableIdentifier(fullTableName)).spec());
  }

  public String getRecordPrimaryKey(String tableName, Operation operation, LinkedHashMap<String, Object> beforeData, LinkedHashMap<String, Object> afterData) {
    if (Operation.INSERT == operation) {
      String tableId = UUID.randomUUID().toString();
      updateProcessedRecordsMap(tableName, afterData, tableId);
      return tableId;
    }

    Optional<String> optionalTableId = getTableIdFromCache(tableName, beforeData);
    if (optionalTableId.isPresent()) {
      updateProcessedRecordsMap(tableName, afterData, optionalTableId.get());
      return optionalTableId.get();
    }

    StringBuilder queryBuilder = new StringBuilder("select * from " + SPARK_CATALOG_TABLE_PREFIX + tableName + " where 1=1 ");

    // call table manager to get table schema, use only table schema fields in the query
    // remove these columns from the query
    boolean tableExists = tableExists(tableName);
    if (tableExists) {
      // table schema might be null
      List<String> currentTableFields = getTableSchema(tableName).columns()
          .stream()
          .map(Types.NestedField::name)
          .collect(Collectors.toList());

      LinkedHashMap<String, Object> filteredBeforeData = StreamEx.of(beforeData.entrySet())
          .filter(entry -> currentTableFields.contains(entry.getKey()))
          .collect(LinkedHashMap::new,
              (map, entry) -> map.put(entry.getKey(), entry.getValue()),
              LinkedHashMap::putAll);

      Optional<String> tableIdFromFilteredData = getTableIdFromCache(tableName, filteredBeforeData);

      if (tableIdFromFilteredData.isPresent()) {
        String tableId = tableIdFromFilteredData.get();
        updateProcessedRecordsMap(tableName, afterData, tableId);
        return tableId;
      }

      appendFieldsToSparkQuery(queryBuilder, filteredBeforeData);
    } else {
      appendFieldsToSparkQuery(queryBuilder, beforeData);
    }

    String query = queryBuilder.toString();
    List<Row> result = sparkSession.sql(query).collectAsList();

    if (config.logVerbose) {
      logger.info("M=getRecordPrimaryKey, query={}, result={}", query, result);
    }

    List<String> unprocessedIds = StreamEx.of(result)
        .map(row -> row.getString(row.fieldIndex(NEXLA_CDC_PRIMARY_KEY)))
        .filter(id -> !processedTableIds.contains(buildKey(tableName, id)))
        .collect(Collectors.toList());

    if (CollectionUtils.isNotEmpty(unprocessedIds)) {
      String tableId = unprocessedIds.get(0);
      processedTableIds.add(buildKey(tableName, tableId));
      updateProcessedRecordsMap(tableName, afterData, tableId);
      return tableId;
    }

    if (tableExists) {
      Set<String> fieldsToDrop = getFieldsToDrop(getTableSchema(tableName), beforeData.keySet())
          .stream()
          .map(Types.NestedField::name)
          .collect(Collectors.toSet());

      if (CollectionUtils.isNotEmpty(fieldsToDrop)) {
        // search in cache removing fields to drop
        String hashValue = buildHashValueFromData(tableName, beforeData);

        Optional<String> keyFound = tableIdsByRecordValue.keySet()
            .stream()
            .filter(it -> it.startsWith(tableName))
            .map(it -> {
              Map<String, String> filteredMap = StreamEx.of(it.split("\\|"))
                  .map(str -> {
                    String[] split = str.split("=");
                    return split.length > 1 ? Pair.of(split[0], split[1]): Pair.of(split[0], split[0]);
                  })
                  .filter(pair -> !fieldsToDrop.contains(pair.getKey()) && !tableName.equalsIgnoreCase(pair.getKey()))
                  .toMap(Pair::getKey, Pair::getValue);

              String filteredHashData = buildHashValueFromData(tableName, Maps.newLinkedHashMap(filteredMap));

              if (filteredHashData.equals(hashValue)) {
                return it;
              }
              return null;
            })
            .filter(java.util.Objects::nonNull)
            .collect(Collectors.toSet())
            .stream()
            .findFirst();

        if (keyFound.isPresent()){
          var tableIds = tableIdsByRecordValue.get(keyFound.get());
          if (CollectionUtils.isNotEmpty(tableIds)) {
            String tableId = tableIds.pop();
            updateProcessedRecordsMap(tableName, afterData, tableId);
            return tableId;
          }
        }
      }
    }

    return null;
  }

  private void appendFieldsToSparkQuery(StringBuilder queryBuilder, LinkedHashMap<String, Object> filteredBeforeData) {
    filteredBeforeData.forEach((fieldName, fieldValue) -> queryBuilder.append(" AND ")
        .append(fieldName)
        .append(nonNull(fieldValue) ? " = ":" is ")
        .append(nonNull(fieldValue) ? "\"" + fieldValue + "\"": "null"));
  }

  private Optional<String> getTableIdFromCache(String tableName, LinkedHashMap<String, Object> data) {
    var tableIds = tableIdsByRecordValue.get(buildHashValueFromData(tableName, data));
    if (CollectionUtils.isNotEmpty(tableIds)) {
      return Optional.of(tableIds.pop());
    }
    return Optional.empty();
  }

  private void updateProcessedRecordsMap(String tableName, LinkedHashMap<String, Object> data, String tableId) {
    String afterDataKey = buildHashValueFromData(tableName, data);
    LinkedList<String> tableIds = tableIdsByRecordValue.getOrDefault(afterDataKey, Lists.newLinkedList());
    tableIds.add(tableId);
    tableIdsByRecordValue.put(afterDataKey, tableIds);
    if (config.logVerbose) {
      logger.info("M=updateProcessedRecordsMap, processedTableIds={}, tableIdsByRecordValue={}", processedTableIds, tableIdsByRecordValue);
    }
  }

  private String buildHashValueFromData(String tableName, LinkedHashMap<String, Object> data){
    String dataAsKeyValueString = data.keySet()
        .stream()
        .sorted()
        .map(fieldName -> String.format("%s=%s", fieldName, data.get(fieldName)))
        .collect(Collectors.joining("|"));

    return tableName + "|" + dataAsKeyValueString;
  }

  private String buildKey(String tableName, String id){
    return String.format("%s-%s", tableName, id);
  }

  private void processPartitionEvolution(String fullTableName) {
    PartitionSpec currentPartitionSpec = getPartitionSpec(fullTableName);
    PartitionSpec partitionSpec = config.partitionKeys.map(keys ->
        SchemaUtils.createPartitionSpec(getTableSchema(fullTableName), List.of(keys.split(",")))
    ).orElse(PartitionSpec.unpartitioned());

    if (partitionSpec.compatibleWith(currentPartitionSpec)) {
      return;
    }

    Set<PartitionFieldKey> newFields = partitionSpec.fields().stream()
        .map(field -> new PartitionFieldKey(field.sourceId(), field.transform(), field.name()))
        .collect(Collectors.toSet());

    Set<PartitionFieldKey> currentFields = currentPartitionSpec.fields().stream()
        .map(field -> new PartitionFieldKey(field.sourceId(), field.transform(), field.name()))
        .collect(Collectors.toSet());

    Set<PartitionFieldKey> toRemove = new HashSet<>(currentFields);
    toRemove.removeAll(newFields);

    Set<PartitionFieldKey> toAdd = new HashSet<>(newFields);
    toAdd.removeAll(currentFields);

    Table table = catalog.loadTable(getTableIdentifier(fullTableName));
    UpdatePartitionSpec updateSpec = table.updateSpec();
    for (PartitionFieldKey field : toRemove) {
      updateSpec.removeField(field.getName());
    }

    // FIXME: known issue, date based transforms (e.g. year(), day(), hour()) end up with incorrect values
    // only field identity transforms are a documented feature
    for (PartitionFieldKey field : toAdd) {
      Schema tableSchema = getTableSchema(fullTableName);
      updateSpec.addField(field.getName(),
          Expressions.transform(tableSchema.findField(field.getSourceId()).name(), field.getTransform()));
    }

    updateSpec.commit();
  }

  @SneakyThrows
  private void processSchemaEvolution(String fullTableName, DebeziumData debeziumData) {
    Schema newSchema = buildIcebergSchema(debeziumData.getSchema(), debeziumData.getPrimaryKey());
    Schema currentSchema = getTableSchema(fullTableName);
    Map<String, Types.NestedField> currentSchemaMap = currentSchema.columns()
        .stream()
        .collect(Collectors.toMap(Types.NestedField::name, Function.identity()));

    List<Types.NestedField> fieldsToAdd = Lists.newLinkedList();
    Set<Types.NestedField> fieldsToAlter = Sets.newHashSet();

    for (Types.NestedField newSchemaField : newSchema.columns()) {
      Types.NestedField currentSchemaField = currentSchemaMap.get(newSchemaField.name());
      if (isNull(currentSchemaField)) {
        logger.info("M=processSchemaEvolution, detected a new column {} to be added to iceberg table {}", newSchemaField.name(), fullTableName);
        fieldsToAdd.add(newSchemaField);
      } else if (newSchemaField.type() != currentSchemaField.type()) {
        logger.info("M=processSchemaEvolution, detected a data type change for column {} from {} to {} in table {}", newSchemaField.name(), currentSchemaField.type(), newSchemaField.type(), fullTableName);
        fieldsToAlter.add(newSchemaField);
      }
    }

    Set<String> newSchemaFieldNames = newSchema.columns()
        .stream()
        .map(Types.NestedField::name)
        .collect(Collectors.toSet());

    Set<Types.NestedField> fieldsToDrop = getFieldsToDrop(currentSchema, newSchemaFieldNames);

    if (!fieldsToDrop.isEmpty()) {
      logger.info("M=processSchemaEvolution, detected {} columns to be dropped from table {}", fieldsToDrop, fullTableName);
    }

    UpdateSchema updateSchema = catalog.loadTable(getTableIdentifier(fullTableName)).updateSchema();

    fieldsToAdd.forEach(field -> {
      logger.info("M=processSchemaEvolution, tableName={}, adding new column {} ", fullTableName, field.name());
      updateSchema.addColumn(field.name(), field.type());
    });

    fieldsToAlter.forEach(field -> {
      logger.info("M=processSchemaEvolution, tableName={}, modifying column {} to {} type", fullTableName, field.name(), field.type());
      updateSchema.updateColumn(field.name(), field.type().asPrimitiveType());
    });

    fieldsToDrop.forEach(field -> {
      logger.info("M=processSchemaEvolution, tableName={}, dropping column {}", fullTableName, field.name());
      updateSchema.deleteColumn(field.name());
    });

    updateSchema.commit();
    tableSchema.put(fullTableName, catalog.loadTable(getTableIdentifier(fullTableName)).schema());
  }


  private Set<Types.NestedField> getFieldsToDrop(Schema currentSchema, Set<String> newSchemaFieldNames) {
    return currentSchema.columns()
        .stream().filter(it -> !newSchemaFieldNames.contains(it.name()))
        .collect(Collectors.toSet());
  }

  private void createDestination(String fullTableName,
                                 LinkedHashMap<String, Map<String, String>> schema,
                                 List<String> primaryKey,
                                 Optional<IcebergS3TablesConnectorService> s3TablesProbe) {
    logger.info("M=createDestination, tableName={}", fullTableName);
    Schema icebergSchema = buildIcebergSchema(schema, primaryKey);
    logger.info("M=createDestination, schema {} for table {} created", icebergSchema, fullTableName);

    PartitionSpec spec = config.partitionKeys.map(keys ->
        SchemaUtils.createPartitionSpec(icebergSchema, List.of(keys.split(",")))
    ).orElse(PartitionSpec.unpartitioned());
    logger.info("M=createDestination, partition spec {} for table {} created", spec, fullTableName);

    s3TablesProbe.ifPresent(IcebergS3TablesConnectorService::createS3TableBucketAndNamespaceIfNotExists);
    Table table = catalog.createTable(getTableIdentifier(fullTableName), icebergSchema, spec);
    logger.info("M=createDestination, table {} created", table.name());
    tableSchema.put(fullTableName, table.schema());
  }

  private Schema buildIcebergSchema(LinkedHashMap<String, Map<String, String>> schema, List<String> primaryKey) {
    AtomicInteger counter = new AtomicInteger(0);
    Set<Integer> identifierIds = Sets.newHashSet();

    List<Types.NestedField> nestedFields = schema.entrySet()
        .stream()
        .map(it -> {
          String fieldName = it.getKey();
          boolean isPrimary = primaryKey.contains(fieldName);
          int currentCount = counter.addAndGet(1);
          if (isPrimary) {
            identifierIds.add(currentCount);
          }
          return Types.NestedField.of(currentCount, !isPrimary, fieldName, getType(it.getValue()));
        })
        .collect(Collectors.toList());

    return new Schema(nestedFields, identifierIds);
  }

  private Type getType(Map<String, String> schema) {
    // handle timestamp and decimal "INT64": "org.apache.kafka.connect.data.Timestamp"
    // by default use the first key of the map
    List<String> keys = Lists.newArrayList(schema.keySet());
    if (keys.size() > 1) {
      throw new RuntimeException("More than one key found in schema " + keys);
    }
    String type = keys.get(0);

    return schemaTypeToIcebergType(Objects.firstNonNull(schema.get(type), type));
  }

  private Type schemaTypeToIcebergType(String type) {
    switch (type) {
      case "org.apache.kafka.connect.data.Timestamp":
      case "io.debezium.time.ZonedTimestamp":
        return Types.TimestampType.withZone();
      case "org.apache.kafka.connect.data.Date":
        return Types.DateType.get();
      case "org.apache.kafka.connect.data.Time":
      case "io.debezium.time.ZonedTime":
        return Types.TimeType.get();
      case "org.apache.kafka.connect.data.Decimal":
        return Types.DecimalType.of(10, 10);
      case "INT8":
      case "INT16":
      case "INT32":
        return Types.IntegerType.get();
      case "INT64":
        return Types.LongType.get();
      case "FLOAT32":
      case "FLOAT64":
        return Types.FloatType.get();
      case "BOOLEAN":
        return Types.BooleanType.get();
      case "STRING":
      case "JSON":
      case "MAP":
      case "STRUCT":
      case "ARRAY":
        return Types.StringType.get();
      case "BYTES":
        return Types.BinaryType.get();
      default:
        throw new RuntimeException("Type not found for " + type);
    }
  }

  @Data
  private static class PartitionFieldKey {
    private final int sourceId;
    private final Transform<?, ?> transform;
    private final String name;
  }
}
