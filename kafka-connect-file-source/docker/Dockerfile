FROM confluentinc/cp-kafka-connect-base:6.2.0 as builder

USER root

RUN yum -y update
RUN yum -y install wget libstdc++ autoconf automake libtool autoconf-archive pkg-config gcc gcc-c++ make libjpeg-devel libpng-devel libtiff-devel zlib-devel

RUN wget https://github.com/DanBloomberg/leptonica/releases/download/1.84.1/leptonica-1.84.1.tar.gz
RUN tar -xvzf leptonica-1.84.1.tar.gz
RUN cd leptonica-1.84.1 && ./configure && make -j && make install && cd ..
RUN rm leptonica-1.84.1.tar.gz

RUN wget https://github.com/tesseract-ocr/tesseract/archive/refs/tags/5.3.4.tar.gz
RUN tar -xvzf 5.3.4.tar.gz
RUN cd tesseract-5.3.4 && ./autogen.sh && PKG_CONFIG_PATH=/usr/local/lib/pkgconfig LIBLEPT_HEADERSDIR=/usr/local/include ./configure --with-extra-includes=/usr/local/include --with-extra-libraries=/usr/local/lib && make && make install && ldconfig && cd ..
RUN rm -f 5.3.4.tar.gz

# Fetch tessdata
RUN wget https://github.com/tesseract-ocr/tessdata/archive/refs/tags/4.1.0.tar.gz
RUN rm -rf /usr/local/share/tessdata/*
RUN tar -xvzf 4.1.0.tar.gz
RUN mv tessdata-4.1.0/* /usr/local/share/tessdata
RUN rm 4.1.0.tar.gz

RUN tesseract --version

FROM nexla/base-connector-source-agent:3.2.1-latest
MAINTAINER Avinash "<EMAIL>"

ARG GIT_HASH
ENV GIT_HASH=$GIT_HASH

# Ensure we're running as root for installations
USER root

# Install GPG 2.4+ and all dependencies from source
RUN set -e && \
    echo "=== Installing GPG 2.4+ from Source with Dependencies ===" && \
    # Install basic build tools via yum (this requires root)
    yum -y install gcc make wget bzip2 tar gzip autoconf automake libtool zlib-devel gettext-devel && \
    # Set environment variables to help configure find libraries
    export PKG_CONFIG_PATH="/usr/local/lib/pkgconfig:/usr/lib64/pkgconfig:/usr/local/share/pkgconfig" && \
    export LD_LIBRARY_PATH="/usr/local/lib:/usr/lib64" && \
    export CPPFLAGS="-I/usr/local/include" && \
    export LDFLAGS="-L/usr/local/lib" && \
    cd /tmp && \
    \
    # 1. Build libgpg-error (must be first)
    wget https://gnupg.org/ftp/gcrypt/libgpg-error/libgpg-error-1.47.tar.bz2 && \
    tar -xjf libgpg-error-1.47.tar.bz2 && \
    cd libgpg-error-1.47 && \
    ./configure --prefix=/usr/local && \
    make -j$(nproc) && make install && \
    ldconfig && cd /tmp && \
    \
    # 2. Build libgcrypt (depends on libgpg-error)
    wget https://gnupg.org/ftp/gcrypt/libgcrypt/libgcrypt-1.10.2.tar.bz2 && \
    tar -xjf libgcrypt-1.10.2.tar.bz2 && \
    cd libgcrypt-1.10.2 && \
    ./configure --prefix=/usr/local && \
    make -j$(nproc) && make install && \
    ldconfig && cd /tmp && \
    \
    # 3. Build libassuan (depends on libgpg-error)
    wget https://gnupg.org/ftp/gcrypt/libassuan/libassuan-2.5.6.tar.bz2 && \
    tar -xjf libassuan-2.5.6.tar.bz2 && \
    cd libassuan-2.5.6 && \
    ./configure --prefix=/usr/local && \
    make -j$(nproc) && make install && \
    ldconfig && cd /tmp && \
    \
    # 4. Build libksba (depends on libgpg-error)
    wget https://gnupg.org/ftp/gcrypt/libksba/libksba-1.6.4.tar.bz2 && \
    tar -xjf libksba-1.6.4.tar.bz2 && \
    cd libksba-1.6.4 && \
    ./configure --prefix=/usr/local && \
    make -j$(nproc) && make install && \
    ldconfig && cd /tmp && \
    \
    # 5. Build npth
    wget https://gnupg.org/ftp/gcrypt/npth/npth-1.6.tar.bz2 && \
    tar -xjf npth-1.6.tar.bz2 && \
    cd npth-1.6 && \
    ./configure --prefix=/usr/local && \
    make -j$(nproc) && make install && \
    ldconfig && cd /tmp && \
    \
    # 6. Now build GPG itself
    wget https://gnupg.org/ftp/gcrypt/gnupg/gnupg-2.4.3.tar.bz2 && \
    tar -xjf gnupg-2.4.3.tar.bz2 && \
    cd gnupg-2.4.3 && \
    ./configure --prefix=/usr/local --disable-ldap --disable-sqlite --disable-ntbtls --disable-doc --disable-tests --enable-gpg-is-gpg2 && \
    make -j$(nproc) && make install && \
    \
    # Update symlinks and library config
    ln -sf /usr/local/bin/gpg /usr/bin/gpg && \
    ln -sf /usr/local/bin/gpg2 /usr/bin/gpg2 && \
    echo "/usr/local/lib" > /etc/ld.so.conf.d/gpg-local.conf && \
    ldconfig && \
    \
    # Setup for appuser
    mkdir -p /home/<USER>/.gnupg && \
    chmod 700 /home/<USER>/.gnupg && \
    chown -R appuser:appuser /home/<USER>/.gnupg && \
    \
    # Verify installation and AEAD support
    echo "=== GPG Installation Complete ===" && \
    NEW_GPG=$(gpg --version | head -1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+') && \
    echo "New GPG version: $NEW_GPG" && \
    if [ "$(printf '%s\n2.3.0\n' "$NEW_GPG" | sort -V | head -1)" = "2.3.0" ]; then \
        echo "✅ SUCCESS: GPG $NEW_GPG installed with AEAD support"; \
    else \
        echo "⚠️ WARNING: GPG $NEW_GPG may lack AEAD support"; \
    fi && \
    su - appuser -c "gpg --version" && \
    echo "✅ GPG is accessible to appuser" && \
    \
    # Cleanup
    cd / && rm -rf /tmp/* && \
    yum -y remove gcc make wget bzip2 autoconf automake libtool zlib-devel gettext-devel && \
    yum clean all && \
    rm -rf /var/cache/yum

# Optional: Switch back to non-root user if needed for runtime
USER appuser

COPY docker/log4j.properties /etc/confluent/docker/log4j.properties.template
COPY docker/log4j2.properties /etc/confluent/docker/log4j2.properties
ENV KAFKA_LOG4J_OPTS "-Dlog4j.configurationFile=/etc/confluent/docker/log4j2.properties -Dgit_hash=${GIT_HASH} -Djava.util.logging.manager=org.apache.logging.log4j.jul.LogManager"

ADD target/kafka-connect-*.jar /etc/${COMPONENT}/jars/

USER root
RUN rm -f /usr/share/java/kafka/slf4j-log4j*.jar
RUN rm -f /usr/share/java/cp-base-new/slf4j-log4j*.jar
USER appuser

COPY --from=builder /usr/local/share/tessdata /usr/local/share/tessdata
COPY --from=builder /usr/local/bin/tesseract /usr/local/bin/tesseract
COPY --from=builder /usr/local/lib/libtesseract.so.5 /usr/local/lib/libtesseract.so
COPY --from=builder /usr/local/lib/libleptonica.so.6 /usr/local/lib/libleptonica.so.6
COPY --from=builder /lib64/libjpeg.so.62 /lib64/libjpeg.so.62
COPY --from=builder /lib64/libtiff.so.5 /lib64/libtiff.so.5
COPY --from=builder /lib64/libpng16.so.16 /lib64/libpng16.so.16
COPY --from=builder /lib64/libgomp.so.1 /lib64/libgomp.so.1
COPY --from=builder /lib64/libjbig.so.2.1 /lib64/libjbig.so.2.1

ENV TESSDATA_PREFIX "/usr/local/share/tessdata"

# Final verification that GPG is working properly for the application user
USER appuser
RUN echo "=== Final GPG Verification ===" && \
    gpg --version && \
    echo "✅ GPG verification complete - ready for PGP file processing"

COPY docker/start.sh /app/start.sh
CMD /app/start.sh
