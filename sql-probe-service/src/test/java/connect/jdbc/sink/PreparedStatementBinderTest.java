package connect.jdbc.sink;

import com.nexla.test.UnitTests;
import connect.data.ConnectSchema;
import connect.data.Field;
import connect.data.Schema;
import connect.jdbc.sink.dialect.PostgreSqlDialect;
import connect.jdbc.sink.dialect.RedshiftSqlDialect;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static connect.jdbc.sink.PreparedStatementBinder.convertValueToString;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

@Category(UnitTests.class)
public class PreparedStatementBinderTest {

	private static final Logger logger = LoggerFactory.getLogger(PreparedStatementBinderTest.class);

	@Test
	public void redshiftCopyTimestamp() {
		Field field = createField("org.apache.kafka.connect.data.Timestamp");
		assertEquals("2018-04-19 20:32:08.000", convertValueToString(1524169928000L, field, logger, new RedshiftSqlDialect()));
	}

	@Test
	public void redshiftCopyTimestampAlternate() {
		Field field = createField("org.apache.kafka.connect.data.Timestamp");
		assertEquals("2018-04-19", convertValueToString("04/19/2018", field, logger, new RedshiftSqlDialect()));
	}

	@Test
	public void redshiftCopyDate() {
		Field field = createField("org.apache.kafka.connect.data.Date");
		assertEquals("2018-04-19", convertValueToString(1524169928000L, field, logger, new RedshiftSqlDialect()));
	}

	@Test
	public void redshiftCopyDateAlternate() {
		Field field = createField("org.apache.kafka.connect.data.Date");
		assertEquals("2018-04-19", convertValueToString("04/19/2018", field, logger, new RedshiftSqlDialect()));
	}

	@Test
	public void redshiftCopyTime() {
		Field field = createField("org.apache.kafka.connect.data.Time");
		assertEquals("20:32:08.000", convertValueToString(1524169928000L, field, logger, new RedshiftSqlDialect()));
	}

	@Test
	public void redshiftCopyEmptyNumericField() {
		Field field = createNumericField("org.apache.kafka.connect.data.Decimal");
		assertEquals(StringUtils.EMPTY, convertValueToString("", field, logger, new RedshiftSqlDialect()));
	}

	@Test
	public void postgresCopyEmptyNumericField() {
		Field field = createNumericField("org.apache.kafka.connect.data.Decimal");
		assertEquals(StringUtils.EMPTY, convertValueToString("", field, logger, new PostgreSqlDialect()));
	}

	@Test
	public void checkException() {
		try {
			Field field = createField(Schema.Type.INT64.getName());
			convertValueToString("test", field, logger, new RedshiftSqlDialect());
			fail("No exception thrown");
		} catch (Exception e) {
		}
	}

	private Field createField(String name) {
		return new Field("test", 0, new ConnectSchema(Schema.Type.INT64, false, 1, name, null, null, null, null, null, null));
	}

	private Field createNumericField(String name) {
		return new Field("test", 0, new ConnectSchema(Schema.Type.FLOAT64, false, 1, name, null, null, null, null, null, null));
	}

	private Field createBooleanField(String name) {
		return new Field(name, 0, new ConnectSchema(Schema.Type.BOOLEAN, false, "t", name, null, null, null, null, null, null));
	}

	@Test
	public void redshiftCopyTrueBooleanField() {
		Field field = createBooleanField("mybooltest");
		String t = convertValueToString("t", field, logger, new RedshiftSqlDialect());
		String true_ = convertValueToString("true", field, logger, new RedshiftSqlDialect());
		String _1 = convertValueToString("1", field, logger, new RedshiftSqlDialect());
		String TRUE_ = convertValueToString("TRUE", field, logger, new RedshiftSqlDialect());
		String y = convertValueToString("y", field, logger, new RedshiftSqlDialect());
		String yes = convertValueToString("yes", field, logger, new RedshiftSqlDialect());
		String on = convertValueToString("on", field, logger, new RedshiftSqlDialect());
		String tiger = convertValueToString("tiger!", field, logger, new RedshiftSqlDialect());

		assertEquals(t, "true");
		assertEquals(true_, "true");
		assertEquals(_1, "true");
		assertEquals(TRUE_, "true");
		assertEquals(y, "true");
		assertEquals(yes, "true");
		assertEquals(on, "true");
		assertEquals(tiger, "false");
	}

	@Test
	public void redshiftCopyFalseBooleanField() {
		Field field = createBooleanField("mybooltest2");
		String f = convertValueToString("f", field, logger, new RedshiftSqlDialect());
		String false_ = convertValueToString("false", field, logger, new RedshiftSqlDialect());
		String _0 = convertValueToString("0", field, logger, new RedshiftSqlDialect());
		String FALSE_ = convertValueToString("FALSE", field, logger, new RedshiftSqlDialect());
		String n = convertValueToString("n", field, logger, new RedshiftSqlDialect());
		String no = convertValueToString("no", field, logger, new RedshiftSqlDialect());
		String off = convertValueToString("off", field, logger, new RedshiftSqlDialect());
		String mouse = convertValueToString("mouse!", field, logger, new RedshiftSqlDialect());

		assertEquals(f, "false");
		assertEquals(false_, "false");
		assertEquals(_0, "false");
		assertEquals(FALSE_, "false");
		assertEquals(n, "false");
		assertEquals(no, "false");
		assertEquals(off, "false");
		assertEquals(mouse, "false");
	}
}
