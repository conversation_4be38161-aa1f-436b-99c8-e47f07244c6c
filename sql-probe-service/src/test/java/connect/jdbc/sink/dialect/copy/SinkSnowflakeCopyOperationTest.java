package connect.jdbc.sink.dialect.copy;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.ConnectionType;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.connector.config.file.AzureAuthConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import com.nexla.test.UnitTests;
import connect.jdbc.sink.dialect.copy.storage.SnowflakeCopyAzureBlobStorage;
import org.junit.experimental.categories.Category;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.LoggerFactory;

import java.util.Map;

import static com.nexla.common.NexlaConstants.CREDENTIALS_TYPE;
import static com.nexla.common.NexlaConstants.CREDS_ENC;
import static com.nexla.common.NexlaConstants.CREDS_ENC_IV;
import static com.nexla.common.NexlaConstants.SINK_ID;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.file.AzureAuthConfig.SAS_TOKEN;
import static com.nexla.connector.config.file.AzureAuthConfig.STORAGE_ACCOUNT_KEY;
import static com.nexla.connector.config.file.AzureAuthConfig.STORAGE_ACCOUNT_NAME;
import static com.nexla.connector.config.jdbc.JdbcAuthConfig.DATABASE_NAME;
import static com.nexla.connector.config.jdbc.JdbcAuthConfig.URL;
import static com.nexla.connector.properties.SqlConfigAccessor.INSERT_MODE;
import static com.nexla.connector.properties.SqlConfigAccessor.PRIMARY_KEY;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;

@Category(UnitTests.class)
public class SinkSnowflakeCopyOperationTest {

	private static final String TMP_UPLOAD_BUCKET = "nexlatempdata";

	private static final String TMP_FILE_NAME = "azure_80878129017969345474435csv.gz";

	private static final String OUTPUT_FILE_PATH = "azure://account.blob.core.windows.net/nexlatempdata/1/azure_80878129017969345474435csv.gz";

	private final SinkSnowflakeCopyOperation sinkSnowflakeCopyOperation = new SinkSnowflakeCopyOperation(
		new SnowflakeCopyAzureBlobStorage(getAzureAuthConfig(),
			TMP_UPLOAD_BUCKET,
			null, // it is null by default
			true)
	);

	@Test
	void tempFileLocation() {
		sinkSnowflakeCopyOperation.init(
			null,
			spy(new JdbcSinkConnectorConfig(getJdbcConfig())),
			mock(AdminApiClient.class),
			WarehouseCopyFileFormat.CSV_GZIP,
			new NexlaLogger(LoggerFactory.getLogger(this.getClass()))
		);

		Assertions.assertEquals(OUTPUT_FILE_PATH, sinkSnowflakeCopyOperation.tempFileLocation(TMP_FILE_NAME));
	}

	private static Map<String, String> getJdbcConfig() {
		return Map.of(
			SINK_ID, "1",
			CREDS_ENC, "1",
			CREDS_ENC_IV, "1",
			UNIT_TEST, "true",
			INSERT_MODE, "upsert",
			CREDENTIALS_TYPE, ConnectionType.SNOWFLAKE.name(),
			PRIMARY_KEY, "id",
			DATABASE_NAME, "db",
			URL, "url");
	}

	private static AzureAuthConfig getAzureAuthConfig() {
		var config = Map.of(
			SAS_TOKEN, "sas-token",
			STORAGE_ACCOUNT_KEY, "master-key",
			STORAGE_ACCOUNT_NAME, "account");

		return new AzureAuthConfig(config, 1);
	}
}
