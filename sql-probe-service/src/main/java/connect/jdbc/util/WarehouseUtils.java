package connect.jdbc.util;

import com.nexla.common.storage.WarehouseTempStorageType;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig;
import connect.jdbc.sink.dialect.copy.storage.*;

import java.util.Objects;
import java.util.Optional;

import static com.nexla.common.ConnectionType.*;
import static com.nexla.common.storage.WarehouseTempStorageType.AMAZON_S3;
import static com.nexla.common.storage.WarehouseTempStorageType.AZURE_BLOB;
import static java.util.Objects.isNull;

public class WarehouseUtils {

	public static final int MAX_ERROR_LIMIT = 100000;

	public static WarehouseCopyTempStorage getCopyOperationTempStorage(JdbcSourceConnectorConfig config) {
		if (Objects.equals(SNOWFLAKE, config.authConfig.dbType)) {

			if (AMAZON_S3 == config.warehouseTempStorageType) {
				validateConfig(config.s3AuthConfig, AMAZON_S3);
				return new SnowflakeCopyS3Storage(config.s3AuthConfig,
						config.tempS3UploadBucket,
						null,
						config.tempS3Delete);
			} else if (AZURE_BLOB == config.warehouseTempStorageType) {
				validateConfig(config.azureAuthConfig, AZURE_BLOB);
				return new SnowflakeCopyAzureBlobStorage(config.azureAuthConfig,
						config.tempAzureBlobUploadBucket,
						null,
						config.tempAzureBlobDelete);
			}
		}

		return new WarehouseCopyS3Storage(config.s3AuthConfig,
				config.tempS3UploadBucket,
				null,
				config.tempS3Delete);
	}

	public static WarehouseCopyTempStorage getCopyOperationTempStorage(JdbcSinkConnectorConfig config) {
		if (AMAZON_S3 == config.warehouseTempStorageType) {
			validateConfig(config.s3AuthConfig, AMAZON_S3);

			if (config.authConfig.dbType.equals(REDSHIFT)) {
				return new RedshiftCopyS3Storage(config.s3AuthConfig,
					config.tempS3UploadBucket,
					config.tempS3UploadPrefix,
					config.tempS3Delete);
			} else if (config.authConfig.dbType.equals(SNOWFLAKE)) {
				return new SnowflakeCopyS3Storage(config.s3AuthConfig,
					config.tempS3UploadBucket,
					config.tempS3UploadPrefix,
					config.tempS3Delete);
			} else if (config.authConfig.dbType.equals(ORACLE_AUTONOMOUS)) {
				return new OracleAutonomousCopyS3Storage(config.s3AuthConfig,
					config.tempS3UploadBucket,
					config.tempS3UploadPrefix,
					config.tempS3Delete);
			}

		} else if (AZURE_BLOB == config.warehouseTempStorageType) {
			validateConfig(config.azureAuthConfig, AZURE_BLOB);

			if (Objects.equals(SNOWFLAKE, config.authConfig.dbType)) {
				return new SnowflakeCopyAzureBlobStorage(config.azureAuthConfig,
					config.tempAzureBlobUploadBucket,
					getTempUploadPrefix(config),
					config.tempAzureBlobDelete);
			}
		}

		return new WarehouseCopyS3Storage(config.s3AuthConfig,
				config.tempS3UploadBucket,
				config.tempS3UploadPrefix,
				config.tempS3Delete);
	}


	private static void validateConfig(Object o, WarehouseTempStorageType type) {
		if (isNull(o)) {
			throw new IllegalArgumentException(String.format("Temp storage config %s is null!", type));
		}
	}

	private static String getTempUploadPrefix(JdbcSinkConnectorConfig config) {
		return Optional.ofNullable(config.tempAzureBlobUploadPrefix)
				.orElse(String.valueOf(config.sinkId));
	}

	public static boolean isRedshiftServerless(JdbcSinkConnectorConfig config) {
		boolean explicitlyServerlessRedshiftSink = config.redshiftServerless;
		boolean hostContainsServerless = Optional.ofNullable(config.authConfig.host)
				.map(hostUrl -> hostUrl.toLowerCase().contains("serverless"))
				.orElse(false);
		boolean urlContainsServerless = Optional.ofNullable(config.authConfig.url)
				.map(url -> url.toLowerCase().contains("serverless"))
				.orElse(false);

		return explicitlyServerlessRedshiftSink || hostContainsServerless || urlContainsServerless;
	}
}
