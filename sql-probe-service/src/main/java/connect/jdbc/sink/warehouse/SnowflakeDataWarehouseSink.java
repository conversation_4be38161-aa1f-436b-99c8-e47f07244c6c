package connect.jdbc.sink.warehouse;

import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.logging.TimeLogger;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.common.MetricUtils;
import com.nexla.connect.common.cdc.DebeziumConstants;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.jdbc.sink.dialect.copy.CopyOperationHelper;
import connect.jdbc.sink.dialect.copy.CopyOperationLocalBuffer;
import connect.jdbc.sink.dialect.copy.FlushResult;
import connect.jdbc.sink.dialect.copy.SinkCopyOperationCommon;
import connect.jdbc.sink.dialect.copy.storage.SnowflakeCopyStageStorage;
import connect.jdbc.sink.dialect.copy.storage.WarehouseCopyTempStorage;
import connect.jdbc.util.WarehouseUtils;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.kitesdk.shaded.com.google.common.base.Objects;
import org.slf4j.Logger;

import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.HashMap;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.INSERT;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.UPSERT;
import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.Compression.GZIP;
import static java.util.stream.Collectors.joining;

@AllArgsConstructor
public class SnowflakeDataWarehouseSink implements DataWarehouseSink {

	private static final String INSUFFICIENT_PRIVILEGES = "Insufficient privileges";
	private static final String INSUFFICIENT_PRIVILEGES_ERROR_MESSAGE = "Unable to create temporary table, please make sure credential has CREATE TABLE privileges!";
	private final SinkCopyOperationCommon common;

	private final Logger logger;

	public SnowflakeDataWarehouseSink(SinkCopyOperationCommon common) {
		this.common = common;
		this.logger = common.getLogger();
	}

	@Override
	public void writeData(List<NexlaMessageContext> records, Map<Long, Integer> streamSizeByRunId) {
		common.writeData(records, streamSizeByRunId);
	}
	@Override
	public void onConnectorStop(Supplier<Connection> connProvider) {
		common.dropTempTable(connProvider);
	}

	@Override
	public void onConnectorStart(Supplier<Connection> connProvider) {
		common.dropTempTable(connProvider);
	}

	public CopyOperationLocalBuffer getBuffer() {
		return common.getBuffer();
	}

	@Override
	public int getBufferSize() {
		return common.getBuffer().streamSize();
	}

	@Override
	public Map<Long, Integer> getStreamSizeByRunId() {
		return common.getBuffer().streamSizeByRunId();
	}

	@Override
	public int getCurrentBatchSize() {
		return common.getTotalStreamSizeSentByRunId().values().stream().mapToInt(Integer::intValue).sum();
	}

	@SneakyThrows
	@Override
	public Optional<FlushResult> flushBatch(Connection connection) {
		if (common.getBuffer().streamSize() > 0) {
			try (Statement statement = connection.createStatement()) {
				var insertMode = common.getConfig().insertMode;

				var storage = getWarehouseTempStorage(common, statement);

				if (UPSERT == insertMode) {
					executeFlushBatchUpsert(statement, connection, storage);
					return Optional.empty();
				} else if (INSERT == insertMode) {
					return executeFlushBatchInsert(statement, storage);
				}
			}
		}

		return Optional.empty();
	}

	@SneakyThrows
	@Override
	public Optional<FlushResult> flushPipeline(Connection connection) {
		int streamSize = common.getBuffer().streamSize();
		if (streamSize > 0) {
			return Optional.empty();
		}

		Map<Long, RecordMetric> metricsByRunId = common.getRecordMetricByRunId();

		try (Statement statement = connection.createStatement()) {
			String tempTableName = common.getQualifiedTempTableName();

			if (!common.isTempTableCreated(connection)) {
				return Optional.empty();
			} else {
				boolean hasRecords = common.tempTableHasRecords(statement);
				if (!hasRecords) {
					// if temp table is empty and has no data we have to drop it
					common.dropTempTable(statement);

					// it means all records failed to be loaded to temp table
					if (metricsByRunId.values().stream().anyMatch(metric -> metric.errorRecords.get() > 0)) {
						FlushResult result = FlushResult.builder()
								.metricsByRunId(metricsByRunId)
								.bufferOffsets(common.getLatestProcessedOffsets())
								.build();

						logger.info("M=flushPipeline, stage=success, no data to merge, errorRecords={}", 
							metricsByRunId.values().stream().mapToLong(metric -> metric.errorRecords.get()).sum());
						common.resetMetrics();
						return Optional.of(result);
					}

					return Optional.empty();
				}
			}

			logger.info("M=flushPipeline, stage=flush-started, tempTableName={}", tempTableName);

			common.removeDuplicatesFromTempTable(statement);
			mergeTempTableAndDestinationTable(statement);

			common.dropTempTable(statement);
			common.updateRecordMetrics();
		}

		FlushResult result = FlushResult.builder()
				.metricsByRunId(metricsByRunId)
				.bufferOffsets(common.getLatestProcessedOffsets())
				.build();

		common.resetMetrics();

		logger.info("M=flushPipeline, stage=success, totalBytesSent={}, totalStreamSizeSent={}",
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentBytesTotal.get()).sum(),
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentRecordsTotal.get()).sum());

		return Optional.of(result);
	}

	@Override
	@SneakyThrows
	public Optional<FlushResult> directFlush(Connection connection, List<File> files, List<String> columnsPassed) {
		var columns = CollectionUtils.isNotEmpty(columnsPassed)
				? common.formatColumnsForQuery(columnsPassed)
				: common.getColumnsForQuery();

		var qualifiedTableName = common.getQualifiedTableName();

		Map<Long, RecordMetric> metricsByRunId = common.getRecordMetricByRunId();
		RecordMetric recordMetric = metricsByRunId.get(0L);

		try (Statement statement = connection.createStatement()) {
			var storage = getWarehouseTempStorage(common, statement);
			files.forEach(storage::uploadFile);

			files.forEach(it -> {
				var storageFileLocation = common.getStorageFileLocation(it.getName(), storage);
				var copyCommand = getCopyCommand(common.getConfig(),
						common.getFileFormat(),
						storageFileLocation,
						qualifiedTableName,
						columns,
						storage);

				var rowsUpdated = common.executeUpdate(statement, copyCommand);
				recordMetric.sentRecordsTotal.addAndGet(rowsUpdated);
			});

			files.forEach(file -> common.cleanupTempFiles(file, storage));
		}

		logger.info("M=directFlush, stage=success, filesSize={}, tableName={}, sentRecordsTotal={}",
				files.size(),
				qualifiedTableName,
				recordMetric.sentRecordsTotal.get());

		FlushResult flushResult = FlushResult.builder()
				.metricsByRunId(metricsByRunId)
				.build();

		return Optional.of(flushResult);
	}

	@SneakyThrows
	@Override
	public void truncateTable(Connection connection, int streamSize) {
		logger.info("resetting buffer with size [{}]...", getBufferSize());
		common.resetBuffer();
		try (Statement statement = connection.createStatement()) {
			logger.info("cleaning DBs...");
			common.dropTempTable(statement);
			common.executeTruncate(statement);
		}
		common.updateMetrics(0L, 1L, streamSize);
	}

	@SneakyThrows
	private Optional<FlushResult> executeFlushBatchInsert(Statement statement, WarehouseCopyTempStorage storage) {
		Map<Long, RecordMetric> metricsByRunId = common.getRecordMetricByRunId();
		StreamEx<NexlaMessage> records = common.getBuffer().getRecords();

		String qualifiedTableName = common.getQualifiedTableName();

		logger.info("M=executeFlushBatchInsert, stage=insert-started, qualifiedTableName={}", qualifiedTableName);

		// Calculate metrics per run ID
		records = records.peek(message -> {
			Long runId = 0L;
			if (message.getNexlaMetaData() != null && message.getNexlaMetaData().getTags() != null && message.getNexlaMetaData().getTags().get("originalMessageRunId") != null) {
				runId = ((Number) message.getNexlaMetaData().getTags().get("originalMessageRunId")).longValue();
			}
			metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());
			common.addMessageForApproximation(message);
		});
		getStreamSizeByRunId().forEach((runId, streamSize) -> {
			common.updateMetrics(runId, 0, streamSize);
		});


		File localFile = common.uploadRecordsToStorage(records, storage);
		String tempFileLocation = common.getStorageFileLocation(localFile.getName(), storage);

		String columns = common.getColumnsForQuery();

		String sql = getCopyCommand(common.getConfig(),
				common.getFileFormat(),
				tempFileLocation,
				qualifiedTableName,
				columns,
				storage);
		try (TimeLogger ignored = new TimeLogger(logger, "executeFlushBatchInsert::execute copy command")) {
			common.executeSQL(statement, sql);
		}

		common.updateRecordMetrics();

		//check for errors
		manageCopyErrors(metricsByRunId.get(0L), statement, localFile.getName());
		common.cleanupTempFiles(localFile, storage);

		FlushResult result = FlushResult.builder()
				.bufferOffsets(common.getBuffer().getOffsets())
				.metricsByRunId(metricsByRunId)
				.build();

		common.resetBuffer();
		common.resetMetrics();

		logger.info("M=executeFlushBatchInsert, stage=success, totalBytesSent={}, totalStreamSizeSent={}",
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentBytesTotal.get()).sum(),
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentRecordsTotal.get()).sum());

		return Optional.of(result);
	}

	private void executeFlushBatchUpsert(Statement statement, Connection connection, WarehouseCopyTempStorage storage) {
		Map<Long, RecordMetric> metricsByRunId = common.getRecordMetricByRunId();
		String tempTableName = common.getQualifiedTempTableName();

		if (!common.isTempTableCreated(connection)) {
			createTemporaryTable(statement, tempTableName, common.getQualifiedTableName());
		}

		logger.info("M=executeFlushBatchUpsert, stage=upsert-started, tempTableName={}", tempTableName);

		Long currentMaxIncrementalId = common.retrieveMaxIncrementalIdFromTempTable(statement);

		StreamEx<NexlaMessage> records = common.prepareRecordsForUpsert(currentMaxIncrementalId, common.getConsumer(v -> v));

		// Calculate metrics per run ID
		records = records.peek(message -> {
			Long runId = 0L;
			if (message.getNexlaMetaData() != null && message.getNexlaMetaData().getTags() != null && message.getNexlaMetaData().getTags().get("originalMessageRunId") != null) {
				runId = ((Number) message.getNexlaMetaData().getTags().get("originalMessageRunId")).longValue();
			}
			metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());
			common.addMessageForApproximation(message);
		});
		getStreamSizeByRunId().forEach((runId, streamSize) -> {
			common.updateMetrics(runId, 0, streamSize);
		});


		File localFile = common.uploadRecordsToStorage(records, storage);
		String tempFileLocation = common.getStorageFileLocation(localFile.getName(), storage);
		String columns = common.getEnrichedColumnsForQuery();

		String copyCommand = getCopyCommand(common.getConfig(),
				common.getFileFormat(),
				tempFileLocation,
				tempTableName,
				columns,
				storage);
		try (TimeLogger ignored = new TimeLogger(logger, "executeFlushBatchUpsert::execute copy command")) {
			common.executeSQL(statement, copyCommand);
		}

		common.updateRecordMetrics();

		//check for errors
		manageCopyErrors(metricsByRunId.get(0L), statement, tempFileLocation, common.getTempTableName());

		common.cleanupTempFiles(localFile, storage);
		common.resetBuffer();

		logger.info("M=executeFlushBatchUpsert, stage=success, totalBytesSent={}, totalStreamSizeSent={}",
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentBytesTotal.get()).sum(),
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentRecordsTotal.get()).sum());
	}

	@SneakyThrows
	private void createTemporaryTable(Statement st, String tempTable, String qualifiedTableName) {
		logger.info("M=createTemporaryTable, tempTableName={}, qualifiedTableName={}", tempTable, qualifiedTableName);
		
		try {
			var createTableCommand = String.format("CREATE TABLE %s LIKE %s", tempTable, qualifiedTableName);
			common.executeSQL(st, createTableCommand);
			common.setTempTableExists(true);
		} catch(Exception e) {
			if (e.getMessage().contains(INSUFFICIENT_PRIVILEGES)) {
				throw new IllegalArgumentException(INSUFFICIENT_PRIVILEGES_ERROR_MESSAGE, e);
			}

			throw e;
		}

		var incIdAlterTable = String.format("ALTER TABLE %s ADD %s BIGINT", tempTable, common.getIncrementalIdColumnName());
		common.executeSQL(st, incIdAlterTable);

		if(common.getConfig().cdcEnabled) {
			var deleteAlterTable = String.format("ALTER TABLE %s ADD %s BOOLEAN", tempTable, common.escapeColumnName(DebeziumConstants.NEXLA_OPERATION_DELETE.toUpperCase()));
			common.executeSQL(st, deleteAlterTable);
		}
	}

	@SneakyThrows
	private void mergeTempTableAndDestinationTable(Statement statement) {
		var tempTableName = common.getQualifiedTempTableName();
		var destinationTableName = common.getQualifiedTableName();
		var aliasTempTable = "temp_table";
		var aliasDestTable = "dest_table";

		var primaryKeys = common.getConfig().primaryKey;

		var primaryKeyWhereClause = primaryKeys
				.stream()
				.map(common::escapeField)
				.map(it -> String.format("%s.%s = %s.%s", aliasDestTable, it, aliasTempTable, it))
				.collect(Collectors.joining(" AND "));

		var schemaFields = common.getSchemaFields().collect(Collectors.toList());

		var insertColumns = common.getColumnsForQuery();
		var insertValues = schemaFields
				.stream()
				.map(it -> String.format("%s.%s", aliasTempTable, it))
				.collect(joining(",", " (", ") "));

		var ifMatchCDC = common.getConfig().cdcEnabled ?
				" WHEN MATCHED AND " + String.format("%s.%s = TRUE", aliasTempTable, common.escapeColumnName(DebeziumConstants.NEXLA_OPERATION_DELETE.toUpperCase())) + " THEN DELETE " : "";
		var ifNotMatchCDC = common.getConfig().cdcEnabled ?
				" AND " + String.format("%s.%s = FALSE", aliasTempTable, common.escapeColumnName(DebeziumConstants.NEXLA_OPERATION_DELETE.toUpperCase())) : "";


		var updateSql = schemaFields
				.stream()
				.filter(it -> !primaryKeys.contains(it.replaceAll("\"", "")))
				.map(it -> String.format("%s.%s = %s.%s", aliasDestTable, it, aliasTempTable, it))
				.collect(Collectors.joining(","));

		var whenMatchedCondition = StringUtils.isNotBlank(updateSql) ? " WHEN MATCHED THEN UPDATE SET " + updateSql : "";

		var mergeCommand = " MERGE INTO " + destinationTableName + " " + aliasDestTable +
				" USING " + tempTableName + " " + aliasTempTable + " ON " + primaryKeyWhereClause +
				ifMatchCDC +
				whenMatchedCondition +
				" WHEN NOT MATCHED " +
				ifNotMatchCDC +
				" THEN INSERT " + insertColumns + " VALUES " + insertValues;

		common.executeUpdate(statement, mergeCommand);
	}

	private WarehouseCopyTempStorage getWarehouseTempStorage(SinkCopyOperationCommon common, Statement statement) {
		var config = common.getConfig();

		if (config.directUpload) {
			return new SnowflakeCopyStageStorage(config, common.getFileFormat(), statement, common);
		}

		return WarehouseUtils.getCopyOperationTempStorage(config);
	}

	private String getCopyCommand(JdbcSinkConnectorConfig config,
								  WarehouseCopyFileFormat fileFormat,
								  String fileLocation,
								  String table,
								  String columns,
								  WarehouseCopyTempStorage storage) {
		String format;
		String skipHeaders;
		String columnMatchMode;
		String dateTimeFormats = getDateTimeFormats(config);
		switch (fileFormat.format) {
			case JSON_FORMAT:
				format = "json " + dateTimeFormats;
				skipHeaders = "";
				columnMatchMode = "MATCH_BY_COLUMN_NAME = CASE_INSENSITIVE ";
				columns = "";
				break;
			case CSV_FORMAT:
				format = "csv FIELD_DELIMITER = '" + fileFormat.delimiter + "' FIELD_OPTIONALLY_ENCLOSED_BY = '\"' ESCAPE = '\\\\' " + "' " + dateTimeFormats;
				skipHeaders = config.skipHeader ? " SKIP_HEADER = 1 " : "";
				columnMatchMode = "";
				break;
			default:
				format = "csv ";
				skipHeaders = config.skipHeader ? " SKIP_HEADER = 1 " : "";
				columnMatchMode = "";
		}

		String compression = fileFormat.compression == GZIP ? "COMPRESSION = gzip" : "";

		return storage.getSinkCopyQuery(table, columns, fileLocation, format, compression, skipHeaders, columnMatchMode, config.stopOnError);
	}

	private String getDateTimeFormats(JdbcSinkConnectorConfig config) {
		var builder = new StringBuilder();

		var warehouseDateFormat = config.warehouseDateFormat;
		if (StringUtils.isNotBlank(warehouseDateFormat)) {
			builder.append("DATE_FORMAT = '");
			builder.append(warehouseDateFormat);
			builder.append("' ");
		}

		var warehouseTimestampFormat = config.warehouseTimestampFormat;
		if (StringUtils.isNotBlank(warehouseTimestampFormat)) {
			builder.append("TIMESTAMP_FORMAT = '");
			builder.append(warehouseTimestampFormat);
			builder.append("' ");
		}

		return builder.toString();
	}

	private void manageCopyErrors(RecordMetric recordMetric,
								  Statement statement,
								  String fileName) {
		// don't mix snowflake errors with application errors
		if(recordMetric.errorRecords.get() > 0) {
			return;
		}
		manageCopyErrors(recordMetric, statement, fileName, null);
	}
	private void manageCopyErrors(RecordMetric recordMetric,
								  Statement statement,
								  String fileName,
								  String tableName) {
		try {

			int extensionIndex = fileName.lastIndexOf('.');
			if (extensionIndex > 0) { // check if there is an extension
				fileName = fileName.substring(0, extensionIndex);
			}

			String sql = CopyOperationHelper.uploadResultSql(fileName, Objects.firstNonNull(tableName, common.getTableName()), true);
			ResultSet rs = common.executeQuery(statement, sql);
			if (rs.next()) {
				int rowCount = rs.getInt("ROW_COUNT");
				int errorCount = rs.getInt("ERROR_COUNT");
				String errMsg = rs.getString("FIRST_ERROR_MESSAGE");
				String errColName = rs.getString("FIRST_ERROR_COL_NAME");

				logger.debug("M=manageCopyErrors, rowCount={}, errorCount={}", rowCount, errorCount);

				// Don't add to sentRecordsTotal, it's already set by updateMetrics
				recordMetric.errorRecords.addAndGet(errorCount);

				if (StringUtils.isNotBlank(errMsg)) {
					String errorMessage = StringUtils.isNotBlank(errColName) ?
							String.format("error message=[%s], error column='%s'", errMsg, errColName) :
							String.format("error message=[%s]", errMsg);

					recordMetric.quarantineMessages.add(RecordMetric.quarantineMessage(new NexlaMessage(new LinkedHashMap<>(), new NexlaMetaData()),
							errorMessage));
				}
			}
		} catch (Exception exc) {
			logger.error("Can't get 'load_history' information for file: {}", fileName, exc);
		}
	}
}
