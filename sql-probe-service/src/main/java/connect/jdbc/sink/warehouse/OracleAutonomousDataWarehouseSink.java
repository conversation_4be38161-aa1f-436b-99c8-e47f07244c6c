package connect.jdbc.sink.warehouse;

import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.exception.NexlaErrorMessage;
import com.nexla.common.exception.NexlaQuarantineMessage;
import com.nexla.common.logging.TimeLogger;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.common.MetricUtils;
import com.nexla.connect.common.cdc.DebeziumConstants;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.SinkConnectorConfig;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.data.Field;
import connect.data.Schema;
import connect.jdbc.sink.dialect.copy.CopyOperationLocalBuffer;
import connect.jdbc.sink.dialect.copy.FlushResult;
import connect.jdbc.sink.dialect.copy.SinkCopyOperationCommon;
import connect.jdbc.sink.dialect.copy.exception.OracleAutonomousErrorMessage;
import connect.jdbc.sink.dialect.copy.exception.OracleAutonomousUtilityEntitiesToNexlaMessageMapper;
import connect.jdbc.sink.dialect.copy.exception.OracleAutonomousUtilityTablesService;
import connect.jdbc.sink.dialect.copy.exception.OracleAutonomousWholeBatchFailedException;
import connect.jdbc.sink.dialect.copy.storage.WarehouseCopyS3Storage;
import connect.jdbc.sink.dialect.copy.storage.WarehouseCopyTempStorage;
import connect.jdbc.sink.dialect.type.OracleType;
import connect.jdbc.util.JdbcUtils;
import connect.jdbc.util.WarehouseUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.javatuples.Pair;
import org.kitesdk.shaded.com.google.common.base.Objects;
import org.slf4j.Logger;

import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.StreamUtils.OBJECT_MAPPER;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.INSERT;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.UPSERT;
import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.Compression.GZIP;
import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.Format.CSV_FORMAT;
import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.Format.JSON_FORMAT;
import static java.lang.String.format;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;

public class OracleAutonomousDataWarehouseSink implements DataWarehouseSink {

	private static final String TRUNCATE_TABLE = "TRUNCATE TABLE ";
	private static final int CREDENTIALS_ALREADY_EXISTS_ERROR_CODE = 20022;
	private static final int REJECT_LIMIT_ERROR_CODE = 20003;
	private static final EnumSet<WarehouseCopyFileFormat.Format> SELF_DESCRIBING_FORMATS = EnumSet.of(JSON_FORMAT);
	private final SinkCopyOperationCommon common;

	private final Logger logger;

	private final WarehouseCopyTempStorage storage;

	private final OracleAutonomousUtilityTablesService utilityTablesService = new OracleAutonomousUtilityTablesService();
	private final OracleAutonomousUtilityEntitiesToNexlaMessageMapper bodiesToNexlaMessageMapper
			= new OracleAutonomousUtilityEntitiesToNexlaMessageMapper();

	public OracleAutonomousDataWarehouseSink(SinkCopyOperationCommon common, WarehouseCopyTempStorage storage) {
		this.common = common;
		this.logger = common.getLogger();
		this.storage = storage;
		common.setIncludeSchemaToQualifiedTempTable(true);
	}

	public OracleAutonomousDataWarehouseSink(SinkCopyOperationCommon common) {
		this.common = common;
		this.logger = common.getLogger();
		this.storage = WarehouseUtils.getCopyOperationTempStorage(common.getConfig());
		common.setIncludeSchemaToQualifiedTempTable(true);
	}

	@Override
	public void writeData(List<NexlaMessageContext> records, Map<Long, Integer> streamSizeByRunId) {
		common.writeData(records, streamSizeByRunId);
	}
	@Override
	public void onConnectorStop(Supplier<Connection> connProvider) {
		dropTempTable(connProvider);
	}

	@Override
	public void onConnectorStart(Supplier<Connection> connProvider) {
		dropTempTable(connProvider);
	}

	public CopyOperationLocalBuffer getBuffer() {
		return common.getBuffer();
	}

	@Override
	public int getBufferSize() {
		return common.getBuffer().streamSize();
	}

	@Override
	public int getCurrentBatchSize() {
		return common.getTotalStreamSizeSentByRunId().values().stream().mapToInt(Integer::intValue).sum();
	}

	@Override
	public Map<Long, Integer> getStreamSizeByRunId() {
		return common.getBuffer().streamSizeByRunId();
	}

	@SneakyThrows
	@Override
	public Optional<FlushResult> flushBatch(Connection connection) {
		if (getBuffer().streamSize() > 0) {
			try (Statement statement = connection.createStatement()) {
				var insertMode = common.getConfig().insertMode;
				if (UPSERT == insertMode) {
					executeFlushBatchUpsert(statement, connection);
					return Optional.empty();
				} else if (INSERT == insertMode) {
					return executeFlushBatchInsert(statement, connection);
				}
			}
		}

		return Optional.empty();
	}

	@Getter
	@AllArgsConstructor
	private class OracleError {
		private int oracleErrorNumber;
		private String oracleErrorMessage;
	}

	public String getQualifiedErrorTableName() {
		return common.getDbDialect().getQualifiedTableName("ERR$_" +
			common.getConfig().table, common.getConfig().authConfig.schemaName, JdbcUtils.getDatabaseName(common.getConfig()));
	}

	private Map<OracleError, LinkedHashMap<String, Object>> readLogErrors(Statement statement) throws SQLException {
		Map<OracleError, LinkedHashMap<String, Object>> errorsMap = new LinkedHashMap<>();
		var cleanColumns = "ORA_ERR_NUMBER$, ORA_ERR_MESG$, " +
			common.getColumnsForQuery().replaceAll("[()]", "").trim();
		String query = String.format("SELECT %s FROM %s", cleanColumns, getQualifiedErrorTableName());
		ResultSet rs = common.executeQuery(statement, query);
		while (rs.next()) {
			OracleError oracleError = new OracleError(
				rs.getInt("ORA_ERR_NUMBER$"),
				rs.getString("ORA_ERR_MESG$")
			);

			LinkedHashMap<String, Object> rawMessageMap = new LinkedHashMap<>();
			for (Field field : common.getSchema().fields()) {
				Object fieldValue = rs.getObject(field.name());
				rawMessageMap.put(field.name(), fieldValue);
			}

			errorsMap.put(oracleError, rawMessageMap);
		}
		return errorsMap;
	}

	private void readLogErrorsAndUpdateMetrics(Statement statement, RecordMetric recordMetric) throws SQLException {
		Map<OracleError, LinkedHashMap<String, Object>> logErrorsMap = readLogErrors(statement);
		if (!logErrorsMap.isEmpty()) {
			for (Map.Entry<OracleError, LinkedHashMap<String, Object>> entry : logErrorsMap.entrySet()){
				recordMetric.errorRecords.incrementAndGet();

				String errorMessage = String.format("error message=%s, error number=%d",
					entry.getKey().getOracleErrorMessage(),
					entry.getKey().getOracleErrorNumber());
				recordMetric.quarantineMessages.add(RecordMetric.quarantineMessage(
					new NexlaMessage(entry.getValue(), new NexlaMetaData()), errorMessage));
				logger.error(errorMessage);
			}
		}
	}

	private void createLogErrorTable(Statement statement) {
		common.executeSQL(statement, String.format("call dbms_errlog.create_error_log('%s')", common.getQualifiedTableName()));
	}

	@SneakyThrows
	@Override
	public Optional<FlushResult> flushPipeline(Connection connection) {
		int streamSize = getBuffer().streamSize();
		if (streamSize > 0) {
			return Optional.empty();
		}

		Map<Long, RecordMetric> metricsByRunId = common.getRecordMetricByRunId();

		try (Statement statement = connection.createStatement()) {
			String tempTableName = common.getQualifiedTempTableName();

			if (!common.isTempTableCreated(connection, getCheckTableSql())) {
				logger.info("Temp table has no records to merge");
				return Optional.empty();
			} else {
				boolean hasRecords = common.tempTableHasRecords(statement);
				if (!hasRecords) {
					// if temp table is empty and has no data we have to drop it
					dropTempTable(statement);

					// it means all records failed to be loaded to temp table
					if (metricsByRunId.values().stream().anyMatch(metric -> metric.errorRecords.get() > 0)) {
						FlushResult result = FlushResult.builder()
								.metricsByRunId(metricsByRunId)
								.bufferOffsets(common.getLatestProcessedOffsets())
								.build();

								logger.info("M=flushPipeline, stage=success, no data to merge, errorRecords={}", 
								metricsByRunId.values().stream().mapToLong(metric -> metric.errorRecords.get()).sum());
								common.resetMetrics();
						return Optional.of(result);
					}

					return Optional.empty();
				}
			}

			logger.info("M=flushPipeline, stage=flush-started, tempTableName={}", tempTableName);
			disableParallelDMLIfNecessary(statement);

			common.removeDuplicatesFromTempTable(statement, getRemoveDuplicatesSql());
			common.deleteDataFromDestinationTable(statement);

			common.executeSQL(statement, getDropIfExistsSql(getQualifiedErrorTableName()));
			createLogErrorTable(statement);
			common.copyDataFromTempToDestinationTable(statement, "0");
			readLogErrorsAndUpdateMetrics(statement, metricsByRunId.get(0L));
			common.executeSQL(statement, getDropIfExistsSql(getQualifiedErrorTableName()));

			dropTempTable(statement);
			common.updateRecordMetrics();
		}

		FlushResult result = FlushResult.builder()
				.metricsByRunId(metricsByRunId)
				.bufferOffsets(common.getLatestProcessedOffsets())
				.build();

		common.resetMetrics();

		logger.info("M=flushPipeline, stage=success, totalBytesSent={}, totalStreamSizeSent={}",
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentBytesTotal.get()).sum(),
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentRecordsTotal.get()).sum());

		return Optional.of(result);
	}

	private String getRemoveDuplicatesSql() {
		return "BEGIN EXECUTE IMMEDIATE " +
				"'DELETE FROM ${tempTableName} " +
				"WHERE EXISTS ( " +
				"    SELECT 1 " +
				"    FROM ( " +
				"        SELECT ${primaryKeys}, MAX(${incrementalId}) AS max_incremental_id " +
				"        FROM ${tempTableName} " +
				"        GROUP BY ${primaryKeys} " +
				"        HAVING COUNT(1) > 1 " +
				"    ) di " +
				"    WHERE ${primaryKeyWhereClause} " +
				"    AND ${tempTableName}.${incrementalId} <> di.max_incremental_id " +
				")'; " +
				"END;";
	}


	@Override
	@SneakyThrows
	public Optional<FlushResult> directFlush(Connection connection, List<File> files, List<String> columnsPassed) {
		var columns = CollectionUtils.isNotEmpty(columnsPassed)
				? columnsPassed
				: common.getSchemaFields().collect(Collectors.toList());

		var qualifiedTableName = common.getDbDialect().q(common.getConfig().table);

		files.forEach(storage::uploadFile);

		Map<Long, RecordMetric> metricsByRunId = common.getRecordMetricByRunId();
		RecordMetric recordMetric = metricsByRunId.get(0L);
		try (Statement statement = connection.createStatement()) {

			files.forEach(it -> {
				var storageFileLocation = common.getStorageFileLocation(it.getName(), storage);
				var copyCommand = getCopyCommand(common.getConfig(),
						common.getFileFormat(),
						storageFileLocation,
						qualifiedTableName,
						columns);
				var rowsUpdated = common.executeUpdate(statement, copyCommand);
				recordMetric.sentRecordsTotal.addAndGet(rowsUpdated);
			});

			files.forEach(file -> common.cleanupTempFiles(file, storage));
		}

		logger.info("M=directFlush, stage=success, filesSize={}, tableName={}, sentRecordsTotal={}",
				files.size(),
				qualifiedTableName,
				recordMetric.sentRecordsTotal.get());

		FlushResult flushResult = FlushResult.builder()
				.metricsByRunId(metricsByRunId)
				.build();

		return Optional.of(flushResult);
	}

	@SneakyThrows
	@Override
	public void truncateTable(Connection connection, int streamSize) {
		logger.info("resetting buffer with size [{}]...", getBufferSize());
		common.resetBuffer();
		try (Statement statement = connection.createStatement()) {
			logger.info("cleaning DBs...");
			dropTempTable(statement);
			common.executeTruncate(statement);
		}
		common.updateMetrics(0L, 1L, streamSize);
	}

	@SneakyThrows
	private Optional<FlushResult> executeFlushBatchInsert(Statement statement, Connection connection) {
		Map<Long, RecordMetric> metricsByRunId = common.getRecordMetricByRunId();
		StreamEx<NexlaMessage> records = getBuffer().getRecords();

		String qualifiedTableName = common.getQualifiedTableName();

		logger.info("M=executeFlushBatchInsert, stage=insert-started, qualifiedTableName={}", qualifiedTableName);


		// Calculate metrics per run ID
		records = records.peek(message -> {
			Long runId = 0L;
			if (message.getNexlaMetaData() != null && message.getNexlaMetaData().getTags() != null && message.getNexlaMetaData().getTags().get("originalMessageRunId") != null) {
				runId = ((Number) message.getNexlaMetaData().getTags().get("originalMessageRunId")).longValue();
			}
			metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());
			common.addMessageForApproximation(message);
		});
		getStreamSizeByRunId().forEach((runId, streamSize) -> {
			common.updateMetrics(runId, 0, streamSize);
		});


		File localFile = common.uploadRecordsToStorage(records, storage);
		String tempFileLocation = common.getStorageFileLocation(localFile.getName(), storage);

		List<String> columns = common.getSchemaFields().collect(Collectors.toList());

		String sql = getCopyCommand(common.getConfig(),
				common.getFileFormat(),
				tempFileLocation,
				common.getDbDialect().q(common.getConfig().table),
				columns);

		try (TimeLogger ignored = new TimeLogger(logger, "executeFlushBatchInsert::create credentials")) {
			createCredentials(statement);
		}

		try (TimeLogger ignored = new TimeLogger(logger, "executeFlushBatchInsert::execute copy command")) {
			executeCopyCommand(statement, sql);
		}

		common.updateRecordMetrics();

		//check for errors
		manageCopyErrors(getBufferSize(), metricsByRunId.get(0L), connection, tempFileLocation);
		common.cleanupTempFiles(localFile, storage);

		FlushResult result = FlushResult.builder()
				.bufferOffsets(getBuffer().getOffsets())
				.metricsByRunId(metricsByRunId)
				.build();

		common.resetBuffer();
		common.resetMetrics();

		logger.info("M=executeFlushBatchInsert, stage=success, totalBytesSent={}, totalStreamSizeSent={}",
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentBytesTotal.get()).sum(),
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentRecordsTotal.get()).sum());

		return Optional.of(result);
	}

	private String getCheckTableSql() {
		return String.format(
				"SELECT count(*) FROM user_tables " +
						"WHERE table_name LIKE '%s' OR table_name LIKE '%s' OR table_name LIKE '%s' OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY",
				common.getTempTableName(), common.getTempTableName().toLowerCase(), common.getTempTableName().toUpperCase());

	}

	private void executeFlushBatchUpsert(Statement statement, Connection connection) {
		Map<Long, RecordMetric> metricsByRunId = common.getRecordMetricByRunId();
		String qualifiedTempTableName = common.getQualifiedTempTableName();

		if (!common.isTempTableCreated(connection, getCheckTableSql())) {
			createTemporaryTable(statement, qualifiedTempTableName, common.getQualifiedTableName(), common.getTableName());
		}

		logger.info("M=executeFlushBatchUpsert, stage=upsert-started, tempTableName={}", qualifiedTempTableName);

		Long currentMaxIncrementalId = common.retrieveMaxIncrementalIdFromTempTable(statement);

		StreamEx<NexlaMessage> records = common.prepareRecordsForUpsert(currentMaxIncrementalId, common.getConsumer(getBoolMapFn()));

		// Calculate metrics per run ID
		records = records.peek(message -> {
			Long runId = 0L;
			if (message.getNexlaMetaData() != null && message.getNexlaMetaData().getTags() != null && message.getNexlaMetaData().getTags().get("originalMessageRunId") != null) {
				runId = ((Number) message.getNexlaMetaData().getTags().get("originalMessageRunId")).longValue();
			}
			metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());
			common.addMessageForApproximation(message);
		});
		getStreamSizeByRunId().forEach((runId, streamSize) -> {
			common.updateMetrics(runId, 0, streamSize);
		});

		File localFile = common.uploadRecordsToStorage(records, storage);
		String tempFileLocation = common.getStorageFileLocation(localFile.getName(), storage);
		List<String> columns = common.getEnrichedColumns();

		String copyCommand = getCopyCommand(common.getConfig(),
				common.getFileFormat(),
				tempFileLocation,
				common.getDbDialect().q(common.getTempTableName()),
				columns);

		try (TimeLogger ignored = new TimeLogger(logger, "executeFlushBatchUpsert::create credentials")) {
			createCredentials(statement);
		}

		try (TimeLogger ignored = new TimeLogger(logger, "executeFlushBatchUpsert::execute copy command")) {
			executeCopyCommand(statement, copyCommand);
		}

		common.updateRecordMetrics();
		//check for errors
		manageCopyErrors(getBufferSize(), metricsByRunId.get(0L), connection, tempFileLocation);

		common.cleanupTempFiles(localFile, storage);
		common.resetBuffer();

		logger.info("M=executeFlushBatchUpsert, stage=success, totalBytesSent={}, totalStreamSizeSent={}",
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentBytesTotal.get()).sum(),
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentRecordsTotal.get()).sum());
	}

	@SneakyThrows
	private void createCredentials(Statement statement) {
		if (storage instanceof WarehouseCopyS3Storage) {
			AWSAuthConfig authConfig = ((WarehouseCopyS3Storage) storage).getConfig();
			var sql = String.format("{ call DBMS_CLOUD.create_credential (credential_name => '%s',"
					+ " username => '%s',"
					+ " password => '%s')}", authConfig.accessKeyId, authConfig.accessKeyId, authConfig.secretKey);

			try {
				common.logSql(sql);
				statement.execute(sql);
			} catch (SQLException e) {
				if (e.getErrorCode() != CREDENTIALS_ALREADY_EXISTS_ERROR_CODE) {
					// error 20022, credentials already exists, do nothing.
					throw e;
				}
			}
		}
	}

	@SneakyThrows
	private void executeCopyCommand(Statement statement, String copyCommand) {
		try {
			common.logSql(copyCommand);
			statement.execute(copyCommand);
		} catch (SQLException e) {
			if (e.getErrorCode() == REJECT_LIMIT_ERROR_CODE) {
				logger.info("rejectLimit: {} reached for the query: {}.", common.getConfig(), copyCommand);
				// config.rejectLimit reached. Ignoring this since exception will be handled later in manageCopyErrors
			} else {
				throw e;
			}
		}
	}

	@SneakyThrows
	private void createTemporaryTable(Statement st, String tempTable, String qualifiedTableName, String tableName) {
		logger.info("M=createTemporaryTable, tempTableName={}, qualifiedTableName={}", tempTable, qualifiedTableName);
		List<String> fields = new ArrayList<>();

		var mappingConfig = common.getConfig().mappingConfig.get().getMapping();
		String existingColumnsQuery = String.format("SELECT " +
			"    COLUMN_NAME," +
			"    DATA_TYPE || " +
			"    CASE " +
			"        WHEN DATA_TYPE IN ('CHAR', 'VARCHAR2', 'NCHAR', 'NVARCHAR2') THEN '(' || DATA_LENGTH || ')'" +
			"        WHEN DATA_TYPE IN ('NUMBER', 'FLOAT') AND DATA_PRECISION IS NOT NULL THEN " +
			"            '(' || DATA_PRECISION || " +
			"            CASE WHEN DATA_SCALE IS NOT NULL THEN ',' || DATA_SCALE END || ')'" +
			"        ELSE NULL " +
			"    END AS DATA_TYPE" +
			" FROM " +
			"    USER_TAB_COLUMNS" +
			" WHERE " +
			"    TABLE_NAME = '%s'", tableName);
		ResultSet existingColumnsResultSet = common.executeQuery(st, existingColumnsQuery);
		while (existingColumnsResultSet.next()) {
			String columnName = existingColumnsResultSet.getString("COLUMN_NAME");
			String columnDataType = existingColumnsResultSet.getString("DATA_TYPE");
			if (mappingConfig.values().stream().flatMap(m -> m.keySet().stream())
				.collect(toList()).contains(columnName)) {
				fields.add(formatNameAndType(common.escapeField(columnName), columnDataType));
			}
		}

		var dbDialect = common.getDbDialect();
    fields.add(formatNameAndType(common.getIncrementalIdColumnName(), dbDialect.getDbType(Schema.Type.INT64)));
		if (common.getConfig().cdcEnabled) {
			fields.add(formatNameAndType(common.escapeColumnName(DebeziumConstants.NEXLA_OPERATION_DELETE.toUpperCase()), dbDialect.getDbType(Schema.Type.BOOLEAN)));
		}

		var joinedFields = joinFields(fields);
		var createTableCommand = format("CREATE TABLE %s (%s)", tempTable, joinedFields);
		common.executeSQL(st, createTableCommand);
		common.setTempTableExists(true);
	}

	private String joinFields(List<String> fields) {
		return String.join(",", fields);
	}

	private String formatNameAndType(String name, String dbType) {
		return String.format("%s %s", name, dbType);
	}

	private String getCopyCommand(JdbcSinkConnectorConfig config,
								  WarehouseCopyFileFormat fileFormat,
								  String fileLocation,
								  String table,
								  List<String> columns) {

		AWSAuthConfig s3AuthConfig = config.s3AuthConfig;
		String schemaName = Objects.firstNonNull(config.authConfig.schemaName, "");

		String delimiter = nonNull(config.intermediateFileDelimiter) ? config.intermediateFileDelimiter : fileFormat.delimiter;
		String quoteChar = nonNull(config.intermediateFileQuoteChar) ? config.intermediateFileQuoteChar : "''";

		String delimiterStr = fileFormat.format == CSV_FORMAT
				? "'delimiter' value '" + delimiter + "', "
				: "";
		String compressionStr = fileFormat.compression == GZIP
				? "'compression' value 'gzip', "
				: "";

		String fieldList = "";
		if (!SELF_DESCRIBING_FORMATS.contains(fileFormat.format)) {
			String formattedColumns = String.join(" CHAR(10000), ", columns) + " CHAR(10000)";

			fieldList = String.format("field_list => '%s', ", formattedColumns);
		}

		String format = String.format("json_object(" +
						"'ignoremissingcolumns' value 'true', " +
						"'quote' value '%s', " +
						"'type' value '%s', " +
						"'escape' value 'true', " +
						"%s " +
						"%s " +
						"'rejectlimit' value %d, " +
						"'dateformat' value '%s', " +
						"'timestampformat' value '%s'" +
						")",
				quoteChar,
				fileFormat.format.name,
				delimiterStr,
				compressionStr,
				config.rejectLimit,
				config.oracleDateFormat,
				config.oracleTimestampFormat
		);

		if (fileFormat.format == JSON_FORMAT) {
			String columnPath = StreamEx.of(columns)
					.map(s -> s.replaceAll("\"", ""))
					.map(s -> String.format("\"$.%s\"", s))
					.joining(",", "[", "]");

			format = String.format("json_object(" +
							"'type' value '%s', " +
							"%s" +
							"'rejectlimit' value %d, " +
							"'columnpath' value '%s'" +
							")",
					fileFormat.format.name,
					compressionStr,
					config.rejectLimit,
					columnPath
			);
		}

		return String.format("{ call DBMS_CLOUD.copy_data("
						+ "    table_name  => '%s', "
						+ "    schema_name => '%s', "
						+ "    credential_name => '%s', "
						+ "    file_uri_list => '%s', "
						+ "    %s" // fieldList
						+ "    format => %s"
						+ ")}",
				table, schemaName, s3AuthConfig.accessKeyId, fileLocation, fieldList, format);
	}

	@SneakyThrows
	protected void manageCopyErrors(int recordNum,
									RecordMetric recordMetric,
									Connection conn,
									String fileName) {
		String table = common.getTableName();
		JdbcSinkConnectorConfig config = common.getConfig();

		int rowsLoaded = utilityTablesService.rowsLoaded(conn, fileName, table, common.getLogger());
		if (recordNum == rowsLoaded) {
			return;
		}
		Optional<Pair<String, String>> logAndBadFileTablesOptional = utilityTablesService.queryLogAndBadFileTables(
				fileName, conn, common.getLogger(), table);
		if (logAndBadFileTablesOptional.isEmpty()) {
			return;
		}

		List<String> badBodies = utilityTablesService.getFormattedBadRecords(conn,
				logAndBadFileTablesOptional.get().getValue1(), common.getLogger());
		if (badBodies.size() == 0) {
			return;
		}

		boolean rejectLimitReached = recordNum > 0 && rowsLoaded == 0 && (badBodies.size() > config.rejectLimit);
		List<OracleAutonomousErrorMessage> errorDetails = utilityTablesService.createOracleAutonomousErrorMessageList(
				fileName, conn, common.getLogger(), table, config.oracleAutonomousObservableExceptions);
		if (CollectionUtils.isEmpty(errorDetails)) {
			logger.error(format("Unable to retrieve errorDetails. Bad records are: %s LogFile: %s, badFile: %s",
					badBodies, logAndBadFileTablesOptional.get().getValue0(),
					logAndBadFileTablesOptional.get().getValue1()));
			return;
		}

		if (rejectLimitReached) {
			String errorAsJson = OBJECT_MAPPER.writeValueAsString(errorDetails);
			String errorMessage = format("All %d records failed since reject.limit %d reached. \n" +
					"Errors that caused failure: %s", recordNum, config.rejectLimit, errorAsJson);
			logger.info("rejectLimit reached with exception: " + errorMessage);
			throw new OracleAutonomousWholeBatchFailedException(errorMessage);
		} else {
			logger.info(format("rejectLimit of %s not reached. Loaded %d out of %d. Updating quarantine records.",
					config.rejectLimit, rowsLoaded, recordNum));
			List<NexlaMessage> records = getBuffer()
					.getRecords()
					.collect(Collectors.toList()); // TODO fix me this may cause memory issue, we shouldn't load all buffer into memory
			this.updateQuarantineRecords(records, recordMetric, badBodies, errorDetails);
		}
	}

	private void updateQuarantineRecords(
			List<NexlaMessage> records,
			RecordMetric recordMetric,
			List<String> badBodies,
			List<OracleAutonomousErrorMessage> errorDetails) {
		recordMetric.errorRecords.addAndGet(badBodies.size());
		final List<Pair<String, Optional<NexlaMessage>>> badBodyToNexlaMessageMapping
				= bodiesToNexlaMessageMapper.createBadBodyToNexlaMessageMapping(records, badBodies);
		final String badTable = errorDetails.get(0).getBadTable();
		final String logTable = errorDetails.get(0).getLogTable();

		for (Pair<String, Optional<NexlaMessage>> pair : badBodyToNexlaMessageMapping) {
			Optional<OracleAutonomousErrorMessage> errorMessage = errorDetails.stream()
					.filter(el -> el.getBodyInOracle().equals(pair.getValue0())).findAny();
			String error = bodiesToNexlaMessageMapper.mapToSingleError(errorMessage, badTable, logTable);
			if (pair.getValue1().isPresent()) {
				NexlaMessage message = pair.getValue1().get();

				NexlaQuarantineMessage quarantineMessage = new NexlaQuarantineMessage(
						message.getNexlaMetaData(),
						new NexlaErrorMessage(error, ""),
						message.getRawMessage(),
						nowUTC().getMillis());
				recordMetric.quarantineMessages.add(quarantineMessage);
			} else {
				recordMetric.quarantineMessages.add(RecordMetric.quarantineMessage(
						new NexlaMessage(
								new LinkedHashMap<>(),
								new NexlaMetaData()),
						error));
			}
		}
	}

	private void dropTempTable(Statement statement) {
		var tempTableName = common.getQualifiedTempTableName();
		try {
			var truncateCommand = TRUNCATE_TABLE + tempTableName;
			common.executeUpdate(statement, truncateCommand);
			common.dropTempTable(statement, getDropIfExistsSql(tempTableName));
		} catch (Exception e) {
			logger.warn(e.getMessage());
		}
	}

	private void disableParallelDMLIfNecessary(Statement statement) {
		SinkConnectorConfig cfg = common.getConfig();
		// most of the time we do not run cdc and these queries below are necessary
		// XXX: just execute them every time if the performance is not a concern
		if (cfg != null && !cfg.cdcEnabled) {
			logger.info("CDC configuration disabled for Oracle Autonomous DWH sink, disabling DML/DDL parallelism explicitly.");
			List.of("ALTER SESSION DISABLE PARALLEL DML",
							"ALTER SESSION DISABLE PARALLEL DDL",
							"ALTER SESSION DISABLE PARALLEL query")
					.forEach(query -> common.executeSQL(statement, query));
		}
	}

	private String getDropIfExistsSql(String qualifiedTableName) {
		return String.format(
				"BEGIN \n" +
						"   EXECUTE IMMEDIATE 'DROP TABLE %s'; \n" +
						"EXCEPTION \n" +
						"   WHEN OTHERS THEN \n" +
						"      IF SQLCODE != -942 THEN \n" +
						"         RAISE; \n" +
						"      END IF; \n" +
						"END;",
				qualifiedTableName);
	}

	private void dropTempTable(Supplier<Connection> conn) {
		try (Connection connection = conn.get();
			 Statement statement = connection.createStatement()) {
			dropTempTable(statement);
			if (!connection.getAutoCommit()) {
				connection.commit();
			}
		} catch (Exception e) {
			logger.warn(e.getMessage());
		}
	}

	private Function<Object, Integer> getBoolMapFn(){
		return v -> Boolean.parseBoolean(v.toString()) ? 1 : 0;
	}
}
