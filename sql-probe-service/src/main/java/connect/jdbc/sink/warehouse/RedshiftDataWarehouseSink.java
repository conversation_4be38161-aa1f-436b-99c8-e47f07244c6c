package connect.jdbc.sink.warehouse;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.nexla.common.ConverterUtils;
import com.nexla.common.MetricUtils;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.exception.NexlaErrorMessage;
import com.nexla.common.exception.NexlaQuarantineMessage;
import com.nexla.common.logging.TimeLogger;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.connect.common.cdc.DebeziumConstants;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.jdbc.sink.dialect.copy.CopyOperationLocalBuffer;
import connect.jdbc.sink.dialect.copy.FlushResult;
import connect.jdbc.sink.dialect.copy.SinkCopyOperationCommon;
import connect.jdbc.sink.dialect.copy.storage.WarehouseCopyTempStorage;
import connect.jdbc.util.WarehouseUtils;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;

import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.INSERT;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.UPSERT;
import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.Compression.GZIP;
import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.Format.CSV_FORMAT;
import static connect.jdbc.sink.dialect.copy.CopyOperationHelper.stlLoadErrorFetchQuery;
import static connect.jdbc.util.WarehouseUtils.MAX_ERROR_LIMIT;

public class RedshiftDataWarehouseSink implements DataWarehouseSink {

	private static final String LINE_NUMBER = "line_number";
	private static final List<String> ERROR_TABLE_COLUMNS = List.of("colname", "type", "col_length", "position", "raw_line", "raw_field_value", "err_reason", LINE_NUMBER);
	private final SinkCopyOperationCommon common;

	private final Logger logger;

	private final WarehouseCopyTempStorage storage;
	private final boolean isServerless;

	public RedshiftDataWarehouseSink(SinkCopyOperationCommon common) {
		this.common = common;
		this.logger = common.getLogger();
		this.storage = WarehouseUtils.getCopyOperationTempStorage(common.getConfig());
		this.isServerless = WarehouseUtils.isRedshiftServerless(common.getConfig());
	}

	@Override
	public void writeData(List<NexlaMessageContext> records, Map<Long, Integer> streamSizeByRunId) {
		common.writeData(records, streamSizeByRunId);
	}

	@Override
	public void onConnectorStop(Supplier<Connection> connProvider) {
		common.dropTempTable(connProvider);
	}

	@Override
	public void onConnectorStart(Supplier<Connection> connProvider) {
		common.dropTempTable(connProvider);
	}

	public CopyOperationLocalBuffer getBuffer() {
		return common.getBuffer();
	}

	@Override
	public int getBufferSize() {
		return common.getBuffer().streamSize();
	}

	@Override
	public Map<Long, Integer> getStreamSizeByRunId() {
		return common.getBuffer().streamSizeByRunId();
	}

	@Override
	public int getCurrentBatchSize() {
		return common.getTotalStreamSizeSentByRunId().values().stream().mapToInt(Integer::intValue).sum();
	}

	@SneakyThrows
	@Override
	public Optional<FlushResult> flushBatch(Connection connection) {
		if (getBufferSize() > 0) {
			try (Statement statement = connection.createStatement()) {
				var insertMode = common.getConfig().insertMode;
				if (UPSERT == insertMode) {
					executeFlushBatchUpsert(statement, connection);
					return Optional.empty();
				} else if (INSERT == insertMode) {
					return executeFlushBatchInsert(statement);
				}
			}
		}

		return Optional.empty();
	}

	@SneakyThrows
	@Override
	public Optional<FlushResult> flushPipeline(Connection connection) {
		int streamSize = getBufferSize();
		if (streamSize > 0) {
			return Optional.empty();
		}

		Map<Long, RecordMetric> metricsByRunId = common.getRecordMetricByRunId();

		try (Statement statement = connection.createStatement()) {
			String tempTableName = common.getQualifiedTempTableName();

			if (!common.isTempTableCreated(connection)) {
				return Optional.empty();
			} else {
				boolean hasRecords = common.tempTableHasRecords(statement);
				if (!hasRecords) {
					// if temp table is empty and has no data we have to drop it
					common.dropTempTable(statement);

					// it means all records failed to be loaded to temp table
					if (metricsByRunId.values().stream().anyMatch(metric -> metric.errorRecords.get() > 0)) {
						FlushResult result = FlushResult.builder()
								.metricsByRunId(metricsByRunId)
								.bufferOffsets(common.getLatestProcessedOffsets())
								.build();

						logger.info("M=flushPipeline, stage=success, no data to merge, errorRecords={}", 
							metricsByRunId.values().stream().mapToLong(metric -> metric.errorRecords.get()).sum());

						common.resetMetrics();

						return Optional.of(result);
					}

					return Optional.empty();
				}
			}

			logger.info("M=flushPipeline, stage=flush-started, tempTableName={}", tempTableName);

			common.removeDuplicatesFromTempTable(statement);
			common.deleteDataFromDestinationTable(statement);
			common.copyDataFromTempToDestinationTable(statement, "FALSE");

			common.dropTempTable(statement);
			common.updateRecordMetrics();
		}

		FlushResult result = FlushResult.builder()
				.metricsByRunId(metricsByRunId)
				.bufferOffsets(common.getLatestProcessedOffsets())
				.build();

		common.resetMetrics();

		logger.info("M=flushPipeline, stage=success, totalBytesSent={}, totalStreamSizeSent={}",
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentBytesTotal.get()).sum(),
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentRecordsTotal.get()).sum());

		return Optional.of(result);
	}

	@Override
	@SneakyThrows
	public Optional<FlushResult> directFlush(Connection connection, List<File> files, List<String> columnsPassed) {
		var columns = CollectionUtils.isNotEmpty(columnsPassed)
				? common.formatColumnsForQuery(columnsPassed)
				: common.getColumnsForQuery();

		var qualifiedTableName = common.getQualifiedTableName();

		files.forEach(storage::uploadFile);

		Map<Long, RecordMetric> metricsByRunId = new HashMap<>();
		RecordMetric recordMetric = new RecordMetric();
		metricsByRunId.put(0L, recordMetric);

		try (Statement statement = connection.createStatement()) {

			files.forEach(it -> {
				var storageFileLocation = common.getStorageFileLocation(it.getName(), storage);
				var copyCommand = getCopyCommand(common.getConfig(),
						common.getFileFormat(),
						storageFileLocation,
						qualifiedTableName,
						columns);
				var rowsUpdated = common.executeUpdate(statement, copyCommand);
				recordMetric.sentRecordsTotal.addAndGet(rowsUpdated);
			});

			files.forEach(file -> common.cleanupTempFiles(file, storage));
		}

		logger.info("M=directFlush, stage=success, filesSize={}, tableName={}, sentRecordsTotal={}",
				files.size(),
				qualifiedTableName,
				recordMetric.sentRecordsTotal.get());

		FlushResult flushResult = FlushResult.builder()
				.metricsByRunId(metricsByRunId)
				.build();

		return Optional.of(flushResult);
	}

	@SneakyThrows
	@Override
	public void truncateTable(Connection connection, int streamSize) {
		common.resetBuffer();
		try (Statement statement = connection.createStatement()) {
			common.dropTempTable(statement);
			common.executeTruncate(statement);
		}
		common.updateMetrics(0L, 1, streamSize);
	}

	@SneakyThrows
	private Optional<FlushResult> executeFlushBatchInsert(Statement statement) {
		Map<Long, RecordMetric> metricsByRunId = common.getRecordMetricByRunId();
		StreamEx<NexlaMessage> records = getBuffer().getRecords();

		String qualifiedTableName = common.getQualifiedTableName();

		logger.info("M=executeFlushBatchInsert, stage=insert-started, qualifiedTableName={}", qualifiedTableName);

		// Calculate metrics per run ID
		records = records.peek(message -> {
			Long runId = 0L;
			if (message.getNexlaMetaData() != null && message.getNexlaMetaData().getTags() != null && message.getNexlaMetaData().getTags().get("originalMessageRunId") != null) {
				runId = ((Number) message.getNexlaMetaData().getTags().get("originalMessageRunId")).longValue();
			}
			metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());
			common.addMessageForApproximation(message);
		});
		getStreamSizeByRunId().forEach((runId, streamSize) -> {
			common.updateMetrics(runId, 0, streamSize);
		});

		File localFile = common.uploadRecordsToStorage(records, storage);
		String tempFileLocation = common.getStorageFileLocation(localFile.getName(), storage);

		String columns = common.getColumnsForQuery();

		String sql = getCopyCommand(common.getConfig(),
				common.getFileFormat(),
				tempFileLocation,
				qualifiedTableName,
				columns);
		try (TimeLogger ignored = new TimeLogger(logger, "executeFlushBatchInsert::execute copy command")) {
			common.executeSQL(statement, sql);
		}

		common.updateRecordMetrics();

		//check for errors
		manageCopyErrors(getBufferSize(), metricsByRunId.get(0L), statement, tempFileLocation, false);
		common.cleanupTempFiles(localFile, storage);

		FlushResult result = FlushResult.builder()
				.bufferOffsets(getBuffer().getOffsets())
				.metricsByRunId(metricsByRunId)
				.build();

		common.resetBuffer();
		common.resetMetrics();

		logger.info("M=executeFlushBatchInsert, stage=success, totalBytesSent={}, totalStreamSizeSent={}",
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentBytesTotal.get()).sum(),
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentRecordsTotal.get()).sum());

		return Optional.of(result);
	}

	private void executeFlushBatchUpsert(Statement statement, Connection connection) {
		Map<Long, RecordMetric> metricsByRunId = common.getRecordMetricByRunId();
		String tempTableName = common.getQualifiedTempTableName();

		if (!common.isTempTableCreated(connection)) {
			createTemporaryTable(statement, tempTableName, common.getQualifiedTableName());
		}

		logger.info("M=executeFlushBatchUpsert, stage=upsert-started, tempTableName={}", tempTableName);

		Long currentMaxIncrementalId = common.retrieveMaxIncrementalIdFromTempTable(statement);

		StreamEx<NexlaMessage> records = common.prepareRecordsForUpsert(currentMaxIncrementalId, common.getConsumer(v -> v));

		// Calculate metrics per run ID
		Map<Long, Long> bytesSentPerRunId = new HashMap<>();
		records = records.peek(message -> {
			Long runId = 0L;
			if (message.getNexlaMetaData() != null && message.getNexlaMetaData().getTags() != null && message.getNexlaMetaData().getTags().get("originalMessageRunId") != null) {
				runId = ((Number) message.getNexlaMetaData().getTags().get("originalMessageRunId")).longValue();
			}
			metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());
			common.addMessageForApproximation(message);
		});
		getStreamSizeByRunId().forEach((runId, streamSize) -> {
			common.updateMetrics(runId, 0, streamSize);
		});

		File localFile = common.uploadRecordsToStorage(records, storage);
		String tempFileLocation = common.getStorageFileLocation(localFile.getName(), storage);
		String columns = common.getEnrichedColumnsForQuery();

		String copyCommand = getCopyCommand(common.getConfig(),
				common.getFileFormat(),
				tempFileLocation,
				tempTableName,
				columns);
		try (TimeLogger ignored = new TimeLogger(logger, "executeFlushBatchUpsert::execute copy command")) {
			common.executeSQL(statement, copyCommand);
		}

		common.updateRecordMetrics();

		//check for errors
		manageCopyErrors(getBufferSize(), metricsByRunId.get(0L), statement, tempFileLocation, true);

		common.cleanupTempFiles(localFile, storage);
		common.resetBuffer();

		logger.info("M=executeFlushBatchUpsert, stage=success, totalBytesSent={}, totalStreamSizeSent={}",
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentBytesTotal.get()).sum(),
				metricsByRunId.values().stream().mapToLong(metric -> metric.sentRecordsTotal.get()).sum());
	}

	@SneakyThrows
	private void createTemporaryTable(Statement st, String tempTable, String qualifiedTableName) {
		logger.info("M=createTemporaryTable, tempTableName={}, qualifiedTableName={}", tempTable, qualifiedTableName);

		var createTableCommand = String.format("CREATE TABLE %s (LIKE %s)", tempTable, qualifiedTableName);
		common.executeSQL(st, createTableCommand);
		common.setTempTableExists(true);

		var incrIdColAlter = String.format("ALTER TABLE %s ADD %s BIGINT", tempTable, common.getIncrementalIdColumnName());
		common.executeSQL(st, incrIdColAlter);

		if(common.getConfig().cdcEnabled) {
			var delColAlter = String.format("ALTER TABLE %s ADD %s BOOLEAN", tempTable, common.escapeColumnName(
					DebeziumConstants.NEXLA_OPERATION_DELETE.toUpperCase()));
			common.executeSQL(st, delColAlter);
		}
	}

	private String getCopyCommand(JdbcSinkConnectorConfig config,
								  WarehouseCopyFileFormat fileFormat,
								  String fileLocation,
								  String table,
								  String columns) {

		String compression = fileFormat.compression == GZIP ? "GZIP " : "";

		String command = storage.getSinkCopyQuery(table, columns, fileLocation, "", compression, "", "", false);

		command += "TIMEFORMAT " + config.redshiftTimeFormat + " " + "DATEFORMAT " + config.redshiftDateFormat + " ";

		if (fileFormat.format == CSV_FORMAT) {
			command += "ESCAPE ";
			command += "DELIMITER '" + fileFormat.delimiter + "' ";
			command += "NULL as " + config.nullAs;
		} else {
			command += "FORMAT AS JSON 'auto ignorecase'";
		}

		if (!config.stopOnError) {
			command += " MAXERROR " + MAX_ERROR_LIMIT;
		}
		if (config.emptyToNull) {
			command += " EMPTYASNULL";
		}
		return command;
	}

	protected void manageCopyErrors(int recordNum,
									RecordMetric recordMetric,
									Statement statement,
									String fileName,
									boolean isUpsert) {

		var lastCopyCount = selectLastCopyCount(statement);

		var allFailed = recordNum > 0 &&
				lastCopyCount.isPresent() &&
				lastCopyCount.get() == 0 &&
				recordMetric.quarantineMessages.isEmpty() &&
				!isUpsert;

		if (allFailed) {
			List<Map<String, String>> errorDetails = selectErrorDetails(statement, fileName);
			throw (errorDetails
					.stream()
					.findFirst()
					.map(Map::toString)
					.map(RuntimeException::new)
					.orElseGet(RuntimeException::new));
		} else if (lastCopyCount.isPresent() && lastCopyCount.get() != recordNum) {
			updateErrorMetrics(statement, recordMetric, fileName);
		}
	}

	private Optional<Integer> selectLastCopyCount(Statement statement) {
		try (ResultSet rs = common.executeQuery(statement, "SELECT pg_last_copy_count()")) {
			if (rs.next()) {
				return Optional.of(ConverterUtils.toInteger(rs.getObject("pg_last_copy_count")));
			}

			return Optional.empty();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return Optional.empty();
		}
	}

	private List<Map<String, String>> selectErrorDetails(Statement statement, String fileName) {
		if (common.getConfig().skipCopyErrorHandling) {
			return Collections.emptyList();
		}

		try (ResultSet rs = common.executeQuery(statement, stlLoadErrorFetchQuery(Optional.of(fileName), this.isServerless))) {

			List<Map<String, String>> result = Lists.newArrayList();

			while (rs.next()) {
				Map<String, String> row = ERROR_TABLE_COLUMNS
						.stream()
						.map(col -> Pair.of(col, getColumnValue(rs, col)))
						.collect(Collectors.toMap(Pair::getLeft, Pair::getRight));

				result.add(row);
			}

			return result;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return List.of();
		}
	}

	@SneakyThrows
	private String getColumnValue(ResultSet rs, String col) {
		return rs.getObject(col).toString().trim();
	}

	private void updateErrorMetrics(Statement statement, RecordMetric recordMetric, String fileName) {
		List<Map<String, String>> errorDetails = selectErrorDetails(statement, fileName);

		Set<Long> lineNumbers = Sets.newHashSet();

		errorDetails.forEach(it -> {

			if (it.containsKey(LINE_NUMBER)) {
				Long lineNumber = Long.valueOf(it.get(LINE_NUMBER));
				lineNumbers.add(lineNumber);
				it.remove(LINE_NUMBER);
			}

			NexlaQuarantineMessage quarantineMessage = new NexlaQuarantineMessage(
					new NexlaMetaData(),
					new NexlaErrorMessage(it.toString(), it.toString()),
					Maps.newLinkedHashMap(it),
					nowUTC().getMillis());

			recordMetric.quarantineMessages.add(quarantineMessage);
		});

		recordMetric.errorRecords.addAndGet(lineNumbers.size());
	}
}
