package connect.jdbc.sink.dialect.type;

public enum FireboltType {
	INT("INT"),
	INTEGER("INTEGER"),
	BIGINT("BIGINT"),
	LONG("LONG"),
	FLOAT("FLOAT"),
	DOUBLE("DOUBLE"),
	DOUBLE_PRECISION("DOUBLE PRECISION"),
	VARCHAR("VARCHAR"),
	TEXT("TEXT"),
	STRING("STRING"),
	DATE("DATE"),
	TIMESTAMP("TIMESTAMP"),
	DATETIME("DATETIME"),
	BOOLEAN("BOOLEAN"),
	ARRAY("ARRAY");

	public final String type;

	FireboltType(String type) {
		this.type = type;
	}
}
