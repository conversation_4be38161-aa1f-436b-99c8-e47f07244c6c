package connect.jdbc.sink.dialect.copy.storage;

import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;

public class SnowflakeCopyS3Storage extends WarehouseCopyS3Storage {

	public SnowflakeCopyS3Storage(
			AWSAuthConfig config,
			String tempUploadBucket,
			String tempUploadPrefix,
			boolean deleteTempBucket
	) {
		super(config, tempUploadBucket, tempUploadPrefix, deleteTempBucket);
	}

	@Override
	public String getSourceCopyQuery(WarehouseCopyFileFormat format,
									 String destination,
									 String source,
									 String compression,
									 String single,
									 boolean optionallyEscaped) {
		if (WarehouseCopyFileFormat.Format.CSV_FORMAT == format.format) {
			String escapeStr = optionallyEscaped ? "  ESCAPE ='\\\\'  ESCAPE_UNENCLOSED_FIELD='\\\\' " : "";

			return "COPY INTO " + destination +
					"  FROM (" + source + ") CREDENTIALS = " +
					"  (AWS_KEY_ID = '" + config.accessKeyId + "' aws_secret_key = '" + config.secretKey + "') " +
					"  FILE_FORMAT = (TYPE = csv FIELD_OPTIONALLY_ENCLOSED_BY = '\"' " + escapeStr +
					"   FIELD_DELIMITER = '" + format.delimiter + "'" + compression + " NULL_IF = (''))" +
					"  OVERWRITE = TRUE HEADER = TRUE" + single;
		}

		if (WarehouseCopyFileFormat.Format.JSON_FORMAT == format.format) {
			return "COPY INTO " + destination +
					"  FROM (SELECT OBJECT_CONSTRUCT(*) FROM (" + source + ")) CREDENTIALS = " +
					"  (AWS_KEY_ID = '" + config.accessKeyId + "' aws_secret_key = '" + config.secretKey + "') " +
					"  FILE_FORMAT = (TYPE = json" + compression + ")" +
					"  OVERWRITE = TRUE" + single;
		}

		throw new UnsupportedOperationException("Only CSV and JSON format supported!");
	}

	@Override
	public String getSinkCopyQuery(
			String table,
			String columns,
			String destination,
			String format,
			String compression,
			String skipHeaders,
			String columnMatchMode,
			boolean stopOnError
	) {
		return "COPY INTO " + (table + columns) +
				"  FROM " + destination + " CREDENTIALS = " +
				"  (AWS_KEY_ID = '" + config.accessKeyId + "' aws_secret_key = '" + config.secretKey + "') " +
				"  FILE_FORMAT = (TYPE = " + format + compression + skipHeaders + ") " +
				columnMatchMode +
				(stopOnError ? "" : " ON_ERROR = CONTINUE ");
	}
}
