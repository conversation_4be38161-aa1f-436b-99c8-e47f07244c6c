package connect.jdbc.sink.dialect.type;

public enum SnowflakeType {
	NUMBER("NUMBER"),
	DECIMA<PERSON>("DECIMAL"),
	NUMERIC("NUMERIC"),
	INT("INT"),
	INTEGER("INTEGER"),
	BIGINT("BIGINT"),
	SMALLINT("SMALLINT"),
	FLOAT("FLOAT"),
	FLOAT4("FLOAT4"),
	FLOAT8("FLOAT8"),
	DOUBLE("DOUBLE"),
	DOUBLE_PRECISION("DOUBLE PRECISION"),
	REAL("REAL"),
	VARCHAR("VARCHAR"),
	CHAR("CHAR"),
	CHARACTER("CHARACTER"),
	STRING("STRING"),
	TEXT("TEXT"),
	BINARY("BINARY"),
	VARBINARY("VARBINARY"),
	BOOLEAN("BOOLEAN"),
	<PERSON>AT<PERSON>("DATE"),
	DATETIM<PERSON>("DATETIME"),
	TIME("TIME"),
	TIMESTAMP("TIMESTAMP"),
	TIMESTAMP_LTZ("TIMESTAMP_LTZ"),
	TIMESTAMP_NTZ("TIMESTAMP_NTZ"),
	TIMESTAMP_TZ("TIMESTAMP_TZ"),
	VARIANT("VARIANT"),
	OBJECT("OBJECT"),
	ARRAY("ARRAY");

	public final String type;

	SnowflakeType(String type) {
		this.type = type;
	}
}