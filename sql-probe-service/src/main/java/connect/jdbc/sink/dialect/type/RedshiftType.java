package connect.jdbc.sink.dialect.type;

public enum RedshiftType {
	SMALLINT("SMALLINT"),
	INT2("INT2"),
	INTEGER("INTEGER"),
	INT("INT"),
	INT4("INT4"),
	<PERSON>IGIN<PERSON>("BIGINT"),
	INT8("INT8"),
	<PERSON><PERSON><PERSON><PERSON>("DECIMAL(18,4)"),
	<PERSON>UMERIC("NUMERIC(18,4)"),
	REAL("REAL"),
	FLOAT4("FLOAT4"),
	DOUBLE_PRECISION("DOUBLE PRECISION"),
	FLOAT8("FLOAT8"),
	BOOLEAN("BOOLEAN"),
	BOOL("BOOL"),
	CHAR("CHAR"),
	CHARACTER("CHARACTER"),
	VARC<PERSON>R("VARCHAR"),
	CHARACTER_VARYING("CHARACTER VARYING"),
	<PERSON>AT<PERSON>("DATE"),
	<PERSON><PERSON><PERSON><PERSON><PERSON>("TIMESTAMP"),
	TIMES<PERSON>MPTZ("TIMESTAMPTZ"),
	TIME("TIME"),
	TIM<PERSON><PERSON>("TIMETZ");

	public final String type;

	RedshiftType(String type) {
		this.type = type;
	}
}