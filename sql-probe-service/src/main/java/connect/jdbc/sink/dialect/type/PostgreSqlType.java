package connect.jdbc.sink.dialect.type;

public enum PostgreSqlType {
	BIGINT("BIGINT"),
	INT8("INT8"),
	BIGSERIAL("BIGSERIAL"),
	SERIAL8("SERIAL8"),
	BIT("BIT"),
	<PERSON>IT_VARYING("BIT VARYING"),
	VA<PERSON><PERSON>("<PERSON><PERSON><PERSON>"),
	BOOLEAN("B<PERSON><PERSON>EA<PERSON>"),
	BOOL("BOOL"),
	BOX("BOX"),
	BYTEA("BYTEA"),
	CHAR("CHAR"),
	VARCHAR("VARCHAR"),
	CHARACTER_VARYING("CHARACTER VARYING"),
	CIDR("CIDR"),
	CITEXT("CITEXT"),
	CIRCLE("CIRCLE"),
	DATE("DATE"),
	DOUBLE_PRECISION("DOUBLE PRECISION"),
	INET("INET"),
	INTEGER("INTEGER"),
	INT("INT"),
	INT4("INT4"),
	INTER<PERSON><PERSON>("INTERVAL"),
	JSON("JSON"),
	J<PERSON>NB("JSON<PERSON>"),
	LINE("LINE"),
	LSEG("LSEG"),
	MACADDR("MACADDR"),
	MONEY("MONEY"),
	NUMERIC("NUMERIC"),
	DECIMAL("DECIMAL"),
	PATH("PATH"),
	PG_LSN("PG_LSN"),
	POINT("POINT"),
	POLYGON("POLYGON"),
	REAL("REAL"),
	FLOAT4("FLOAT4"),
	SMALLINT("SMALLINT"),
	INT2("INT2"),
	SMALLSERIAL("SMALLSERIAL"),
	SERIAL2("SERIAL2"),
	SERIAL("SERIAL"),
	SERIAL4("SERIAL4"),
	TEXT("TEXT"),
	TIME("TIME"),
	TIMETZ("TIMETZ"),
	TIMESTAMP("TIMESTAMP"),
	TIMESTAMPTZ("TIMESTAMPTZ"),
	TSQUERY("TSQUERY"),
	TSVECTOR("TSVECTOR"),
	TXID_SNAPSHOT("TXID_SNAPSHOT"),
	UUID("UUID"),
	XML("XML");

	public final String type;

	PostgreSqlType(String type) {
		this.type = type;
	}

	public static boolean isVarcharType(String type) {
		try {
			PostgreSqlType resType = PostgreSqlType.valueOf(type.toUpperCase());
			switch (resType) {
				case UUID:
				case CITEXT:
				case CIDR:
				case INET:
				case MACADDR:
				case TSQUERY:
				case TSVECTOR:
					return true;
			}

		} catch (Exception e) {
			return false;
		}
		return false;
	}
}