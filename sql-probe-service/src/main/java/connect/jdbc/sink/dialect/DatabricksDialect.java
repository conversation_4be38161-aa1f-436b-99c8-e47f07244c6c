package connect.jdbc.sink.dialect;

import com.nexla.connector.config.databricks.DatabricksCloudType;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.data.Date;
import connect.data.Decimal;
import connect.data.Schema;
import connect.data.Time;
import connect.data.Timestamp;
import connect.jdbc.sink.dialect.copy.SinkCopyOperation;
import connect.jdbc.sink.dialect.copy.SinkDatabricksAzureCopyOperation;
import connect.jdbc.sink.dialect.copy.SinkDatabricksS3CopyOperation;
import connect.jdbc.sink.dialect.copy.SinkDatabricksUnityVolumeCopyOperation;
import connect.jdbc.sink.dialect.type.DatabricksType;
import java.util.Collections;
import one.util.streamex.StreamEx;
import com.google.common.collect.Sets;

import java.sql.ResultSet;
import java.util.EnumSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static connect.jdbc.sink.dialect.DialectFeature.COPY_INSERT;
import static connect.jdbc.sink.dialect.DialectFeature.COPY_UPSERT;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static org.apache.commons.lang3.StringUtils.isEmpty;

public class DatabricksDialect extends DbDialect {

	private static final Set<DialectFeature> SPARK_DIALECT_FEATURES = EnumSet.of(COPY_INSERT, COPY_UPSERT);

	public DatabricksDialect() {
		super("`", "`", empty(), empty(), empty(), false);
	}

	@Override
	public String getQualifiedTableName(String table, String schemaName, String databaseName) {
		StringBuilder tableName = new StringBuilder();

		if (!isEmpty(databaseName)) {
			tableName.append(q(databaseName)).append(".");
		}

		if (!isEmpty(schemaName)) {
			tableName.append(q(schemaName)).append(".");
		}

		tableName.append(q(table));

		return tableName.toString();
	}

	@Override
	public String getQualifiedSchemaName(String schemaName, String databaseName) {
		if (!isEmpty(schemaName)) {
			if (!isEmpty(databaseName)) {
				return q(databaseName) + "." + q(schemaName);
			}
			return q(schemaName);
		}

		return getDefaultSchemaName().map(this::q).orElse("");
	}

	@Override
	public Set<WarehouseCopyFileFormat> sinkFileFormats() {
		return Sets.newHashSet(WarehouseCopyFileFormat.JSON);
	}

	@Override
	public Optional<WarehouseCopyFileFormat> defaultSinkFileFormat() {
		return Optional.of(WarehouseCopyFileFormat.JSON);
	}

	@Override
	public Set<String> systemSchemas() {
		return Set.of("default");
	}

	@Override
	public StreamEx<String> getSqlTypes() {
		return StreamEx
				.of(DatabricksType.values())
				.map(x -> x.type);
	}

	@Override
	public String getDbTypeByKafka(String schemaName, Map<String, String> parameters) {
		switch (schemaName) {
			case Decimal.LOGICAL_NAME:
				return DatabricksType.DECIMAL.type;
			case Date.LOGICAL_NAME:
				return DatabricksType.DATE.type;
			case Time.LOGICAL_NAME:
			case Timestamp.LOGICAL_NAME:
				return DatabricksType.TIMESTAMP.type;
			default:
				return null;
		}
	}

	@Override
	public String getDbType(Schema.Type type) {
		switch (type) {
			case INT8:
				return "BYTE";
			case INT16:
				return "SHORT";
			case INT32:
				return "INT";
			case INT64:
				return "LONG";
			case FLOAT32:
				return "FLOAT";
			case FLOAT64:
				return "DOUBLE";
			case BOOLEAN:
				return "BOOLEAN";
			case STRING:
				return "STRING";
			case BYTES:
				return "BINARY";
			default:
				return null;
		}
	}

	@Override
	public ResultSet fixResultSet(ResultSet resultSet) {
		return new HiveResultSet(resultSet);
	}

	@Override
	public Set<DialectFeature> getFeatures() {
		return SPARK_DIALECT_FEATURES;
	}

	@Override
	public boolean supportPrimaryKeys() {
		return false;
	}

	@Override
	public SinkCopyOperation newSinkCopyOperation(JdbcSinkConnectorConfig config) {
		final DatabricksCloudType databricksCloudType = config.authConfig.databricksCloudType;
		return new SinkDatabricksUnityVolumeCopyOperation();
//
//		if (databricksCloudType == DatabricksCloudType.AWS) {
//			return new SinkDatabricksS3CopyOperation();
//		} else if (databricksCloudType == DatabricksCloudType.AZURE){
//			return new SinkDatabricksAzureCopyOperation();
//		} else if (databricksCloudType == DatabricksCloudType.DATABRICKS){
//			return new SinkDatabricksUnityVolumeCopyOperation();
//		} else {
//			throw new IllegalArgumentException("Unsupported Databricks cloud type: " + databricksCloudType);
//		}
	}

	@Override
	public boolean isAutoCommit() {
		return true;
	}

	@Override
	public String getTargetName(String originalName) {
		return TableNameMapper.getName(originalName.toLowerCase(), TableNameMapper.groupNamePattern2, false, 126, "[a-z0-9_]", TableNameMapper.prefix.toLowerCase());
	}
}
