package connect.jdbc.sink.dialect.copy.parser;

import com.nexla.common.logging.NexlaLogger;
import connect.jdbc.sink.dialect.copy.exception.OracleAutonomousErrorMessage;
import lombok.SneakyThrows;
import oracle.jdbc.OracleClob;
import org.apache.commons.io.IOUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.lang.String.format;
import static java.util.function.Predicate.not;

public class OracleAutonomousExceptionParser {
	public List<OracleAutonomousErrorMessage> constructErrorToFieldMap(List<String> badBodies, List<String> logErrorBodies,
																	   String logFileTableName, String badFileTableName,
																	   NexlaLogger logger) {
		Map<String, String> errorToFieldMap = new HashMap<>();
		if (logErrorBodies.size() == badBodies.size()) {
			// if we parsed LOG table and BAD table correctly, map them 1 to 1.
			for (int i = 0; i < badBodies.size(); i++) {
				errorToFieldMap.put(logErrorBodies.get(i), badBodies.get(i));
			}
		} else {
			logger.info("Unable to create 1 to 1 mapping between log and bad records.");
			for (int i = 0; i < badBodies.size(); i++) {
				errorToFieldMap.put(format("Unable to insert record number %d. ", i + 1), badBodies.get(i));
			}
		}
		return errorToFieldMap.entrySet().stream()
				.map(entry -> new OracleAutonomousErrorMessage(entry.getKey(), entry.getValue(),
						logFileTableName, badFileTableName))
				.collect(Collectors.toList());
	}

	public List<String> groupLogFiles(List<String> logFiles) {
		if (logFiles == null) {
			return new ArrayList<>();
		}
		if (CollectionUtils.isEmpty(logFiles)) {
			return logFiles;
		}

		String[] lines = logFiles.stream()
				.filter(Objects::nonNull)
				.filter(not(el -> el.contains("Warning")))
				.collect(Collectors.joining("\n"))
				.split("\\r?\\n");

		List<String> result = new ArrayList<>();
		StringBuilder currentRecord = new StringBuilder();
		for (String line : lines) {
			// each new log starts with KUP-04101: for example record 3 rejected in file https://temp-nexla.s3...
			if (line.startsWith("KUP-04101: record")) {
				result.add(currentRecord.toString());
				currentRecord.setLength(0);
			}
			currentRecord.append(line).append("; ");
		}
		result.add(currentRecord.toString());

		return result;
	}

	public List<String> getBadBodies(List<Object> bads) {
		return bads.stream()
				.filter(Objects::nonNull)
				.map(el -> (OracleClob) el)
				.map(this::clobToString)
				.collect(Collectors.toList());
	}

	@SneakyThrows
	private String clobToString(OracleClob clob) {
		return IOUtils.toString(clob.getCharacterStream());
	}
}
