package connect.jdbc.sink.dialect.type;

public enum DatabricksType {
    BOOLEAN("BOOLEAN"),
    BYTE("BYTE"),
    TINYINT("TINYINT"),
    SHORT("SHORT"),
    SMALLINT("SMALLINT"),
    INT("INT"),
    LONG("LONG"),
    <PERSON><PERSON>INT("BIGINT"),
    FLOAT("FLOAT"),
    REAL("REAL"),
    DOUBLE("DOUBLE"),
    DATE("DATE"),
    TIMESTAMP("TIMESTAMP"),
    STRING("STRING"),
    BINARY("BINARY"),
    DECIMAL("DECIMAL"),
    NUMERIC("NUMERIC"),
    ARRAY("ARRAY"),
    STRUCT("STRUCT"),
    MAP("MAP");

    public final String type;

    DatabricksType(String type) {
        this.type = type;
    }
}
