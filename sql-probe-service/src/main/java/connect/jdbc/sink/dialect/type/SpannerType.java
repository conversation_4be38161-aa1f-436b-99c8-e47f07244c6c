package connect.jdbc.sink.dialect.type;

public enum SpannerType {

    ARRAY("ARRAY"),
    BOOLEAN("BOOL"),
    BIT("BOOL"),
    BIGINT("INT64"),
    SMALLINT("INT64"),
    TINYINT("INT64"),
    INTEGER("INT64"),
    CH<PERSON>("STRING(1)"),
    VARCHAR("STRING(MAX)"),
    NVARCHAR("STRING(MAX)"),
    FLOAT("FLOAT64"),
    DOUBLE("FLOAT64"),
    DATE("DATE"),
    TIME("TIMESTAMP"),
    TIMESTAMP("TIMESTAMP"),
    VARBINARY("BYTES(MAX)"),
    BINARY("BYTES(MAX)"),
    LONGVARCHAR("STRING(MAX)"),
    LONGVARBINARY("BYTES(MAX)"),
    CLOB("STRING(MAX)"),
    NCLOB("STRING(MAX)"),
    BLOB("BYTES(MAX)"),
    DECIMAL("NUMERIC"),
    NUMERIC("NUMERIC");

    public final String type;

    SpannerType(String type) {
        this.type = type;
    }

}
