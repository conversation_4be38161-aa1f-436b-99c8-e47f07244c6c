package connect.jdbc.sink.dialect.copy.filewriter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexla.common.NexlaMessage;
import com.nexla.common.StreamUtils;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;

import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

public class JsonDataFileWriter extends DataFileWriter {

	// Disable auto close of writer stream.
	private static final ObjectMapper OBJECT_MAPPER = StreamUtils.OBJECT_MAPPER.copy().configure(JsonGenerator.Feature.AUTO_CLOSE_TARGET, false);

	public JsonDataFileWriter(WarehouseCopyFileFormat fileFormat) {
		super(fileFormat);
	}

	@SneakyThrows
	protected void writeOutputStream(OutputStream outputStream,
	                                 StreamEx<NexlaMessage> records,
	                                 BiConsumer<Exception, NexlaMessage> onError,
									 Consumer<NexlaMessage> onSuccess) {

		try (OutputStreamWriter jsonWriter = new OutputStreamWriter(outputStream)) {
			records.forEach(message -> {
				try {
					OBJECT_MAPPER.writeValue(jsonWriter, message.getRawMessage());
					jsonWriter.append("\n");
					onSuccess.accept(message);
				} catch (IOException e) {
					throw new RuntimeException("error while creating the temp file to upload", e);
				}

			});
			jsonWriter.flush();
		}
	}

}
