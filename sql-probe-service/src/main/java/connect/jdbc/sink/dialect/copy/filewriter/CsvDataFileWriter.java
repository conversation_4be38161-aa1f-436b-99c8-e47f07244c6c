package connect.jdbc.sink.dialect.copy.filewriter;

import com.fasterxml.jackson.databind.ObjectWriter;
import com.fasterxml.jackson.databind.SequenceWriter;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.data.Field;
import connect.data.Schema;
import connect.jdbc.sink.dialect.DbDialect;
import io.vavr.control.Either;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;

import java.io.OutputStream;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import static connect.jdbc.sink.PreparedStatementBinder.convertValueToString;
import static io.vavr.control.Either.left;
import static io.vavr.control.Either.right;

public class CsvDataFileWriter extends DataFileWriter {
	private static final CsvMapper OBJECT_MAPPER = new CsvMapper();

	private final Schema schema;
	private final DbDialect dbDialect;
	private final NexlaLogger logger;

	private final List<String> extraSchemaFields;

	private final Character delimiter;

	private final Character quoteChar;

	public CsvDataFileWriter(WarehouseCopyFileFormat fileFormat,
							 Schema schema,
							 DbDialect dbDialect,
							 NexlaLogger logger,
							 List<String> extraSchemaFields,
							 JdbcSinkConnectorConfig sinkConfig) {
		super(fileFormat);
		this.schema = schema;
		this.dbDialect = dbDialect;
		this.logger = logger;
		this.extraSchemaFields = extraSchemaFields;

		boolean configurableDelimiterQuoteCharEnabled = Objects.equals(sinkConfig.authConfig.dbType, ConnectionType.ORACLE_AUTONOMOUS);
		this.delimiter = configurableDelimiterQuoteCharEnabled && StringUtils.isNotBlank(sinkConfig.intermediateFileDelimiter)
						? sinkConfig.intermediateFileDelimiter.charAt(0)
						: fileFormat.delimiter.charAt(0);
		this.quoteChar = configurableDelimiterQuoteCharEnabled && StringUtils.isNotBlank(sinkConfig.intermediateFileQuoteChar)
						? sinkConfig.intermediateFileQuoteChar.charAt(0)
						: null;

	}

	@SneakyThrows
	protected void writeOutputStream(OutputStream outputStream,
	                                 StreamEx<NexlaMessage> records,
	                                 BiConsumer<Exception, NexlaMessage> onError,
									 Consumer<NexlaMessage> onSuccess) {
		ObjectWriter writer = OBJECT_MAPPER.writer(getCsvSchema(schema));
		try (SequenceWriter sequenceWriter = writer.writeValues(outputStream)) {
			records
				.forEach(data -> formatData(schema, data.getRawMessage())
					.fold(
						exc -> {
							onError.accept(exc, data);
							return null;
						},
						value -> {
							try {
								SequenceWriter write = sequenceWriter.write(value);
								onSuccess.accept(data);
								return write;
							} catch (Exception e) {
								throw new RuntimeException(e);
							}
						}));
		}
	}

	private CsvSchema getCsvSchema(Schema schema) {
		CsvSchema.Builder builder = getCsvBuilder();

		schema.fields().stream().map(Field::name).forEach(builder::addColumn);

		extraSchemaFields.forEach(builder::addColumn);

		return builder.build();
	}

	private CsvSchema.Builder getCsvBuilder() {
		var builder = CsvSchema.builder()
				.setUseHeader(false)
				.setColumnSeparator(delimiter)
				.disableEscapeChar();

		if (Objects.nonNull(quoteChar)) {
			builder.setQuoteChar(quoteChar);
		} else {
			builder.disableQuoteChar();
		}

		return builder;
	}

	private Either<Exception, LinkedHashMap<String, Object>> formatData(Schema schema, LinkedHashMap<String, Object> data) {
		try {
			LinkedHashMap<String, Object> formattedData = new LinkedHashMap<>();
			for (Field field : schema.fields()) {
				Object value = data.get(field.name());
				formattedData.put(field.name(), escape(convertValueToString(value, field, logger, dbDialect)));
			}

			extraSchemaFields
					.stream()
					.filter(data::containsKey)
					.forEach(key -> {
						var value = String.valueOf(data.get(key));
						formattedData.put(key, escape(value));
					});

			return right(formattedData);
		} catch (Exception e) {
			return left(e);
		}
	}

	private String escape(String s) {
		if (s == null) {
			return "";
		}
		String strDelimiter = String.valueOf(delimiter);
		return s
			.replace("\\", "\\\\")
			.replace("\n", "\\\n")
			.replace(strDelimiter, "\\" + strDelimiter);
	}


}
