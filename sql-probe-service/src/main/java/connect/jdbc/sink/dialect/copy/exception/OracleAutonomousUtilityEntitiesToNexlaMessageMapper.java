package connect.jdbc.sink.dialect.copy.exception;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexla.common.NexlaMessage;
import lombok.SneakyThrows;
import org.javatuples.Pair;

import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.Set;


/**
 * Makes a mapping between OracleAutonomous BadFile#Record and NexlaMessage[].
 * BadFile#Record differs from NexlaMessage, since DB can change message format after the insertion.
 * </p>
 * For example, 123.0 in NexlaMessage can be saved as 123 to Oracle and represented in BadFile table in this manner.
 * In production example which was the cause of the ticket, the most basic customer's record differed in 3 fields.
 */
public class OracleAutonomousUtilityEntitiesToNexlaMessageMapper {
	private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

	/**
	 * Creates mapped OracleAutonomous BadRecords to NexlaMessages.
	 * @return pair of OracleAutonomous bad file to NexlaMessage mapping.
	 * Returns Optional[empty] in case such mapping cannot be established.
	 * </p>
	 * Example input:
	 * [BadMessage{123;Mark;2}, BadMessage{125;John;3}, BadMessage{125;Mark;7}, BadMessage{125;Mark;3}],
	 * [NexlaMessage{id=123, name=Mark, goods=2}, NexlaMessage{id=125, name=John, goods=3}, NexlaMessage{id=125, name=Mark, goods=7}, NexlaMessage{id=125, name=Mark, goods=3}]
	 * Example output:
	 * [[BadMessage{123;Mark;2},NexlaMessage{id=123, name=Mark, goods=2}], [BadMessage{125;John;3}, NexlaMessage{id=125, name=John, goods=3}], [
	 * BadMessage{125;Mark;7}, NexlaMessage{id=125, name=Mark, goods=7}], [BadMessage{125;Mark;3}, EMPTY]]
	 */
	public List<Pair<String, Optional<NexlaMessage>>> createBadBodyToNexlaMessageMapping(List<NexlaMessage> records, List<String> badBodies) {
		final LinkedList<Pair<String, Optional<NexlaMessage>>> badBodyToNexlaMessageList = new LinkedList<>();
		final LinkedHashMap<String, NexlaMessage> indexNexlaMessages = this.uniqueIndexNexlaMessage(records);
		for (String badBody : badBodies) {
			Pair<String, Optional<NexlaMessage>> badBodyToNexlaMessage = new Pair<>(badBody, Optional.empty());
			badBodyToNexlaMessageList.addLast(badBodyToNexlaMessage);
			for (String badBodyPart : badBody.split(";")) {
				if (indexNexlaMessages.containsKey(badBodyPart)) {
					badBodyToNexlaMessage = badBodyToNexlaMessage.setAt1(Optional.of(indexNexlaMessages.get(badBodyPart)));
					badBodyToNexlaMessageList.removeLast();
					badBodyToNexlaMessageList.addLast(badBodyToNexlaMessage);
					break;
				}
			}
		}
		return badBodyToNexlaMessageList;
	}

	/**
	 * Index each NexlaMessage record for by each of it's fields (if key is present more than once, it won't be
	 * in the resulting collection).
	 * @param records NexlaMessages
	 * @return an index map where keys are present only once (since otherwise we can't establish 1-to-1 mapping between
	 * BadFile record and NexlaMessage.
	 */
	private LinkedHashMap<String, NexlaMessage> uniqueIndexNexlaMessage(List<NexlaMessage> records) {
		Set<String> duplicatedKeys = new HashSet<>();
		LinkedHashMap<String, NexlaMessage> indexedNexlaMessages = new LinkedHashMap<>();
		for (NexlaMessage record : records) {
			LinkedHashMap<String, Object> rawMessages = record.getRawMessage();
			for (Object value : rawMessages.values()) {
				String valueAsString = value.toString();
				if (duplicatedKeys.contains(valueAsString)) {
					// if we have more than 1 unique key, we have to delete it from the lookup index.
					indexedNexlaMessages.remove(valueAsString);
				} else {
					duplicatedKeys.add(valueAsString);
					indexedNexlaMessages.put(valueAsString, record);
				}
			}
		}
		return indexedNexlaMessages;
	}

	@SneakyThrows
	public String mapToSingleError(Optional<OracleAutonomousErrorMessage> oracleAutonomousErrorMessage,
									String badTable, String logTable) {
		LinkedHashMap<String, String> errorMap = new LinkedHashMap<>();
		if (oracleAutonomousErrorMessage.isPresent()) {
			errorMap.put("error", oracleAutonomousErrorMessage.get().getError());
			errorMap.put("bad table name", badTable);
			errorMap.put("log table name", logTable);
		} else {
			errorMap.put("error", "Unable to retrieve error, please check following tables for the issue details");
			errorMap.put("bad table name", badTable);
			errorMap.put("log table name", logTable);
		}
		return OBJECT_MAPPER.writeValueAsString(errorMap);
	}
}
