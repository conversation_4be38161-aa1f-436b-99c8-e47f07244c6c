package connect.jdbc.sink.dialect.type;

public enum TeradataType {

    BYTEINT("BYTEINT"),
    SMALLINT("SMALLINT"),
    INTEGER("INTEGER"),
    BIGINT("BIGINT"),
    FLOAT("FLOAT"),
    DOUBLE_PRECISION("DOUBLE PRECISION"),
    VARCHAR("VARCHAR"),
    VARBYTE("VARBYTE(64000)"),
    DATE("DATE"),
    TIME("TIME"),
    TIMESTAMP("TIMESTAMP");

    public final String type;

    TeradataType(String type) {
        this.type = type;
    }

}
