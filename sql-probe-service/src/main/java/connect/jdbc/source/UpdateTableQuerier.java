package connect.jdbc.source;

import com.nexla.admin.client.NexlaSchema;
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig;
import connect.data.ConnectSchema;
import connect.jdbc.sink.dialect.DbDialect;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.nexla.common.FileUtils.closeSilently;
import static java.lang.String.format;

public class UpdateTableQuerier implements TableQuerier {
	private static final Logger logger = LoggerFactory.getLogger(UpdateTableQuerier.class);
	public static final String SELECT_ORDER_FORMAT = "SELECT * FROM (%s) parts WHERE %s BETWEEN ? AND ? ORDER BY %s ASC";

	protected final String query;
	protected final Integer queryProbeTimeout;
	protected final Boolean sample;
	protected DbDialect db;
	protected PreparedStatement stmt;
	protected Integer result;
	private JdbcSourceConnectorConfig config;

	public UpdateTableQuerier(
		DbDialect dbDialect,
		JdbcSourceConnectorConfig config,
		Boolean sample,
		String query,
		Integer queryProveTimeout
	) {
		this.sample = sample;
		this.query = query;
		this.queryProbeTimeout = queryProveTimeout;
		this.db = dbDialect;
		this.config = config;
	}

	public Integer getResult() {
		return result;
	}

	@SneakyThrows
	public void initStatementCreator(Connection conn, StatementCreator stmtCreator) {
		if (config != null) {
			if (config.isIncrementingMode()) {
				String column = db.q(config.incrementingColumnName);
				String sql = format(SELECT_ORDER_FORMAT, query, column, db.q(config.incrementingColumnName));
				stmtCreator.prepareStatement(sql, conn);
				// those config values are set in SqlConnectorService.readSample
				stmtCreator.setLong(1, config.internalIncrementingColumnFrom);
				stmtCreator.setLong(2, config.internalIncrementingColumnTo);
			} else if (config.isTimestampAndIncrementingMode()) {
				String ts = db.q(config.timestampColumnName);
				String inc = db.q(config.incrementingColumnName);
				String sql = "SELECT * FROM (" + query + ") parts " +
					"WHERE {ts} <= ? and ( ( {ts} = ? and {inc} > ? ) OR {ts} > ? )" +
					" ORDER BY " + db.q(config.timestampColumnName) + " ASC, " + db.q(config.incrementingColumnName) + " ASC";
				sql = sql
					.replace("{ts}", ts)
					.replace("{inc}", inc);

				stmtCreator.prepareStatement(sql, conn);
				stmtCreator.setTimestamp(1, new Timestamp(config.internalTimestampColumnTo));
				stmtCreator.setTimestamp(2, new Timestamp(config.internalTimestampColumnFrom));
				stmtCreator.setLong(3, config.internalIncrementingColumnFrom);
				stmtCreator.setTimestamp(4, new Timestamp(config.internalTimestampColumnFrom));
			} else if (config.isTimestampMode()) {
				String column = db.q(config.timestampColumnName);
				String sql = format(SELECT_ORDER_FORMAT, query, column, db.q(config.timestampColumnName));
				stmtCreator.prepareStatement(sql, conn);
				stmtCreator.setTimestamp(1, new Timestamp(config.internalTimestampColumnFrom));
				stmtCreator.setTimestamp(2, new Timestamp(config.internalTimestampColumnTo));
			} else {
				stmtCreator.prepareStatement(query, conn);
			}
		} else {
			stmtCreator.prepareStatement(query, conn);
		}
	}

	@Override
	public String toString() {
		return "UpdateTableQuerier{" +
			", query='" + query + '\'' +
			'}';
	}

	public PreparedStatement getOrCreatePreparedStatement(Connection db) {
		if (stmt != null) {
			return stmt;
		}
		StatementCreator stmtCreator = new StatementCreator();
		initStatementCreator(db, stmtCreator);
		PreparedStatement statement = stmtCreator.getStatement();
		if (sample) {
			int timeoutSeconds = (int)TimeUnit.MILLISECONDS.toSeconds(queryProbeTimeout);
			if (timeoutSeconds > 0) {
				try {
					statement.setQueryTimeout(timeoutSeconds);
				} catch (SQLException e) {
					logger.warn("Failed to set query timeout to {} seconds", timeoutSeconds, e);
				}
			}
		}
		return statement;
	}

	public void maybeStartQuery(Connection db) throws SQLException {
		if (result == null) {
			stmt = getOrCreatePreparedStatement(db);
			//todo is this a bug? executeUpdate can throw exception and does not return data from select statement, which this looks to intend to support
			result = stmt.executeUpdate();
		}
	}

	@Override
	public void close() {
		closeSilently(stmt);
	}

	@Override
	public Set<String> getFieldNames() {
		return Set.of("result");
	}

	@Override
	public Optional<NexlaSchema> getNexlaSchema() {
		//todo we don't get a ResultSet back as we call executeUpdate. We can build the schema if/when we change to executeQuery
		return Optional.empty();
	}

	@Override
	public Optional<ConnectSchema> getSchema() {
		return Optional.empty();
	}
}
