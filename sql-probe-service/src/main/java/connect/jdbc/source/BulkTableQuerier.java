package connect.jdbc.source;

import com.nexla.admin.client.NexlaSchema;
import com.nexla.common.ConnectionType;
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig;
import connect.data.ConnectSchema;
import connect.data.Field;
import connect.data.Struct;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.MySqlDialect;
import connect.jdbc.util.JdbcUtils;
import edu.emory.mathcs.backport.java.util.Collections;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.LinkedHashMap;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.nexla.common.FileUtils.closeSilently;
import static connect.jdbc.util.CdcMode.MULTI_TABLE;
import static java.lang.String.format;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toSet;

public class BulkTableQuerier implements TableQuerier {

	private static final Logger LOGGER = LoggerFactory.getLogger(BulkTableQuerier.class);
	public static final String SELECT_ORDER_FORMAT = "SELECT * FROM (%s) parts WHERE %s BETWEEN {dp} AND {dp} ORDER BY %s ASC";
	public static final String SELECT_ORACLE_AUTONOMOUS_ORDER_FORMAT = "SELECT * FROM (%s) parts WHERE %s BETWEEN '%s' AND '%s' ORDER BY %s ASC";
	public static final String ORACLE_AUTONOMOUS_EXPORT_DATE_FORMAT = "dd-MM-yyyy hh:mm:ss.mmm a";
	public static final String SELECT_ORDER_FORMAT_GT = "SELECT * FROM (%s) parts WHERE %s >= {dp} ORDER BY %s ASC";
	public static final int DEFAULT_MAX_SAMPLE_ROWS = 1000;
	private static final SimpleDateFormat ORACLE_AUTONOMOUS_DATE_FORMAT = new SimpleDateFormat(ORACLE_AUTONOMOUS_EXPORT_DATE_FORMAT);

	private final int maxSampleRows;
	private final QueryMode mode;
	private final String tableName;
	private final String query;
	private final Optional<Integer> queryProbeTimeout;
	private final String topicPrefix;
	private final boolean mapNumerics;
	private final DbDialect db;
	private PreparedStatement stmt;
	private ResultSet resultSet;
	// todo can we have 1 schema? What's ConnectSchema for? It looks to be used for when we write to database sink
	private ConnectSchema schema;
	// For ingestion from source as we map the SQL schema to our internal schema and schema types
	private NexlaSchema nexlaSchema;

	private JdbcSourceConnectorConfig config;
	private final boolean sample;

	public BulkTableQuerier(
		DbDialect dbDialect,
		JdbcSourceConnectorConfig config,
		boolean sample,
		int maxSampleRows,
		QueryMode mode,
		String schemaName,
		Optional<String> table,
		Optional<String> query,
		Optional<Integer> queryProbeTimeout
	) {
		String nameOrQuery = table.orElseGet(query::get);
		this.db = dbDialect;
		this.mode = mode;
		this.tableName = mode.equals(QueryMode.TABLE) ? dbDialect.getQualifiedTableName(nameOrQuery, schemaName, JdbcUtils.getDatabaseName(config)) : null;
		this.query = mode.equals(QueryMode.QUERY) ? nameOrQuery : null;
		this.queryProbeTimeout = queryProbeTimeout;
		this.topicPrefix = "*";
		this.mapNumerics = dbDialect.mapNumerics;
		this.config = config;
		this.sample = sample;
		this.maxSampleRows = maxSampleRows;
	}

	public BulkTableQuerier(
		DbDialect dbDialect, boolean sample, QueryMode mode,
		String schemaName, String table, String database
	) {
		this.db = dbDialect;
		this.mode = mode;
		this.tableName = mode.equals(QueryMode.TABLE) ? dbDialect.getQualifiedTableName(table, schemaName, database) : null;
		this.query = mode.equals(QueryMode.QUERY) ? table : null;
		this.queryProbeTimeout = Optional.empty();
		this.topicPrefix = "*";
		this.mapNumerics = dbDialect.mapNumerics;
		this.sample = sample;
		this.maxSampleRows = DEFAULT_MAX_SAMPLE_ROWS;
	}

	@SneakyThrows
	public void initStatementCreator(Connection conn, StatementCreator stmtCreator) {
		String baseSql;

		switch (mode) {
			case TABLE:
				baseSql = "SELECT * FROM " + tableName;
				break;
			case QUERY:
				baseSql = query;
				break;
			default:
				throw new IllegalArgumentException("Unknown mode: " + mode);
		}

		// in not sample mode config is always not null
		// in sample mode only IncrementingMode processing is enabled
		// that's why we check if config != null

		if (config != null) {
			String dateParameter = getDateParameterForQuery();
			if (config.isIncrementingMode()) {
				String column = db.q(config.incrementingColumnName);
				String sql = format(SELECT_ORDER_FORMAT, baseSql, column, db.q(config.incrementingColumnName))
						.replace("{dp}", "?");
				stmtCreator.prepareStatement(sql, conn);
				// those config values are set in SqlConnectorService.readSample
				stmtCreator.setLong(1, config.internalIncrementingColumnFrom);
				stmtCreator.setLong(2, config.internalIncrementingColumnTo);
			} else if (config.isTimestampAndIncrementingMode()) {
				if (sample) {
					if (config.incrementingLoadFromExclusive.isPresent() && config.timestampLoadFrom.isPresent()) {
						String ts = db.q(config.timestampColumnName);
						String inc = db.q(config.incrementingColumnName);
						String sql = "SELECT * FROM (" + baseSql + ") parts " +
									 "WHERE (( {ts} = {dp} and {inc} >= ? ) OR {ts} > {dp} )" +
									 " ORDER BY " + db.q(config.timestampColumnName) + " ASC, " + db.q(config.incrementingColumnName) + " ASC";
						sql = sql
							.replace("{ts}", ts)
							.replace("{inc}", inc)
							.replace("{dp}", dateParameter);

						stmtCreator.prepareStatement(sql, conn);
						setStatementTimestamp(1, config.timestampLoadFrom.get(), stmtCreator);
						stmtCreator.setLong(2, config.incrementingLoadFromExclusive.get());
						setStatementTimestamp(3, config.timestampLoadFrom.get(), stmtCreator);
					} else {
						stmtCreator.prepareStatement(baseSql, conn);
					}
				} else {
					String ts = db.q(config.timestampColumnName);
					String inc = db.q(config.incrementingColumnName);
					String sql = "SELECT * FROM (" + baseSql + ") parts " +
								 "WHERE {ts} <= {dp} and ( {ts} >= {dp} and {inc} > ? )" +
								 " ORDER BY " + db.q(config.timestampColumnName) + " ASC, " + db.q(config.incrementingColumnName) + " ASC";
					sql = sql
						.replace("{ts}", ts)
						.replace("{inc}", inc)
						.replace("{dp}", dateParameter);

					stmtCreator.prepareStatement(sql, conn);
					setStatementTimestamp(1, config.internalTimestampColumnTo, stmtCreator);
					setStatementTimestamp(2, config.internalTimestampColumnFrom, stmtCreator);
					stmtCreator.setLong(3, config.internalIncrementingColumnFrom);
				}
			} else if (config.isTimestampMode()) {
				if (sample) {
					if (config.timestampLoadFrom.isPresent()) {
						String column = db.q(config.timestampColumnName);
						String sql = format(SELECT_ORDER_FORMAT_GT, baseSql, column, db.q(config.timestampColumnName));
						sql = sql.replace("{dp}", dateParameter);

						stmtCreator.prepareStatement(sql, conn);
						setStatementTimestamp(1, config.timestampLoadFrom.get(), stmtCreator);
					} else {
						stmtCreator.prepareStatement(baseSql, conn);
					}
				} else {
					String column = db.q(config.timestampColumnName);
					String sql = Objects.equals(config.authConfig.dbType, ConnectionType.ORACLE_AUTONOMOUS)
							? getOracleAutonomousQuery(baseSql)
							: format(SELECT_ORDER_FORMAT, baseSql, column, db.q(config.timestampColumnName));

					sql = sql.replace("{dp}", dateParameter);

					stmtCreator.prepareStatement(sql, conn);
					if (!Objects.equals(config.authConfig.dbType, ConnectionType.ORACLE_AUTONOMOUS)) {
						setStatementTimestamp(1, config.internalTimestampColumnFrom, stmtCreator);
						setStatementTimestamp(2, config.internalTimestampColumnTo, stmtCreator);
					}
				}
			} else {
				stmtCreator.prepareStatement(baseSql, conn);
			}
		} else {
			stmtCreator.prepareStatement(baseSql, conn);
		}
		if (sample) {
			stmtCreator.setMaxRows(maxSampleRows);
		}
	}

	private String getOracleAutonomousQuery(String baseSql) {
		String column = db.q(config.timestampColumnName);
		final String from = ORACLE_AUTONOMOUS_DATE_FORMAT.format(new Date(config.internalTimestampColumnFrom));
		final String to = ORACLE_AUTONOMOUS_DATE_FORMAT.format(new Date(config.internalTimestampColumnTo));
		return format(SELECT_ORACLE_AUTONOMOUS_ORDER_FORMAT, baseSql, column, from, to, column);
	}

	@SneakyThrows
	private void setStatementTimestamp(int position, Long timestamp, StatementCreator stmtCreator) {
		if (Objects.equals(ConnectionType.NETSUITE_JDBC, config.authConfig.dbType)) {
			if (config.netsuiteTzUtc) {
				DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss XXX").withZone(ZoneOffset.UTC);
				var format = dateFormat.format(Instant.ofEpochMilli(timestamp));
				stmtCreator.setString(position, format);
			} else {
				var dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				var format = dateFormat.format(new Date(timestamp));
				stmtCreator.setString(position, format);
			}
		} else {
			stmtCreator.setTimestamp(position, new Timestamp(timestamp));
		}
	}

	private String getDateParameterForQuery() {
		if (Objects.equals(ConnectionType.NETSUITE_JDBC, config.authConfig.dbType)) {
			if (config.netsuiteTzUtc) {
				return "TO_TIMESTAMP_TZ(?, 'YYYY-MM-DD HH24:MI:SS TZH:TZM')";
			} else {
				return "TO_TIMESTAMP(?, 'YYYY-MM-DD HH24:MI:SS')";
			}
		}

		return "?";
	}

	protected ResultSet executeQuery() throws SQLException {
		return stmt.executeQuery();
	}

	public LinkedHashMap<String, Object> extractNexlaRecord() {
		return recordToMap(DataConverter.convertRecord(db, schema, resultSet, mapNumerics));
	}

	public String getRangeInfo() {
		if (config.isTimestampAndIncrementingMode()) {
			return String.format("timestamp incrementing mode range, from timestamp=%d and increment=%d to timestamp=%d",
				config.internalTimestampColumnFrom, config.internalIncrementingColumnFrom, config.internalTimestampColumnTo);
		} else if (config.isIncrementingMode()) {
			return String.format("incrementing mode range, from=%d to=%d",
				config.internalIncrementingColumnFrom, config.internalIncrementingColumnTo);
		} else if (config.isTimestampMode()) {
			return String.format("timestamp mode range, from timestamp=%d to timestamp=%d",
				config.internalTimestampColumnFrom, config.internalTimestampColumnTo);
		} else {
			return "none mode";
		}
	}

	private LinkedHashMap<String, Object> recordToMap(Struct record) {
		LinkedHashMap<String, Object> data = new LinkedHashMap<>();
		for (Field field : record.schema().fields()) {
			data.put(field.name(), record.getValues()[field.index()]);
		}
		return data;
	}

	@Override
	public String toString() {
		return "BulkTableQuerier{" +
			   "tableName='" + tableName + '\'' +
			   ", query='" + query + '\'' +
			   ", topicPrefix='" + topicPrefix + '\'' +
			   '}';
	}

	@Override
	public Optional<ConnectSchema> getSchema() {
		return Optional.ofNullable(schema);
	}

	@SneakyThrows
	public PreparedStatement getOrCreatePreparedStatement(Connection db) {
		enableSpannerDataBoostIfNecessary(db);

		if (stmt != null) {
			return stmt;
		}

		StatementCreator stmtCreator = new StatementCreator();
		initStatementCreator(db, stmtCreator);
		PreparedStatement statement = stmtCreator.getStatement();

		if (config != null) {
			try {
				if (this.db instanceof MySqlDialect) {
					// NEX-13236 - It is required in order to prevent memory issues. Integer.MIN_VALUE is a signal for mysql
					// jdbc driver to stream result sets row-by-row and it ensures that the memory will not be overloaded.
					statement.setFetchSize(Integer.MIN_VALUE);
					LOGGER.info("Setting fetch size to Integer.MIN_VALUE for mysql database.");
				} else {
					statement.setFetchSize(config.batchSizeApprox);
					LOGGER.info("Setting fetch size to 'batch.size.approx' for non mysql database. config.batchSizeApprox: {}.", config.batchSizeApprox);
				}
			} catch (Exception e) {
				LOGGER.error(String.format("Error during fetch size setting. config.batchSizeApprox: %d", config.batchSizeApprox), e);
			}
		}

		if (sample && queryProbeTimeout.isPresent()) {
			int timeoutSeconds = (int) TimeUnit.MILLISECONDS.toSeconds(queryProbeTimeout.get());
			if (timeoutSeconds > 0) {
				try {
					statement.setQueryTimeout(timeoutSeconds);
				} catch (SQLException e) {
					LOGGER.warn("Failed to set query timeout to {} seconds", timeoutSeconds, e);
				}
			}
		}

		return statement;
	}

	private void enableSpannerDataBoostIfNecessary(Connection db) throws SQLException {
		if (nonNull(config) && nonNull(config.authConfig)) {
			var spannerAuth = config.authConfig.spannerAuth;

			if (spannerAuth.isPresent() && config.enableSpannerDataBoost) {
				try (Statement statement = db.createStatement()) {
					// Partition query is not supported for read/write transaction
					db.setReadOnly(true);
					statement.execute("set auto_partition_mode=true");
					statement.execute("set data_boost_enabled=true");

					LOGGER.info("M=enableSpannerDataBoostIfNecessary, credentialId={}, I=dataBoost enabled for spanner connector",
							config.authConfig.getCredsId());
				}
			}
		}
	}

	public void maybeStartQuery(Connection db) throws SQLException {
		if (resultSet == null) {
			stmt = getOrCreatePreparedStatement(db);
			resultSet = this.db.fixResultSet(executeQuery());
			schema = DataConverter.convertSchema(mode, tableName, this.db, resultSet.getMetaData(), mapNumerics);
			nexlaSchema = config != null && config.cdcEnabled && config.listingEnabled
					&& MULTI_TABLE.getMode().equals(config.cdcMode.toLowerCase()) ?
					DataConverter.toNexlaSchema(schema) : DataConverter.toNexlaSchema(resultSet.getMetaData(), this.db);
		}
	}

	@Override
	public Set<String> getFieldNames() {
		return schema == null ? Collections.emptySet() : schema.fields().stream().map(Field::name).collect(toSet());
	}

	@Override
	public Optional<NexlaSchema> getNexlaSchema() {
		return Optional.ofNullable(nexlaSchema);
	}

	@SneakyThrows
	public boolean next() {
		return resultSet.next();
	}

	@Override
	public void close() {
		closeSilently(resultSet, stmt);
	}

	public enum QueryMode {
		TABLE, // Copying whole tables, with queries constructed automatically
		QUERY // User-specified query
	}
}
