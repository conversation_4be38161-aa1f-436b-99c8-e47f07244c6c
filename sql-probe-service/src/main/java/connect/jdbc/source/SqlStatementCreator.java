package connect.jdbc.source;

import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;

import java.sql.Connection;
import java.sql.Timestamp;
import java.util.Map;

@NoArgsConstructor
@Getter
public class SqlStatementCreator extends StatementCreator {

	private String sql;
	private Map<Integer, String> parameters = Maps.newTreeMap();

	@SneakyThrows
	public void prepareStatement(String sql, Connection connection) {
		super.prepareStatement(sql, connection);
		this.sql = sql;
	}

	@SneakyThrows
	public void setLong(int parameterIndex, long x) {
		super.setLong(parameterIndex, x);
		parameters.put(parameterIndex, Long.valueOf(x).toString());
	}

	@SneakyThrows
	public void setTimestamp(int parameterIndex, Timestamp x) {
		super.setTimestamp(parameterIndex, x);
		parameters.put(parameterIndex, "'" + x.toString() + "'");
	}

	public String getStatementSql() {
		return StreamEx
				.of(parameters.values())
				.foldLeft(sql, (acc, par) -> acc.replaceFirst("[?]", par));
	}

	public void setMaxRows(int max) {
	}

}
