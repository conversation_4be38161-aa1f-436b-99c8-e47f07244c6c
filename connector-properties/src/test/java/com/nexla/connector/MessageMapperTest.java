package com.nexla.connector;

import com.nexla.common.NexlaMessage;
import com.nexla.connector.config.SinkConnectorConfig;
import org.junit.Ignore;
import org.junit.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.StreamUtils.lhm;
import static com.nexla.connector.config.SinkConnectorConfig.*;
import static com.nexla.connector.config.SinkConnectorConfig.FIRST_FLUSH_DELAY_MINUTES;
import static com.nexla.connector.properties.SqlConfigAccessor.CDC_ENABLED;
import static org.junit.Assert.assertEquals;

public class MessageMapperTest {
    @Test
    public void mapsNormalTypes() {
        String mapping = "{\"mapping\":{\"int\":{\"INT\":\"INT\"},\"name\":{\"NAME\":\"TEXT\"},\"float\":{\"FLOAT\":\"DOUBLE\"}},\"mode\":\"manual\",\"tracker_mode\":\"NONE\"}";
        Map<String, String> config = stubConfig(mapping);
        SinkConnectorConfig sinkConnectorConfig = new SinkConnectorConfig(config);
        MessageMapper messageMapper = new MessageMapper(sinkConnectorConfig.mappingConfig, sinkConnectorConfig, false, Optional.empty());
        NexlaMessage original = new NexlaMessage(lhm("int", 123 , "name", "{\"some\":[\"text123!\"]}", "float", 1.23));
        NexlaMessage expected = new NexlaMessage(lhm("INT", 123 , "NAME", "{\"some\":[\"text123!\"]}", "FLOAT", 1.23));
        assertEquals(expected, messageMapper.extractMessage(original).getMapped());
    }

    @Test
    public void mapsArrays() {
        String mapping = "{\"mapping\":{\"array\":{\"ARRAY\":\"ARRAY\"},\"id\":{\"ID\":\"INT\"}},\"mode\":\"manual\",\"tracker_mode\":\"NONE\"}";
        Map<String, String> config = stubConfig(mapping);
        SinkConnectorConfig sinkConnectorConfig = new SinkConnectorConfig(config);
        MessageMapper messageMapper = new MessageMapper(sinkConnectorConfig.mappingConfig, sinkConnectorConfig, false, Optional.empty());
        NexlaMessage original = new NexlaMessage(lhm("id", 1 , "array", Arrays.asList(lhm("merchant", "Wal-Mart.com", "price", 3.67))));
        NexlaMessage expected = new NexlaMessage(lhm("ID", 1 , "ARRAY", null));
        assertEquals(expected, messageMapper.extractMessage(original).getMapped());
    }

    @Test
    public void mapsObjects() {
        String mapping = "{\"mapping\":{\"object\":{\"OBJECT\":\"OBJECT\"},\"id\":{\"ID\":\"INT\"}},\"mode\":\"manual\",\"tracker_mode\":\"NONE\"}";
        Map<String, String> config = stubConfig(mapping);
        SinkConnectorConfig sinkConnectorConfig = new SinkConnectorConfig(config);
        MessageMapper messageMapper = new MessageMapper(sinkConnectorConfig.mappingConfig, sinkConnectorConfig, false, Optional.empty());
        NexlaMessage original = new NexlaMessage(lhm("id", 2 , "object", lhm("outer", lhm("inner", "innerVal"))));
        NexlaMessage expected = new NexlaMessage(lhm("ID", 2 , "OBJECT", null));
        assertEquals(expected, messageMapper.extractMessage(original).getMapped());
    }

    private Map<String, String> stubConfig(String mapping) {
        Map<String, String> cfg = new HashMap<>();
        cfg.put(MAPPING, mapping);
        cfg.put(SINK_ID, "123");
        cfg.put(DATASET_ID, "345");
        cfg.put(VERSION, "99");
        cfg.put(CREDS_ENC, "encoded");
        cfg.put(CREDS_ENC_IV, "encoded-IV");
        cfg.put(CREDS_ID, "9123");
        cfg.put(CDC_ENABLED, "false");
        cfg.put(TRACKER_ENCRYPTION_ENABLED, "false");
        cfg.put(TRACKER_ENCRYPTION_KEY, "NONE");
        cfg.put(NODE_TAG, "tagg");
        cfg.put(INACTIVITY_TIMEOUT_BEFORE_FLUSH_MIN, "1");
        cfg.put(FIRST_FLUSH_DELAY_MINUTES, "1");
        cfg.put(FLUSH_COMBINE_RUN_IDS, "true");
        return cfg;
    }

    @Test
    public void autoMappingKeepsStructure() {
        String mapping = "{\"mode\":\"auto\",\"tracker_mode\":\"NONE\"}";
        Map<String, String> config = stubConfig(mapping);
        SinkConnectorConfig sinkConnectorConfig = new SinkConnectorConfig(config);
        MessageMapper messageMapper = new MessageMapper(sinkConnectorConfig.mappingConfig, sinkConnectorConfig, false, Optional.empty());
        NexlaMessage original = new NexlaMessage(lhm("top", lhm("nestedObject", lhm("nestedArray", Arrays.asList(123,"value"))), "2ndLevel", 123));
        NexlaMessage expected = new NexlaMessage(lhm("top", lhm("nestedObject", lhm("nestedArray", Arrays.asList(123,"value"))), "2ndLevel", 123));
        assertEquals(expected, messageMapper.extractMessage(original).getMapped());
    }

    @Test
    @Ignore // might be useful when the nested object feature is turned on
    public void usesDirectPropertyOverNestedForConflicts() {
        // FE-created mapping of the nested property to NESTEDCOL, direct to DIRECTCOL
        String mapping = "{\"mapping\":{\"id\":{\"ID\":\"LONG\"},\"object\":{\"OBJECT\":\"OBJECT\"},\"object.l1\":{\"DIRECTCOL\":\"TEXT\",\"NESTEDCOL\":\"TEXT\"}},\"mode\":\"manual\",\"tracker_mode\":\"NONE\"}";
        Map<String, String> config = stubConfig(mapping);
        SinkConnectorConfig sinkConnectorConfig = new SinkConnectorConfig(config);
        MessageMapper messageMapper = new MessageMapper(sinkConnectorConfig.mappingConfig, sinkConnectorConfig, false, Optional.empty());
        NexlaMessage original = new NexlaMessage(lhm("id", 2, "object.l1", "direct", "object", lhm("l1", "nestval")));
        NexlaMessage expected = new NexlaMessage(lhm("ID", 2, "OBJECT", lhm("l1", "nestval"), "DIRECTCOL", "direct", "NESTEDCOL", "direct"));
        assertEquals(expected, messageMapper.extractMessage(original).getMapped());
    }
}