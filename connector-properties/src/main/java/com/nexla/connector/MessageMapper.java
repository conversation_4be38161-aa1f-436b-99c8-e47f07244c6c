package com.nexla.connector;

import com.google.common.collect.Lists;
import com.nexla.common.EncryptionUtils;
import com.nexla.common.NexlaMessage;
import com.nexla.common.tracker.SinkItem;
import com.nexla.common.tracker.Tracker;
import com.nexla.common.transform.Flattener;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.SinkConnectorConfig;
import lombok.Data;
import one.util.streamex.EntryStream;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.connect.errors.ConnectException;

import java.util.*;
import java.util.function.Function;

import static com.google.common.collect.Sets.newHashSet;
import static com.nexla.common.StreamUtils.toLinkedHashMap;
import static com.nexla.common.tracker.Tracker.TrackerMode.NONE;
import static com.nexla.connector.config.MappingConfig.MODE_AUTO;
import static com.nexla.connector.config.MappingConfig.MODE_MANUAL;
import static java.util.function.Function.identity;
import static org.apache.commons.net.util.Base64.encodeBase64URLSafeString;

@Data
public class MessageMapper {

	private final Optional<MappingConfig> mappingConfig;
	private final SinkConnectorConfig config;
	private final boolean forceFlattening;
	private final KeyTransform keyTransform;
	private final Optional<EncryptionUtils> encryptionUtils;
	private final Optional<Comparator<Map.Entry<String, ?>>> fieldsOrderComparator;
	private final FilterMessageTraverse filterMessageTraverse;

	public MessageMapper(
		Optional<MappingConfig> mappingConfig,
		SinkConnectorConfig config,
		boolean forceFlattening,
		Optional<EncryptionUtils> encryptionUtils
	) {
		this(mappingConfig, config, forceFlattening, KeyTransform.NONE, encryptionUtils);
	}

	public MessageMapper(
		Optional<MappingConfig> mappingConfig,
		SinkConnectorConfig config,
		boolean forceFlattening,
		KeyTransform keyTransform,
		Optional<EncryptionUtils> encryptionUtils
	) {
		this.mappingConfig = mappingConfig;
		this.config = config;
		this.forceFlattening = forceFlattening;
		this.keyTransform = keyTransform;
		this.encryptionUtils = encryptionUtils;
		this.fieldsOrderComparator = mappingConfig.map(mc -> {
			List<String> fieldsOrder = Lists.newArrayList();
			fieldsOrder.addAll(mc.getFieldsOrder());
			mc.getMapping().forEach((k, v) -> {
				if (!fieldsOrder.contains(k)) {
					fieldsOrder.add(k);
				}
			});

			return (o1, o2) -> {
				int fstInd = fieldsOrder.indexOf(o1.getKey());
				int sndInd = fieldsOrder.indexOf(o2.getKey());
				// return ordered values as ordered
				if (fstInd != -1 && sndInd != -1) {
					return fstInd - sndInd;
				}
				// return ordered values before automatic ones
				int diff = sndInd - fstInd;
				return diff == 0 ? 1 : diff;
			};
		});
		this.filterMessageTraverse = new FilterMessageTraverse((path, o) ->
			config
				.mappingConfig
				.map(c -> !c.getExcludes().contains(path))
				.orElse(true));
	}

	public ExtractedMessage extractMessage(NexlaMessage originalMessage) {
		NexlaMessage mappedMessage = new NexlaMessage();
		mappedMessage.setRawMessage(originalMessage.getRawMessage());
		mappedMessage.setNexlaMetaData(originalMessage.getNexlaMetaData());
		LinkedHashMap<String, Object> rawMessage = originalMessage.getRawMessage();

		config.mappingConfig.ifPresent(conf -> {
			if (StringUtils.isNotEmpty(conf.getMode())) {
				LinkedHashMap<String, Object> transformedMap = applyMapping(rawMessage);
				mappedMessage.setRawMessage(transformedMap);
			}

			if (conf.getTrackerMode() != NONE &&
				mappedMessage.getNexlaMetaData() != null &&
				mappedMessage.getNexlaMetaData().getTrackerId() != null) {

				Tracker trackerId = mappedMessage.getNexlaMetaData().getTrackerId();
				long offsetFromIngest = System.currentTimeMillis() - trackerId.getSource().getInitialIngestTimestamp();
				trackerId.setSink(new SinkItem(config.sinkId, config.version, offsetFromIngest));

				String trackerString = trackerId.toString(conf.getTrackerMode());
				String trackerEncoded = encryptionUtils
					.map(enc -> enc.encrypt(trackerString))
					.orElseGet(() -> encodeBase64URLSafeString(trackerString.getBytes()));

				mappedMessage.getRawMessage().put(conf.getTrackerFieldName(), trackerEncoded);
			}
		});

		return new ExtractedMessage(originalMessage, mappedMessage);
	}

	private LinkedHashMap<String, Object> applyMapping(LinkedHashMap<String, Object> data) {
		if (!config.mappingConfig.isPresent()) {
			return data;
		}
		MappingConfig mappingConfig = this.config.mappingConfig.get();
		LinkedHashMap<String, Map<String, String>> mapping = mappingConfig.getMapping();
		switch (mappingConfig.getMode()) {

			case MODE_MANUAL: {
				LinkedHashMap<String, Object> flattenedData = Flattener.INSTANCE.flatten(data);

				return toLinkedHashMap(
					EntryStream
						.of(mapping)
						.sorted(fieldsOrderComparator.get())
						.mapKeyValue((k, v) -> mapValuesWithData(flattenedData, k, v.keySet()))
						.flatMapToEntry(e -> e));
			}

			case MODE_AUTO: {

				LinkedHashMap<String, Object> flattened = forceFlattening ?
					Flattener.INSTANCE.flatten(data) :
					data;

				LinkedHashMap<String, Object> excluded = mappingConfig.getExcludes().isEmpty() ?
					flattened :
					filterMessageTraverse.filter(flattened);

				return toLinkedHashMap(
					EntryStream
						.of(excluded)
						.sorted(fieldsOrderComparator.get())
						.mapKeyValue((k, v) -> mapStreamWithMapping(mapping, k, v))
						.flatMapToEntry(e -> e)
						.mapKeys(keyTransform::transform)
				);
			}

			default:
				throw new ConnectException("Illegal mapping mode: " + mappingConfig.getMode());
		}
	}

	private Map<String, Object> mapStreamWithMapping(LinkedHashMap<String, Map<String, String>> mapping, String k, Object v) {
		Map<String, String> newKeys = mapping.get(k);
		Set<String> keys = newKeys != null ? newKeys.keySet() : newHashSet(k);
		return toLinkedHashMap(keys.stream(), identity(), s -> v);
	}

	private LinkedHashMap<String, Object> mapValuesWithData(
		LinkedHashMap<String, Object> flattenedData,
		String key,
		Set<String> values
	) {
		return toLinkedHashMap(values.stream(), identity(), s -> flattenedData.get(key));
	}

	public enum KeyTransform {
		LOWER(String::toLowerCase), UPPER(String::toUpperCase), NONE(Function.identity());

		private final Function<String, String> fn;

		KeyTransform(Function<String, String> fn) {
			this.fn = fn;
		}

		public String transform(String v) {
			return fn.apply(v);
		}
	}

}