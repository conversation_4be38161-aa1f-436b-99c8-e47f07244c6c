package com.nexla.connector.properties;

import com.nexla.common.NexlaConstants;

public interface RestConfigAccessor {

	String PARTITION_KEY = "partition.key";

	String POLL_MS = "poll.ms";

	/**
	 * Response item ID field name
	 */
	String RESPONSE_ID_FIELD_NAME = "response.id.field.name";

	/**
	 * Number of rows required to consider given page as full.
	 * <br>Only full pages should be ingested to prevent gaps in data.
	 */
	String PAGE_EXPECTED_ROWS = "page.expected.rows";

	/**
	 * REST iteration stop condition. Supported modes:
	 * - default - REST iteration stops when the retrieved page size is less than the expected one (PAGE_EXPECTED_ROWS)
	 * - norecords - REST iteration stops when there are no records returned
	 */
	String ITERATION_STOP_CONDITION = "iteration.stop.condition";

	/**
	 * Specifies if 'page.expected.rows' property is number or string.
	 * <br>By Default is false.
	 */
	String PAGE_VALUE_IS_NUMBER = "page.value.is.number";

	/**
	 * Http method, one of GET, POST, PUT, DELETE and so on
	 */
	String METHOD = "method";

	/**
	 * URL template for sink connector, can contain variables like {var}
	 */
	String URL_TEMPLATE = "url.template";

	/**
	 * Whether to skip URL encoding for this REST iteration or not.
	 */
	String SKIP_URL_ENCODING = "skip.url.encoding";

	/**
	 * URL template for the initial request in case if initial request differs from later iteration calls.
	 * In the current implementation, the result records will be injected to the Source as well.
	 */
	String URL_TEMPLATE_INIT_REQUEST = "url.template.init.request";

	/**
	 * Body template for sink connector, can contain variables like {var}.
	 *
	 * @see #VAR_MESSAGE_JSON
	 */
	String BODY_TEMPLATE = "body.template";

	String RETRY_THRESHOLD_NUMBER = "retry.threshold";

	String RETRY_DELAY = "retry.delay";

	String REST_ITERATIONS_JSON = NexlaConstants.REST_ITERATIONS_JSON;
	/**
	 * {message.json} will be replaced with raw message map serialized to json and encoded
	 */
	String VAR_MESSAGE_JSON = "message.json";

	/**
	 * If present, will replace response body.
	 */
	String RESPONSE_DATA = "response.data";

	/**
	 * XPath or JsonPath to extract items from response
	 */
	String RESPONSE_DATA_PATH = "response.data.path";

	/**
	 * XPath or JsonPath to extract additional info from response
	 */
	String RESPONSE_DATA_PATH_ADDITIONAL = "response.data.path.additional";

	/**
	 * Additional JSON data to be appended to the response
	 */
	String RESPONSE_DATA_ADDITIONAL = "response.data.additional";

	/**
	 * Response format, xml or json
	 */
	String RESPONSE_FORMAT = "response.format";

	String DATASET_COLUMNS = "dataset.columns";

	/**
	 * API parameter indicating id to request data from (inclusive)
	 */
	String PARAM_ID = "param.id";

	/**
	 *  Marcos parameter to be used to define current page and be changed on the flight.
	 *  "url.template": "https://test-be.nexla.com/echo?page+{page_number_value}",
	 *  "param.id.macro": "page_number_value"
	 *  Supported for [paging.incrementing.offset, paging.incrementing, paging.next.token, response.id.number, response.id.string] modes.
	 */
	String PARAM_ID_MACRO = "param.id.macro";

	/**
	 *  Marcos parameter to be used to define current page size and be changed on the flight.
	 *  "url.template": "https://test-be.nexla.com/echo?page+{page_number_value}+per_page+{per_page_value}",
	 *  "page.expected.rows.macro":"per_page_value"
	 *  Supported for [paging.incrementing.offset, paging.incrementing]
	 */
	String PAGE_EXPECTED_ROWS_MACRO = "page.expected.rows.macro";

	/**
	 *  Marcos parameter to be used to define current offset and be changed on the flight.
	 *  "iteration.type": "paging.incrementing.offset",
	 *  "url.template": "https://test-be.nexla.com/echo?page+{page_number_value}+offset+50",
	 *  "param.offset.macro": "page_number_value"
	 *  Supported only for paging.incrementing.offset mode.
	 */
	String PARAM_OFFSET_MACRO = "param.offset.macro";

	String PARAM_OFFSET = "param.offset";

	String PARAM_PAGE_SIZE = "param.page.size";

	String PARAM_PAGE_SIZE_MACRO = "param.page.size.macro";

	String REQUEST_HEADERS = "request.headers";

	String PARAM_ID_INCLUSIVE = "param.id.inclusive";

	/**
	 * Initial id to request data from
	 */
	String START_ID_FROM = "start.id.from";

	String IGNORE_SSL_CERT_VALIDATION = "ignore.ssl.cert.validation";

	public enum AuthType {
		NONE,
		TOKEN,
		BASIC,
		OAUTH1,
		OAUTH2,
		API_KEY,
		AWS_SIGNATURE,
		GCP_SERVICE_ACCOUNT
	}

	/**
	 * Authentication type: none, basic, oauth1, oauth2, api_key, aws_signature
	 */
	String AUTH_TYPE = "auth.type";

	String BASIC_USERNAME = "basic.username";
	String BASIC_PASSWORD = "basic.password";

	String CONTENT_TYPE = "content.type";
	String ACCEPT_HEADER = "accept.header";

	String DEFAULT_HEADERS_FALLBACK = "default.headers.fallback";

	String CLIENT_P12 = "client.p12";
	String CLIENT_P12_PASSWORD = "client.p12.password";

	String PAGING_HORIZONTAL_URL = "paging.horizontal.url";

	String BODY_AS_FILE_ADVANCED_MODE = "body.as.file.advanced.mode";
}