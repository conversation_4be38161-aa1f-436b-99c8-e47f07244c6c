package com.nexla.connector.properties;

import com.nexla.common.NexlaConstants;

public class FileConfigAccessor {

	public static final String FILE_LIST = "fileList";
	public static final String OFFSET_POSITION_KEY = "position";
	public static final String PARTITION_KEY = "file";

	public static final String MONITOR_POLL_MS = NexlaConstants.MONITOR_POLL_MS;

	public static final String OUTPUT_DIR_NAME_PATTERN = "output.dir.name.pattern";

	public static final String MAX_FILE_SIZE_MB = "max.file.size.mb";
	public static final String MAX_RECORDS_PER_FILE = "max.records.per.file";

	public static final String FILE_NAME_PREFIX = "file.name.prefix";
	public static final String FILE_NAME = "file.name";

	public static final String OPEN_FILES_CACHE_SIZE = "open.files.cache.size";
	public static final String GZIP = "gzip";

	public static final String LOCAL_BUFFER_DIR = "local.buffer.dir";

}