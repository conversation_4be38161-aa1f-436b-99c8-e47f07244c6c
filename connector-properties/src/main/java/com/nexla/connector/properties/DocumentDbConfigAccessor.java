package com.nexla.connector.properties;

public class DocumentDbConfigAccessor {
	public static final String BATCH_SIZE = "batch.size";

	public static final String MODE = "mode";
	public static final String MODE_TIMESTAMP = "timestamp";
	public static final String MODE_NONE = "none";
	public static final String MODE_QUERY = "query";

	public static final String TIMESTAMP_KEY = "timestamp.key";
	public static final String TIMESTAMP_LOAD_FROM = "timestamp.load.from";
	public static final String TIMESTAMP_LOAD_TO = "timestamp.load.to";

	public static final String COLLECTION = "collection";

	public static final String ID_FIELDS = "id.fields";
	public static final String INSERT_MODE = "insert.mode";
	public static final String DEFAULT_INSERT_MODE = "insert";

}
