package com.nexla.connector.config.kafka.pubsub;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SinkConnectorConfig;

import java.util.Map;

import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.kafka.KafkaSinkConnectorConfig.READ_CONCURRENCY;
import static org.apache.kafka.common.config.ConfigDef.Type.*;

public class PubSubSinkConnectorConfig extends SinkConnectorConfig {

	public static final String GROUP = "pubsub";

	private static final String TOPIC_NAME = "topic";
	private static final String SUBSCRIPTION_NAME = "subscription";
	private static final String INCLUDE_METADATA = "include.metadata";

	public final PubSubAuthConfig authConfig;

	public final String topicName;
	public final String subscriptionName;
	public final boolean includeMetadata;
	public final Integer readConcurrency;

	public PubSubSinkConnectorConfig(Map<String, String> originals) {
		super(configDef(), originals);

		Map<String, String> authMap = unitTest ? originals : getCreds(decryptKey, credsEnc, credsEncIv);
		this.authConfig = new PubSubAuthConfig(authMap, credsId);

		this.topicName = getString(TOPIC_NAME);
		this.subscriptionName = getString(SUBSCRIPTION_NAME);
		this.includeMetadata = getBoolean(INCLUDE_METADATA);
		this.readConcurrency = getInt(READ_CONCURRENCY);
	}

	public static NexlaConfigDef configDef() {

		return new NexlaConfigDef(sinkConfigDef())

			.withKey(nexlaKey(TOPIC_NAME, STRING, null)
				.documentation("Destination topic")
				.group(GROUP)
				.displayName("Destination topic to publish data to"))

			.withKey(nexlaKey(SUBSCRIPTION_NAME, STRING, null)
				.documentation("Destination subscription")
				.group(GROUP)
				.displayName("Destination subscription"))

			.withKey(nexlaKey(INCLUDE_METADATA, BOOLEAN, false)
				.documentation("Include metadata?")
				.group(GROUP)
				.displayName("Include metadata?"))

			.withKey(nexlaKey(READ_CONCURRENCY, INT, 1)
				.documentation("Read concurrency")
				.group(GROUP)
				.displayName("Read concurrency"))
			;
	}
}
