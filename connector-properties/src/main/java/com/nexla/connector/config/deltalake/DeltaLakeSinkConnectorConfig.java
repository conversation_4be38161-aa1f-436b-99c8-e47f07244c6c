package com.nexla.connector.config.deltalake;

import com.nexla.common.DeltaTableInsertMode;
import com.nexla.connector.config.AbstractNoLoggingConfig;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.file.FileSinkConnectorConfig;
import org.apache.kafka.common.config.ConfigDef;

import java.util.Map;
import java.util.Optional;

import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class DeltaLakeSinkConnectorConfig extends AbstractNoLoggingConfig {
    public static final String DELTA_LAKE_SINK_GROUP = "delta-lake-sink";

    public static final String DELTA_TABLE_INSERT_MODE = "delta.table.insert.mode";
    private static final String DELTA_TABLE_KEYS = "delta.table.keys";
    public static final String PARTITION_KEYS = "delta.partition-keys";


    // delta lake
    public final DeltaTableInsertMode deltaTableInsertMode;
    public final String deltaTableKeys;
    public final Optional<String> partitionKeys;

    public DeltaLakeSinkConnectorConfig(Map<String, String> originals) {
        super(configDef(), originals);
        this.deltaTableInsertMode = DeltaTableInsertMode.valueOf(getString(DELTA_TABLE_INSERT_MODE).toUpperCase());
        this.deltaTableKeys = getString(DELTA_TABLE_KEYS);
        this.partitionKeys = Optional.ofNullable(originals.get(PARTITION_KEYS));
    }

    public static NexlaConfigDef configDef() {
        return new NexlaConfigDef()

                .withKey(nexlaKey(DELTA_TABLE_INSERT_MODE, STRING, "insert")
                        .documentation("Delta Table Insert Mode")
                        .group(DELTA_LAKE_SINK_GROUP)
                        .displayName("Delta Table Insert Mode"))

                .withKey(nexlaKey(DELTA_TABLE_KEYS, STRING, null)
                        .documentation("Delta Table Keys")
                        .group(DELTA_LAKE_SINK_GROUP)
                        .displayName("Delta Table Keys"))

                .withKey(nexlaKey(PARTITION_KEYS, ConfigDef.Type.STRING, null)
                        .documentation("Comma separated list of column names to use for partitioning "
                                + "when creating a new table. If the table already exists we will not modify the table spec and "
                                + "this option will be ignored.")
                        .group(DELTA_LAKE_SINK_GROUP)
                        .displayName("Partition keys"))
                ;
    }
}
