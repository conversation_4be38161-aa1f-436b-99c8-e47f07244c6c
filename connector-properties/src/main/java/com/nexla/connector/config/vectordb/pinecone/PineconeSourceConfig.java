package com.nexla.connector.config.vectordb.pinecone;

import com.nexla.common.ConfigUtils;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.vectordb.VectorSourceConfig;
import lombok.Getter;
import org.apache.kafka.common.config.ConfigDef;

import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import static com.nexla.common.ConfigUtils.*;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;

@Getter
public class PineconeSourceConfig extends VectorSourceConfig {

    public static final String PINECONE_PREFIX = "pinecone.prefix";
    public static final String PINECONE_FETCH_LIMIT = "pinecone.fetch.limit";
    public static final String PINECONE_FILTER = "pinecone.filter";
    public static final String PINECONE_INCLUDE_VALUES = "pinecone.includeValues";
    public static final String PINECONE_INCLUDE_METADATA = "pinecone.includeMetadata";

    public final Optional<String> prefix;
    public final int fetchLimit;
    public final Optional<String> filter;
    public final boolean includeValues;
    public final boolean includeMetadata;

    public PineconeSourceConfig(Map<String, ?> queryConfigMap, Function<String, String> macroResolver) {
        super(pineconeQueryConfigDef(), queryConfigMap);

        this.fetchLimit = getInt(PINECONE_FETCH_LIMIT);

        this.prefix = optWithMacro(getString(PINECONE_PREFIX), macroResolver);
        this.filter = optWithMacro(getString(PINECONE_FILTER), macroResolver);
        this.includeValues = optBooleanWithMacro(getString(PINECONE_INCLUDE_VALUES), macroResolver).orElse(true);
        this.includeMetadata = optBooleanWithMacro(getString(PINECONE_INCLUDE_METADATA), macroResolver).orElse(true);
    }

    public static NexlaConfigDef pineconeQueryConfigDef() {
        return new NexlaConfigDef()
                .withKey(nexlaKey(PINECONE_PREFIX, ConfigDef.Type.STRING, null))
                .withKey(nexlaKey(PINECONE_FILTER, ConfigDef.Type.STRING, null))
                .withKey(nexlaKey(PINECONE_FETCH_LIMIT, ConfigDef.Type.INT, 100))
                .withKey(nexlaKey(PINECONE_INCLUDE_VALUES, ConfigDef.Type.STRING, "true"))
                .withKey(nexlaKey(PINECONE_INCLUDE_METADATA, ConfigDef.Type.STRING, "true"))
                ;
    }
}
