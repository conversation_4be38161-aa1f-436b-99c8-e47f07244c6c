package com.nexla.connector.config.vectordb.pinecone;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.vectordb.VectorSinkConfig;
import lombok.Getter;
import org.apache.kafka.common.config.ConfigDef;

import java.util.Map;

import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;

@Getter
public class PineconeSinkConfig extends VectorSinkConfig {

    public static final String PINECONE_UPSERT_PARALLELISM = "pinecone.upsert.parallelism";

    public final int upsertParallelism;

    public PineconeSinkConfig(Map<String, ?> queryConfigMap) {
        super(pineconeSinkConfigDef(), queryConfigMap);

        this.upsertParallelism = getInt(PINECONE_UPSERT_PARALLELISM);
    }

    public static NexlaConfigDef pineconeSinkConfigDef() {
        return new NexlaConfigDef()
                .withKey(nexlaKey(PINECONE_UPSERT_PARALLELISM, ConfigDef.Type.INT, 1))
                ;
    }
}
