package com.nexla.connector.config.kinesis;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SinkConnectorConfig;
import com.nexla.connector.config.file.AWSAuthConfig;

import java.util.Map;

import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.SourceConnectorConfig.EnumValidator;
import static org.apache.kafka.common.config.ConfigDef.NO_DEFAULT_VALUE;
import static org.apache.kafka.common.config.ConfigDef.Type;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class KinesisSinkConnectorConfig extends SinkConnectorConfig {

	public static final String SINK_GROUP = "";

	private static final String REGION = "region";
	private static final String STREAM_NAME = "stream.name";
	private static final String MAX_BUFFERED_TIME = "max.buffered.time";
	private static final String MAX_CONNECTIONS = "max.connections";
	private static final String RATE_LIMIT = "rate.limit";
	private static final String RECORD_TTL = "ttl";
	private static final String METRICS_LEVEL = "metrics.level";
	private static final String METRICS_GRANUALITY = "metrics.granularity";
	private static final String METRICS_NAMESPACE = "metrics.namespace";
	private static final String AGGREGATION_ENABLED = "aggregation";
	private static final String USE_PARTITION_AS_HASH_KEY = "use.partition.as.hash.key";

	public final String region;
	public final String streamName;
	public final Long maxBufferedTime;
	public final Long maxConnections;
	public final Long rateLimit;
	public final Long ttl;
	public final String metricsLevel;
	public final String metricsGranuality;
	public final String metricsNameSpace;
	public final boolean aggregationEnabled;
	public final boolean usePartitionAsHashKey;

	public AWSAuthConfig authConfig;

	public KinesisSinkConnectorConfig(Map<String, String> originals) {
		super(configDef(), originals);

		this.region = getString(REGION);
		this.streamName = getString(STREAM_NAME);
		this.maxBufferedTime = getLong(MAX_BUFFERED_TIME);
		this.maxConnections = getLong(MAX_CONNECTIONS);
		this.rateLimit = getLong(RATE_LIMIT);
		this.ttl = getLong(RECORD_TTL);
		this.metricsLevel = getString(METRICS_LEVEL);
		this.metricsGranuality = getString(METRICS_GRANUALITY);
		this.metricsNameSpace = getString(METRICS_NAMESPACE);
		this.aggregationEnabled = getBoolean(AGGREGATION_ENABLED);
		this.usePartitionAsHashKey = getBoolean(USE_PARTITION_AS_HASH_KEY);

		this.authConfig = new AWSAuthConfig(unitTest ? originals : getCreds(decryptKey, credsEnc, credsEncIv), credsId);
	}

	public static NexlaConfigDef configDef() {

		int index = 0;

		return new NexlaConfigDef(sinkConfigDef())

			.withKey(nexlaKey(STREAM_NAME, STRING, NO_DEFAULT_VALUE)
				.documentation("Stream name")
				.group(SINK_GROUP)
				.displayName("Stream name"))

			.withKey(nexlaKey(REGION, STRING, null)
				.documentation("Region")
				.group(SINK_GROUP)
				.displayName("Region"))
			.withKey(nexlaKey(MAX_BUFFERED_TIME, Type.LONG, 15000L)
				.documentation("Max buffered time")
				.group(SINK_GROUP)
				.displayName("Maximum amount of time (milliseconds) a record may spend being " +
							 " buffered before it gets sent. Records may be sent sooner than this depending " +
							 " on the other buffering limits"))

			.withKey(nexlaKey(MAX_CONNECTIONS, Type.LONG, 24L)
				.documentation("Max connections")
				.group(SINK_GROUP)
				.displayName("Max connection"))
			.withKey(nexlaKey(RATE_LIMIT, Type.LONG, 100L)
				.documentation("Rate limit")
				.group(SINK_GROUP)
				.displayName("Limits the maximum allowed put rate for a shard, as a percentage of the backend limits"))

			.withKey(nexlaKey(RECORD_TTL, Type.LONG, 60000L)
				.documentation("Record TTL")
				.group(SINK_GROUP)
				.displayName("Set a time-to-live on records (milliseconds). Records that do not get" +
							 "successfully put within the limit are failed."))

			.withKey(nexlaKey(METRICS_LEVEL, Type.STRING, "none")
				.validator(EnumValidator.in(MetricsLevel.values()))
				.documentation("Metrics level")
				.group(SINK_GROUP)
				.displayName("Controls the number of metrics that are uploaded to CloudWatch"))

			.withKey(nexlaKey(METRICS_GRANUALITY, Type.STRING, "global")
				.validator(EnumValidator.in(MetricsGranularity.values()))
				.documentation("Metrics granularity")
				.group(SINK_GROUP)
				.displayName("Controls the granularity of metrics that are uploaded to CloudWatch. " +
							 "Greater granularity produces more metrics."))

			.withKey(nexlaKey(METRICS_NAMESPACE, Type.STRING, "KinesisProducer")
				.documentation("Metrics namespace")
				.group(SINK_GROUP)
				.displayName("The namespace to upload metrics under"))

			.withKey(nexlaKey(AGGREGATION_ENABLED, Type.BOOLEAN, false)
				.documentation("Aggregation enabled")
				.group(SINK_GROUP)
				.displayName("Aggregation enabled"))

			.withKey(nexlaKey(USE_PARTITION_AS_HASH_KEY, Type.BOOLEAN, false)
				.documentation("Use partition as hash key")
				.group(SINK_GROUP)
				.displayName("Use partition as hash key"))
			;
	}

	public enum MetricsLevel {
		NONE,
		SUMMARY,
		DETAILED
	}

	public enum MetricsGranularity {
		GLOBAL,
		STREAM,
		SHARD
	}
}
