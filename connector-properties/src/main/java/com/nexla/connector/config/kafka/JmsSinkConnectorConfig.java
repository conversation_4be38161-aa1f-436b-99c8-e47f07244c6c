package com.nexla.connector.config.kafka;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SinkConnectorConfig;

import java.util.Map;

import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.kafka.KafkaSinkConnectorConfig.READ_CONCURRENCY;
import static org.apache.kafka.common.config.ConfigDef.Type.*;

public class JmsSinkConnectorConfig extends SinkConnectorConfig {

	public static final String GROUP = "JMS";

	private static final String TARGET_NAME = "target.name";
	private static final String JMS_VENDOR = "jms.vendor";
	private static final String TARGET_TYPE = "target.type";
	private static final String INCLUDE_METADATA = "include.metadata";

	public final JmsAuthConfig authConfig;

	public final String targetName;
	public final String jmsType;
	public final String targetType;
	public final Boolean includeMetadata;
	public final Integer readConcurrency;

	public JmsSinkConnectorConfig(Map<String, String> originals) {
		super(configDef(), originals);

		Map<String, String> authMap = unitTest ? originals : getCreds(decryptKey, credsEnc, credsEncIv);
		this.authConfig = new JmsAuthConfig(authMap, credsId);

		this.targetName = getString(TARGET_NAME);
		this.jmsType = getString(JMS_VENDOR);
		this.targetType = getString(TARGET_TYPE);
		this.includeMetadata = getBoolean(INCLUDE_METADATA);
		this.readConcurrency = getInt(READ_CONCURRENCY);
	}

	public static NexlaConfigDef configDef() {

		return new NexlaConfigDef(sinkConfigDef())

			.withKey(nexlaKey(TARGET_NAME, STRING, null)
				.documentation("Destination topic")
				.group(GROUP)
				.displayName("Destination topic to publish data to"))

			.withKey(nexlaKey(JMS_VENDOR, STRING, null)
				.documentation("JMS vendor: [activemq, tibco, rabbitmq]")
				.group(GROUP)
				.displayName("JMS vendor: [activemq, tibco, rabbitmq]"))

			.withKey(nexlaKey(TARGET_TYPE, STRING, "topic")
				.documentation("Target type: [topic, queue]")
				.group(GROUP)
				.displayName("Target type: [topic, queue]"))

			.withKey(nexlaKey(INCLUDE_METADATA, BOOLEAN, false)
				.documentation("Include metadata")
				.group(GROUP)
				.displayName("Include metadata"))

			.withKey(nexlaKey(READ_CONCURRENCY, INT, 1)
				.documentation("Read concurrency")
				.group(GROUP)
				.displayName("Read concurrency"))

			;
	}
}
