package com.nexla.connector.config.api_streams;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.connector.config.rest.RestIterationConfigBuilder;
import lombok.Getter;
import lombok.ToString;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.api_streams.properties.ApiStreamsConfigAccessor.*;
import static org.apache.kafka.common.config.ConfigDef.Type.LIST;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

@ToString
@Getter
public class SelfContainedDataStreamConfig extends BaseEltStreamConfig {
    private final List<RestIterationConfig> restIterationConfigs;
    public final List<String> primaryKeys;
    public final Optional<Object> metadata;

    public SelfContainedDataStreamConfig(Map<String, Object> originals,
                                         RestIterationConfigBuilder restIterationConfigBuilder,
                                         Optional<Map<String, String>> params) {
        super(configDef(), originals, DATA_FEED_CONFIG_TYPE, params);
        this.restIterationConfigs = restIterationConfigBuilder.buildConfigs(originals);
        this.primaryKeys = getList(PRIMARY_KEYS);
        this.metadata = Optional.ofNullable(originals.get(FEED_SPEC_METADATA));
    }

    @Override
    public List<RestIterationConfig> getRestIterationConfigs() {
        return restIterationConfigs;
    }

    @Override
    public Optional<String> getRefSpec() {
        return Optional.empty();
    }

    public static NexlaConfigDef configDef() {
        return new NexlaConfigDef()
                .withKey(nexlaKey(REST_ITERATIONS_JSON, STRING, null)
                        .documentation("List of REST iterations")
                        .displayName("REST Iterations"))
                .withKey(nexlaKey(PRIMARY_KEYS, LIST, null)
                        .documentation("Primary keys for columns")
                        .displayName("Primary keys"))
                .withKey(nexlaKey(FEED_SPEC_METADATA, STRING, null)
                    .documentation("Metadata containing extra configuration that must be used, format is JSON map, e.g {\"overridden_mappings\": { ... }}")
                    .displayName("Metadata"));
    }
}
