package com.nexla.connector.config.kafka;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SinkConnectorConfig;

import java.util.Map;
import java.util.Optional;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.apache.kafka.common.config.ConfigDef.Type.BOOLEAN;
import static org.apache.kafka.common.config.ConfigDef.Type.INT;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class KafkaSinkConnectorConfig extends SinkConnectorConfig {

	public static final String KAFKA_GROUP = "kafka";
	private static final String DEFAULT_KEY_SER = "org.apache.kafka.common.serialization.StringSerializer";
	private static final String DEFAULT_VALUE_SER = "org.apache.kafka.common.serialization.StringSerializer";

	private static final String TOPIC_NAME = "topic";
	private static final String TOPIC_REPLICATION_FACTOR = "topic.replication.factor";
	private static final String TOPIC_PARTITIONS = "topic.partitions";

	private static final String KEY_SERIALIZER = "key.serializer";
	private static final String VALUE_SERIALIZER = "value.serializer";
	private static final String MESSAGE_KEY = "message.key";
	private static final String INCLUDE_METADATA = "include.metadata";
	public static final String READ_CONCURRENCY = "read.concurrency";
	public static final String MAX_REQUEST_SIZE = "max.request.size";

	public final KafkaAuthConfig authConfig;
	public final String topic;
	public final Integer partitions;
	public final Integer replicationFactor;
	public final String keySerializer;
	public final String valueSerializer;
	public final Optional<String> messageKey;
	public final Boolean includeMetadata;
	public final Integer readConcurrency;
	public final Integer maxRequestSize;

	public KafkaSinkConnectorConfig(Map<String, String> originals) {
		super(configDef(), originals);

		Map<String, String> authMap = unitTest ? originals : getCreds(decryptKey, credsEnc, credsEncIv);

		this.topic = getString(TOPIC_NAME);
		this.partitions = getInt(TOPIC_PARTITIONS);
		this.replicationFactor = getInt(TOPIC_REPLICATION_FACTOR);
		this.keySerializer = getString(KEY_SERIALIZER);
		this.valueSerializer = getString(VALUE_SERIALIZER);
		this.authConfig = new KafkaAuthConfig(authMap, credsId);
		this.messageKey = opt(getString(MESSAGE_KEY));
		this.includeMetadata = getBoolean(INCLUDE_METADATA);
		this.readConcurrency = getInt(READ_CONCURRENCY);
		this.maxRequestSize = getInt(MAX_REQUEST_SIZE);
	}

	public static NexlaConfigDef configDef() {

		return new NexlaConfigDef(sinkConfigDef())

			.withKey(nexlaKey(TOPIC_NAME, STRING, null)
				.documentation("Destination topic")
				.group(KAFKA_GROUP)
				.displayName("Destination topic to publish data to"))

			.withKey(nexlaKey(KEY_SERIALIZER, STRING, DEFAULT_KEY_SER)
				.documentation("Key serializer")
				.group(KAFKA_GROUP)
				.displayName("Key Serializer for writing keys to Kafka topic"))

			.withKey(nexlaKey(VALUE_SERIALIZER, STRING, DEFAULT_VALUE_SER)
				.documentation("Value serializer")
				.group(KAFKA_GROUP)
				.displayName("Value Serializer for writing keys to Kafka topic"))

			.withKey(nexlaKey(TOPIC_REPLICATION_FACTOR, INT, 3)
				.documentation("Topic replication factor")
				.group(KAFKA_GROUP)
				.displayName("Topic replication factor (Only relevant for topic creation)"))

			.withKey(nexlaKey(TOPIC_PARTITIONS, INT, 3)
				.documentation("# of Topic Partitions")
				.group(KAFKA_GROUP)
				.displayName("# of Topic Partitions (Only relevant for topic creation)"))

			.withKey(nexlaKey(MESSAGE_KEY, STRING, null)
				.documentation("Kafka event key")
				.group(KAFKA_GROUP)
				.displayName("Select the key attribute from the dataset, default is empty"))

			.withKey(nexlaKey(INCLUDE_METADATA, BOOLEAN, false)
				.documentation("Include metadata")
				.group(KAFKA_GROUP)
				.displayName("Include metadata"))

			.withKey(nexlaKey(READ_CONCURRENCY, INT, 1)
				.documentation("Read concurrency")
				.group(KAFKA_GROUP)
				.displayName("Read concurrency"))

			.withKey(nexlaKey(MAX_REQUEST_SIZE, INT, 10000120)
				.documentation("Max request size")
				.group(KAFKA_GROUP)
				.displayName("Max request size"))
			;
	}
}
