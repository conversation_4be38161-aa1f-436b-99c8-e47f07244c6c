package com.nexla.connector.config.documentdb;

import com.nexla.common.ConnectionType;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SinkConnectorConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.properties.DocumentDbConfigAccessor;
import org.apache.kafka.common.config.ConfigDef;

import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.nexla.common.NexlaConstants.CREDENTIALS_TYPE;
import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static java.util.Collections.emptyList;

public class DocumentDbSinkConnectorConfig extends SinkConnectorConfig implements WithAuth {

	public enum InsertMode {
		INSERT,
		UPSERT
	}

	public final String collection;
	public final Set<String> idFields;
	public final InsertMode insertMode;
	public final ConnectionType connectionType;

	public final BaseAuthConfig authConfig;

	public DocumentDbSinkConnectorConfig(Map<String, String> originals) {
		super(configDef(), originals);

		this.collection = getString(DocumentDbConfigAccessor.COLLECTION);
		this.idFields = new HashSet<>(getList(DocumentDbConfigAccessor.ID_FIELDS));
		this.insertMode = InsertMode.valueOf(getString(DocumentDbConfigAccessor.INSERT_MODE).toUpperCase());
		Map<String, String> parsedConfig = unitTest ? originals : getCreds(decryptKey, credsEnc, credsEncIv);

		String connectionTypeStr = Optional.ofNullable(getString(CREDENTIALS_TYPE))
				.orElse(parsedConfig.get(CREDENTIALS_TYPE));
		this.connectionType = ConnectionType.fromString(connectionTypeStr.toUpperCase());

		this.authConfig = newAuthConfig(connectionType, parsedConfig, credsId);
	}

	public static NexlaConfigDef configDef() {
		return new NexlaConfigDef(sinkConfigDef())
			.withKey(nexlaKey(DocumentDbConfigAccessor.COLLECTION, ConfigDef.Type.STRING, null))
			.withKey(nexlaKey(DocumentDbConfigAccessor.ID_FIELDS, ConfigDef.Type.LIST, emptyList())
				.importance(ConfigDef.Importance.LOW))
			.withKey(nexlaKey(DocumentDbConfigAccessor.INSERT_MODE, ConfigDef.Type.STRING, DocumentDbConfigAccessor.DEFAULT_INSERT_MODE))
			.withKey(nexlaKey(CREDENTIALS_TYPE, ConfigDef.Type.STRING, null))
			;
	}
}
