package com.nexla.connector.config.vectordb;

import com.nexla.common.ConnectionType;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.NexlaConfigKeyBuilder;
import com.nexla.connector.config.SourceConnectorConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.ssh.tunnel.ConfigWithAuth;
import lombok.Getter;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.common.config.ConfigException;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.nexla.common.ConfigUtils.*;
import static com.nexla.common.NexlaConstants.CREDENTIALS_TYPE;
import static com.nexla.common.NexlaConstants.SOURCE_TYPE;
import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.properties.VectorDbConfigAccessor.*;
import static org.apache.kafka.common.config.ConfigDef.NO_DEFAULT_VALUE;
import static org.apache.kafka.common.config.ConfigDef.Type.*;

@Getter
public class VectorSourceConnectorConfig extends SourceConnectorConfig implements ConfigWithAuth<BaseAuthConfig>,
		WithVectorAuthConfig, WithVectorSourceConfig {

	public static final String VECTORDB_SOURCE_GROUP = "";

	// Specific connector may not support all possible search types
	public enum SearchBy {
		VECTOR_ID,
		DENSE_VECTOR
	}

	public final BaseAuthConfig authConfig;
	public final ConnectionType connectionType;
	public final VectorSourceConfig providerSourceConfig;

	public final String database;
	public final String queryType;

	public final SearchBy searchBy;

	public final Optional<String> collection;
	public final Optional<Integer> topK;
	public final Optional<String> vectorId;

	public final List<Double> denseVector;
	public final List<Long> sparseVectorIndices;
	public final List<Double> sparseVectorValues;

	public int batchSizeApprox;

	public VectorSourceConnectorConfig(Map<String, Object> originals) {
		super(configDef(), originals);

		Map<String, ?> parsedConfig = unitTest ? originals : getCreds(decryptKey, credsEnc, credsEncIv);
		String connectionTypeStr = Optional.ofNullable(getString(CREDENTIALS_TYPE)).orElse((String)parsedConfig.get(CREDENTIALS_TYPE));

		this.connectionType = ConnectionType.fromString(connectionTypeStr.toUpperCase());
		this.providerSourceConfig = newVectorSourceConfig(connectionType, originals, this::replaceNowVars);
		this.authConfig = newAuthConfig(connectionType, parsedConfig, credsId);

		this.batchSizeApprox = getInt(BATCH_SIZE);

		// Settings with macros
		this.database = withMacro(getString(DATABASE));
		this.queryType = withMacro(getString(QUERY_TYPE));

		this.collection = optWithMacro(getString(COLLECTION));
		this.topK = optIntWithMacro(getString(TOPK));

		this.searchBy = selectSearchBy(withMacro(getString(SEARCH_BY)).toUpperCase().trim());

		this.vectorId = optWithMacro(getString(VECTOR_ID));
		this.denseVector = parseVectorValuesWithMacro(originals, DENSE_VECTOR);
		this.sparseVectorIndices = parseVectorIndexesWithMacro(originals, SPARSE_VECTOR_INDICES);
		this.sparseVectorValues = parseVectorValuesWithMacro(originals, SPARSE_VECTOR_VALUES);
	}

	public static NexlaConfigDef configDef() {
		return new NexlaConfigDef(sourceConfigDef())
			.withKey(k(QUERY_TYPE, STRING, SIMILARITY_SEARCH, "Search query type", "Query Type"))
			.withKey(k(CREDENTIALS_TYPE, STRING, null))
			.withKey(k(COLLECTION, STRING, null, "Collection name", "Collection"))
			.withKey(k(DATABASE, STRING, NO_DEFAULT_VALUE, "Database name", "Database"))
			.withKey(k(TOPK, STRING, "20", "Number of vectors to be fetched", "Top K"))
			.withKey(k(SEARCH_BY, STRING, SearchBy.DENSE_VECTOR.name(), "Search parameters for similarity search", "Search By"))
			.withKey(k(VECTOR_ID, STRING, null, "Vector ID for search", "Vector ID"))
			.withKey(k(DENSE_VECTOR, STRING, null, "Dense vector for search", "Dense Vector"))
			.withKey(k(SPARSE_VECTOR_INDICES, STRING, null, "Sparse vector indices for search", "Sparse Vector Indices"))
			.withKey(k(SPARSE_VECTOR_VALUES, STRING, null, "Sparse vector values for search", "Sparse Vector Values"))
			.withKey(k(BATCH_SIZE, INT, 1000, "Number of rows to be read at once", "Batch size"))
			;
	}

	private SearchBy selectSearchBy(String searchBy) {
		try {
			return SearchBy.valueOf(searchBy.toUpperCase());
		} catch (IllegalArgumentException e) {
			throw new ConfigException(SEARCH_BY, searchBy, "Expected vector_id or dense_vector]");
		}
	}

	private List<Double> parseVectorValuesWithMacro(Map<String, Object> originals, String key) {
		try {
			return optListWithMacro(originals.get(key), this::replaceNowVars).map(this::parseDoubleListParameters).orElse(List.of());
		} catch (Exception e) {
			throw new ConfigException(key, getString(key), "Expected a comma separated list of float values.");
		}
	}

	private List<Long> parseVectorIndexesWithMacro(Map<String, Object> originals, String key) {
		try {
			return optListWithMacro(originals.get(key), this::replaceNowVars).map(this::parseLongListParameters).orElse(List.of());
		} catch (Exception e) {
			throw new ConfigException(key, getString(key), "Expected a comma separated list of integer values.");
		}
	}

	private List<Long> parseLongListParameters(List<String> vector) {
		if (vector != null) {
			return vector.stream().map(x -> Long.parseLong(x)).collect(Collectors.toList());
		} else {
			return null;
		}
	}

	private List<Double> parseDoubleListParameters(List<String> vector) {
		if (vector != null) {
			return vector.stream().map(x -> Double.parseDouble(x)).collect(Collectors.toList());
		} else {
			return null;
		}
	}

	private static NexlaConfigKeyBuilder k(String name, ConfigDef.Type type, Object defaultValue) {
		return nexlaKey(name, type, defaultValue);
	}

	private static NexlaConfigKeyBuilder k(String name, ConfigDef.Type type, Object defaultValue, String documentation, String displayName) {
		return k(name, type, defaultValue)
				.documentation(documentation)
				.displayName(displayName);
	}

	@Override
	public BaseAuthConfig authConfig() {
		return authConfig;
	}
}
