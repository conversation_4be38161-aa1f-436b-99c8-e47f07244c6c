package com.nexla.connector.config.kafka.pubsub;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.file.GCPAuthConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.rest.RestAuthConfig;

import java.util.Map;

import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class PubSubAuthConfig extends GCPAuthConfig {
	public final Map<String, String> credsMap;

	public PubSubAuthConfig(Map<String, String> parsedConfig, Integer credId) {
		super(authConfigDef(), parsedConfig, credId);

		this.credsMap = parsedConfig;
	}

	public static NexlaConfigDef authConfigDef() {
		return GCPAuthConfig.configDef();
	}

}
