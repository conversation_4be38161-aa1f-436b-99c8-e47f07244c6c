package com.nexla.connector.config.kafka;

import com.nexla.common.ResourceType;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.rest.BaseAuthConfig;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.ConfigDef;

import java.util.Map;
import java.util.Optional;

import static com.nexla.common.NexlaConstants.RESOURCE_ID;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.SourceConnectorConfig.EnumValidator;
import static java.util.Optional.ofNullable;
import static org.apache.kafka.common.config.ConfigDef.Type.BOOLEAN;
import static org.apache.kafka.common.config.ConfigDef.Type.INT;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class KafkaBackendReadConfig extends BaseAuthConfig {

	public static final String EVENT_COUNT = "event_count";
	public static final String ID = "id";
	public static final String RESOURCE_TYPE = "resource_type";
	public static final String PARTITION = "partition";
	public static final String OFFSET = "offset";
	public static final String LATEST = "latest";
	public static final String SAMPLE = "sample";
	public static final String START_TIME_MS = "start_time";
	public static final String END_TIME_MS = "end_time";
	public static final String PAGE_NUMBER = "page";
	public static final String PAGE_SIZE = "per_page";
	public static final String SORT_ORDER_LATEST = "sort_order_latest";

	public static final String FETCH_MAX_BYTES = ConsumerConfig.FETCH_MAX_BYTES_CONFIG;
	public static final String MAX_PARTITION_FETCH_BYTES = ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG;

	public static final String TOPIC = "topic";
	static final int DEFAULT_EVENT_COUNT = 100;
	static final int DEFAULT_PAGE_SIZE = 100;
	public static final NexlaConfigDef CONFIG_DEF = configDef();
	public final Integer eventCount;
	public final Integer partition;
	public final boolean sample;
	public final String topic;
	public final boolean latest;
	public final Long offset;
	public final ResourceType resourceType;
	public final Integer id;
	public final Integer resourceId;
	public final Optional<Long> startTimeMs;
	public final Optional<Long> endTimeMs;
	public final Optional<Integer> pageNumber;
	public final Integer pageSize;
	public final boolean sortOrderLatest;
	public final Integer fetchMaxBytes;
	public final Integer maxPartitionFetchBytes;

	public KafkaBackendReadConfig(Map<String, String> parsedConfig) {
		super(CONFIG_DEF, parsedConfig, null);

		this.eventCount = getInt(EVENT_COUNT);
		this.partition = getInt(PARTITION);
		this.sample = getBoolean(SAMPLE);
		this.topic = getString(TOPIC);
		this.latest = getBoolean(LATEST);
		this.offset = getLong(OFFSET);
		this.resourceType = ResourceType.fromString(getString(RESOURCE_TYPE).toUpperCase());
		this.resourceId = getInt(RESOURCE_ID);
		this.id = getInt(ID);
		this.startTimeMs = ofNullable(getLong(START_TIME_MS));
		this.endTimeMs = ofNullable(getLong(END_TIME_MS));
		this.pageNumber = ofNullable(getInt(PAGE_NUMBER));
		this.pageSize = getInt(PAGE_SIZE);
		this.sortOrderLatest = getBoolean(SORT_ORDER_LATEST);
		this.fetchMaxBytes = getInt(FETCH_MAX_BYTES);
		this.maxPartitionFetchBytes = getInt(MAX_PARTITION_FETCH_BYTES);
	}

	public static NexlaConfigDef configDef() {

		return baseAuthConfigDef()

			.withKey(nexlaKey(EVENT_COUNT, INT, DEFAULT_EVENT_COUNT))
			.withKey(nexlaKey(PARTITION, INT, null))
			.withKey(nexlaKey(SAMPLE, BOOLEAN, false))
			.withKey(nexlaKey(TOPIC, STRING, null))
			.withKey(nexlaKey(LATEST, BOOLEAN, false))
			.withKey(nexlaKey(OFFSET, ConfigDef.Type.LONG, null))
			.withKey(nexlaKey(RESOURCE_TYPE, ConfigDef.Type.STRING, ResourceType.SOURCE.name())
				.validator(EnumValidator.in(ResourceType.values())))
			.withKey(nexlaKey(RESOURCE_ID, INT, null))
			.withKey(nexlaKey(ID, ConfigDef.Type.INT, null))
			.withKey(nexlaKey(START_TIME_MS, ConfigDef.Type.LONG, null))
			.withKey(nexlaKey(END_TIME_MS, ConfigDef.Type.LONG, null))
			.withKey(nexlaKey(PAGE_NUMBER, ConfigDef.Type.INT, null))
			.withKey(nexlaKey(PAGE_SIZE, ConfigDef.Type.INT, DEFAULT_PAGE_SIZE))
			.withKey(nexlaKey(SORT_ORDER_LATEST, BOOLEAN, true))
			.withKey(nexlaKey(FETCH_MAX_BYTES, ConfigDef.Type.INT, null))
			.withKey(nexlaKey(MAX_PARTITION_FETCH_BYTES, ConfigDef.Type.INT, null))
			;
	}
}
