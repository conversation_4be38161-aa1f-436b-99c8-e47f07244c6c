package com.nexla.connector.config.vectordb;

import com.nexla.common.ConnectionType;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.vectordb.pinecone.PineconeAuthConfig;

import java.util.Map;

import static com.nexla.common.ConnectionType.PINECONE;

public interface WithVectorAuthConfig { // todo better naming

    default BaseAuthConfig newAuthConfig(ConnectionType connectionType, Map<String, ?> parsedConfig, int credsId) {
      if (connectionType.equals(PINECONE)) {
        return new PineconeAuthConfig(parsedConfig, credsId);
      }
      throw new IllegalArgumentException("Not a vector based database: " + connectionType);
    }

}
