package com.nexla.connector.config.soap;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.common.parse.ParserConfigs;
import com.nexla.common.time.EpochDateTimeFormatter;
import com.nexla.common.time.NexlaTimeUnit;
import com.nexla.connector.config.AbstractNoLoggingConfig;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SourceConnectorConfig;
import com.nexla.connector.config.rest.RestAuthConfig;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.common.config.ConfigException;
import org.springframework.http.HttpMethod;

import java.nio.file.Paths;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.NexlaConstants.MAP_ID;
import static com.nexla.common.NexlaConstants.REDIS_CREDS_ENC;
import static com.nexla.common.NexlaConstants.REDIS_CREDS_ENC_IV;
import static com.nexla.common.NexlaConstants.SOAP_PARAMS;
import static com.nexla.connector.config.BaseConnectorConfig.DATE_FORMAT;
import static com.nexla.connector.config.BaseConnectorConfig.DATE_TIME_UNIT;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.soap.SoapSourceConnectorConfig.SOURCE_GROUP;
import static com.nexla.connector.properties.RestConfigAccessor.DATASET_COLUMNS;
import static com.nexla.connector.properties.RestConfigAccessor.METHOD;
import static com.nexla.connector.properties.RestConfigAccessor.RESPONSE_DATA_PATH;
import static com.nexla.connector.properties.RestConfigAccessor.RETRY_DELAY;
import static com.nexla.connector.properties.RestConfigAccessor.RETRY_THRESHOLD_NUMBER;
import static com.nexla.connector.properties.RestConfigAccessor.START_ID_FROM;
import static java.util.Collections.emptyList;
import static java.util.Optional.ofNullable;
import static org.apache.kafka.common.config.ConfigDef.Importance.LOW;
import static org.apache.kafka.common.config.ConfigDef.Type.LIST;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class SoapIterationConfig extends AbstractNoLoggingConfig {

	public static final int DEFAULT_RETRY_THRESHOLD = 0;
	public static final int DEFAULT_RETRY_DELAY = 1000;
	public static final int MINIMAL_PARALLELISM_COUNT = 1;
	public static final String REDIS_QUEUE_NAME = "redis.queue.name";
	public static final String REQUEST_PARALLELISM_COUNT = "request.parallelism.count";
	public static final String KEY = "key";
	public static final String START_PAGE_FROM = "start.page.from";
	public static final String CHARSET_DETECTION_CONFIDENCE_THRESHOLD = ParserConfigs.CHARSET_DETECTION_CONFIDENCE_THRESHOLD;
	public static final String SOAP_ENV_V1 = "/SOAP-ENV:Envelope/SOAP-ENV:Body/";
	public static final String SOAP_ENV_V2 = "/soap:Envelope/soap:Body/";

	public final Map<String, String> soapFormParams;

	public final String responseDataPath;
	public final int requestParallelismCount;

	public final String credentialsDecryptKey;
	public final Boolean unitTest;
	public final String key;
	public final Integer sourceId;
	public final Optional<EpochDateTimeFormatter> dateFormat;
	public final Optional<NexlaTimeUnit> dateTimeUnit;

	public final RestAuthConfig authConfig;
	public final HttpMethod method;
	public final int retryThreshold;
	public final int retryDelay;
	public final int charsetDetectionThreshold;
	public final SoapWsdlConfig soapConf;

	public SoapIterationConfig(
		Map<String, ?> originals,
		String credentialsDecryptKey,
		Boolean unitTest,
		Integer sourceId,
		Integer charsetDetectionThreshold,
		RestAuthConfig authConfig
	) {
		super(configDef(), originals);
		this.key = getString(KEY);

		this.unitTest = unitTest;
		this.credentialsDecryptKey = credentialsDecryptKey;
		this.sourceId = sourceId;

		this.authConfig = authConfig;
		this.method = HttpMethod.valueOf(getString(METHOD).toUpperCase());
		this.requestParallelismCount = getInt(REQUEST_PARALLELISM_COUNT);

		this.dateFormat = opt(getString(DATE_FORMAT)).map(EpochDateTimeFormatter::new);
		this.dateTimeUnit = opt(getString(DATE_TIME_UNIT)).map(NexlaTimeUnit::findByPattern);
		this.retryThreshold = getInt(RETRY_THRESHOLD_NUMBER);
		this.retryDelay = getInt(RETRY_DELAY);
		this.charsetDetectionThreshold = ofNullable(getInt(CHARSET_DETECTION_CONFIDENCE_THRESHOLD))
			.orElse(charsetDetectionThreshold);

		Map params = JsonUtils.jsonToMap(getString(SOAP_PARAMS));
		this.soapFormParams = params;

		this.soapConf = new SoapWsdlConfig(originals);
		this.responseDataPath = getResponseDataPath();
	}

	private String getResponseDataPath() {
		final String basePath;
		if (soapConf.getSoapVersion() == SoapWsdlConfig.SoapVersion.V1_1) {
			basePath = SOAP_ENV_V1;
		} else {
			basePath = SOAP_ENV_V2;
		}
		String result = basePath + opt(getString(RESPONSE_DATA_PATH)).orElse("*");
		return Paths.get(result).toString();
	}

	public static NexlaConfigDef configDef() {

		NexlaConfigDef sinkConfig = new NexlaConfigDef()
			.withKey(nexlaKey(KEY, STRING, "KEY")
				.documentation("Rest Iteration key")
				.group(SOURCE_GROUP)
				.displayName("Rest Iteration key"))

			.withKey(nexlaKey(DATASET_COLUMNS, LIST, emptyList())
				.documentation("Dataset columns")
				.group(SOURCE_GROUP)
				.displayName("Dataset columns"))

			.withKey(nexlaKey(START_PAGE_FROM, ConfigDef.Type.LONG, 1)
				.documentation("Start page to load data from")
				.group(SOURCE_GROUP)
				.displayName("Start page to load data from"))

			.withKey(nexlaKey(START_ID_FROM, ConfigDef.Type.STRING, null)
				.documentation("Start ID to load data from")
				.group(SOURCE_GROUP)
				.displayName("Start ID to load data from"))

			.withKey(nexlaKey(METHOD, ConfigDef.Type.STRING, HttpMethod.GET.name())
				.validator(SourceConnectorConfig.EnumValidator.in(HttpMethod.values()))
				.documentation("API Method")
				.group(SOURCE_GROUP)
				.displayName("API Method"))

			.withKey(nexlaKey(RESPONSE_DATA_PATH, ConfigDef.Type.STRING, null)
				.documentation("Data path")
				.group(SOURCE_GROUP)
				.displayName("Data path as XPath or JsonPath"))

			.withKey(nexlaKey(MAP_ID, ConfigDef.Type.INT, null)
				.importance(LOW)
				.documentation("Map id")
				.group(SOURCE_GROUP)
				.displayName("Redis map id"))

			.withKey(nexlaKey(REDIS_QUEUE_NAME, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Redis queue name")
				.group(SOURCE_GROUP)
				.displayName("Redis queue name "))

			.withKey(nexlaKey(DATE_FORMAT, ConfigDef.Type.STRING, "yyyy-MM-dd")
				.importance(LOW)
				.documentation("DateTime var format")
				.group(SOURCE_GROUP)
				.displayName("DateTime var format"))

			.withKey(nexlaKey(DATE_TIME_UNIT, ConfigDef.Type.STRING, "dd")
				.importance(LOW)
				.documentation("DateTime add/sub time unit")
				.group(SOURCE_GROUP)
				.displayName("DateTime add/sub time unit"))

			.withKey(nexlaKey(RETRY_DELAY, ConfigDef.Type.INT, DEFAULT_RETRY_DELAY)
				.importance(LOW)
				.documentation("Delays between retries on empty result")
				.group(SOURCE_GROUP)
				.displayName("Delays between retries on empty result"))

			.withKey(nexlaKey(RETRY_THRESHOLD_NUMBER, ConfigDef.Type.INT, DEFAULT_RETRY_THRESHOLD)
				.importance(LOW)
				.documentation("Retry Threshold on empty result. 0 for no retries")
				.group(SOURCE_GROUP)
				.displayName("Retry Threshold on empty result. 0 for no retries"))

			.withKey(nexlaKey(REQUEST_PARALLELISM_COUNT, ConfigDef.Type.INT, MINIMAL_PARALLELISM_COUNT)
				.validator((name, value) -> {
					int parallelism = Integer.parseInt(value.toString());
					if (parallelism < 1) {
						throw new ConfigException(REQUEST_PARALLELISM_COUNT + " should be greater than 0");
					}
				})
				.importance(LOW)
				.documentation("Number of requests to be sent at once")
				.group(SOURCE_GROUP)
				.displayName("Request parallelism"))

			.withKey(nexlaKey(REDIS_CREDS_ENC, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Redis Creds Encrypted for enabling iteration via datamaps ")
				.group(SOURCE_GROUP)
				.displayName("URL details template"))

			.withKey(nexlaKey(REDIS_CREDS_ENC_IV, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Redis Creds Encrypted IV for enabling iteration via datamaps ")
				.group(SOURCE_GROUP)
				.displayName("URL details template"))

			.withKey(nexlaKey(CHARSET_DETECTION_CONFIDENCE_THRESHOLD, ConfigDef.Type.INT, null)
				.importance(LOW)
				.documentation("Confidence threshold for charset detection. 0 for no detection")
				.group(SOURCE_GROUP)
				.displayName("Confidence threshold for charset detection. 0 for no detection"))

			.withKey(nexlaKey(SOAP_PARAMS, ConfigDef.Type.STRING, null)
				.documentation("Parameters to fill SOAP request")
				.group(SOURCE_GROUP)
				.displayName("Parameters to fill SOAP request"));

		return SoapWsdlConfig.addCallerParameters(sinkConfig);
	}

}
