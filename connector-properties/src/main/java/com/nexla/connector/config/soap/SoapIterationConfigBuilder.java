package com.nexla.connector.config.soap;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nexla.connector.config.rest.RestAuthConfig;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.ConfigException;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.nexla.connector.config.rest.IterationType.DATA_MAP_KEY_QUEUE;
import static com.nexla.connector.config.rest.IterationType.STATIC_URL;
import static com.nexla.connector.config.rest.RestIterationConfig.ITERATION_TYPE;
import static com.nexla.connector.properties.RestConfigAccessor.REST_ITERATIONS_JSON;
import static java.util.stream.Collectors.toList;

@AllArgsConstructor
public class SoapIterationConfigBuilder {

	private static final TypeReference<List<Map<String, Object>>> MAP_TYPE_REFERENCE = new TypeReference<List<Map<String, Object>>>() {
	};

	private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

	private final Integer sourceId;
	private final String credentialsDecryptKey;
	private final boolean unitTest;
	private final RestAuthConfig authConfig;
	private final Integer charsetDetectionThreshold;

	public List<SoapIterationConfig> buildConfigs(Map<String, Object> originals) {
		List<SoapIterationConfig> result = initRestIterationConfigs(originals);
		return validateKeyUniqueness(result);
	}

	@SneakyThrows
	private List<SoapIterationConfig> initRestIterationConfigs(Map<String, Object> originals) {

		Object restIterationsJson = originals.get(REST_ITERATIONS_JSON);

		if (restIterationsJson != null) {

			List<Map<String, Object>> data = restIterationsJson instanceof String
				? OBJECT_MAPPER.readValue(restIterationsJson.toString(), MAP_TYPE_REFERENCE)
				: (List<Map<String, Object>>) restIterationsJson;

			List<Map<String, String>> config = data.stream()
				.map(e -> EntryStream.of(e).mapValues(v -> (String) (v != null ? v.toString() : v)).toMap())
				.collect(toList());

			return config
				.stream()
				.map(callerConfigMap -> new SoapIterationConfig(
					callerConfigMap,
					credentialsDecryptKey,
					unitTest,
					sourceId,
					charsetDetectionThreshold,
					authConfig))
				.collect(toList());
		} else { // TODO remove when UI supports Rest Iterations
			if (DATA_MAP_KEY_QUEUE.property.equals(originals.get(ITERATION_TYPE))) {
				return createDataMapStaticPair(originals);
			} else {
				originals.put(SoapIterationConfig.KEY, SoapIterationConfig.KEY);
				return Collections.singletonList(
					new SoapIterationConfig(
						originals,
						credentialsDecryptKey,
						unitTest,
						sourceId,
						charsetDetectionThreshold,
						authConfig
					)
				);
			}
		}
	}

	private List<SoapIterationConfig> createDataMapStaticPair(Map<String, Object> staticConfig) {
		HashMap<String, Object> mapConfig = Maps.newHashMap(staticConfig);
		mapConfig.put(SoapIterationConfig.KEY, "map");
		mapConfig.put(ITERATION_TYPE, DATA_MAP_KEY_QUEUE.property);

		staticConfig.put(SoapIterationConfig.KEY, SoapIterationConfig.KEY);
		staticConfig.put(ITERATION_TYPE, STATIC_URL.property);

		SoapIterationConfig dataMapIteration = new SoapIterationConfig(
			mapConfig,
			credentialsDecryptKey,
			unitTest,
			sourceId,
			charsetDetectionThreshold,
			authConfig
		);
		SoapIterationConfig staticMapIteration = new SoapIterationConfig(
			staticConfig,
			credentialsDecryptKey,
			unitTest,
			sourceId,
			charsetDetectionThreshold,
			authConfig
		);
		return Lists.newArrayList(dataMapIteration, staticMapIteration);
	}

	private List<SoapIterationConfig> validateKeyUniqueness(List<SoapIterationConfig> iterations) {
		int uniqueKeysNum = StreamEx.of(iterations)
			.map(s -> s.key)
			.toSet()
			.size();

		if (uniqueKeysNum != iterations.size()) {
			throw new ConfigException("Keys are not unique among iterations");
		}

		return iterations;
	}

}
