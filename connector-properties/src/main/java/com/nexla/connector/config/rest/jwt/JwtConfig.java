package com.nexla.connector.config.rest.jwt;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.common.time.VarUtils;
import com.nexla.connector.config.rest.RestAuthConfig;

import java.util.Map;
import java.util.Optional;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.connector.config.rest.RestAuthConfig.*;
import static java.util.Collections.emptyMap;
import static java.util.Optional.ofNullable;

public class JwtConfig {

	public final String url;
	public final Boolean skipUrlEncoding;
	public final Optional<VarUtils.VarInfo> body;
	public final String contentType;
	public final String responseFormat;
	public final String responseTokenPath;
	public final String secret;
	public final Map<String, Object> extraClaims;
	public final Optional<String> responseTokenTypePath;
	public final TokenIncludeMode tokenIncludeMode;
	public final Optional<String> tokenUrlParameter;
	public final Optional<String> tokenHeader;
	public final Optional<String> tokenHeaderType;
	public final Optional<String> issuerClaim;
	public final Optional<String> audienceClaim;
	public final Optional<Integer> expirationSecClaim;
	public final Optional<String> scopeClaim;
	public final String algorithm;

	public JwtConfig(RestAuthConfig config) {
		this.url = config.getString(JWT_HTTP_URL);
		this.skipUrlEncoding = config.getBoolean(SKIP_URL_ENCODING);
		this.body = opt(config.getString(JWT_HTTP_BODY)).map(VarUtils::processStringWithVars);
		this.contentType = config.getString(JWT_CONTENT_TYPE);
		this.responseFormat = config.getString(JWT_RESPONSE_FORMAT);
		this.responseTokenPath = config.getString(JWT_RESPONSE_TOKEN_PATH);
		this.responseTokenTypePath = opt(config.getString(JWT_RESPONSE_TOKEN_TYPE_PATH));
		this.tokenIncludeMode = TokenIncludeMode.fromProperty(config.getString(JWT_TOKEN_INCLUDE_MODE));
		this.tokenUrlParameter = opt(config.getString(JWT_TOKEN_URL_PARAMETER));
		this.tokenHeaderType = opt(config.getString(JWT_TOKEN_HEADER_TYPE));
		this.tokenHeader = opt(config.getString(JWT_TOKEN_HEADER));
		this.secret = config.getString(JWT_TOKEN_SECRET);
		this.extraClaims = opt(config.getString(JWT_CLAIM_EXTRA_JSON))
			.map(JsonUtils::jsonToMap)
			.orElse(emptyMap());
		this.issuerClaim = opt(config.getString(JWT_CLAIM_ISSUER));
		this.audienceClaim = opt(config.getString(JWT_CLAIM_AUDIENCE));
		this.expirationSecClaim = ofNullable(config.getInt(JWT_CLAIM_EXPIRATION_SEC));
		this.scopeClaim = opt(config.getString(JWT_CLAIM_SCOPE));
		this.algorithm = config.getString(JWT_ALGORITHM);
	}

}
