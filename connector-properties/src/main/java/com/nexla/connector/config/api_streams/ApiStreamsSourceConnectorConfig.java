package com.nexla.connector.config.api_streams;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SourceConnectorConfig;
import com.nexla.connector.config.rest.RestAuthConfig;
import com.nexla.connector.config.rest.RestIterationConfigBuilder;
import one.util.streamex.EntryStream;
import org.apache.kafka.common.config.ConfigDef;

import java.util.Map;
import java.util.Optional;
import java.util.function.Supplier;

import static com.nexla.common.ConfigUtils.optBoolean;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.common.parse.ParserConfigs.DEFAULT_CHARSET_DETECT_THRESHOLD;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.api_streams.properties.ApiStreamsConfigAccessor.FEEDS;
import static com.nexla.connector.config.api_streams.properties.ApiStreamsConfigAccessor.JWT_FILTER_KEY;
import static com.nexla.connector.config.api_streams.properties.ApiStreamsConfigAccessor.*;
import static com.nexla.connector.config.rest.RestSourceConnectorConfig.RUN_ONCE;
import static com.nexla.connector.config.rest.RestSourceConnectorConfig.SINGLE_SCHEMA;
import static org.apache.kafka.common.config.ConfigDef.Importance.LOW;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class ApiStreamsSourceConnectorConfig extends SourceConnectorConfig {

    public static final String SOURCE_GROUP = "";
    public final CommonEltStreamConfig commonEltStreamConfig;
    public final Map<String, BaseEltStreamConfig> streamConfigs;
    public final RestAuthConfig authConfig;
    public final boolean runOnce;
    public final boolean singleSchema;

    public ApiStreamsSourceConnectorConfig(Map<String, String> originals) {
        this(originals, Optional.empty(), Optional.empty());
    }

    public ApiStreamsSourceConnectorConfig(Map<String, String> originals,
                                           Optional<RestAuthConfig> restAuthConfig,
                                           Optional<CommonEltStreamConfig> commonEltStreamConfig) {
        super(configDef(), originals);

        Supplier<Map<String, String>> authConfigMapSp = () -> EntryStream.of(originals)
                .filterKeys(e -> e.startsWith(JWT_FILTER_KEY))
                .append(getCreds(decryptKey, credsEnc, credsEncIv))
                .toMap();

        Map<String, String> authMap = unitTest ? originals : authConfigMapSp.get();
        this.authConfig = restAuthConfig.orElse(new RestAuthConfig(authMap, credsId));

        Map<String, Object> commonConfig = Optional.ofNullable(originals.get(COMMON_CONFIG_JSON))
          .map(JsonUtils::jsonToMap)
          .orElse(Map.of());

        this.commonEltStreamConfig = commonEltStreamConfig.orElse(new CommonEltStreamConfig(commonConfig));

        RestIterationConfigBuilder restIterationConfigBuilder = new RestIterationConfigBuilder(
                sourceId, decryptKey, unitTest, authConfig, DEFAULT_CHARSET_DETECT_THRESHOLD);
        EltStreamsConfigBuilder eltStreamsConfigBuilder = new EltStreamsConfigBuilder(restIterationConfigBuilder);

        this.streamConfigs = eltStreamsConfigBuilder.initStreamConfigs((Map) originals);
        this.runOnce = getBoolean(RUN_ONCE);
        this.singleSchema = optBoolean(getBoolean(SINGLE_SCHEMA)).orElse(true);
    }

    public static NexlaConfigDef configDef() {

        NexlaConfigDef baseConfig = sourceConfigDef();
        baseConfig.configKeys().remove(RESET_OFFSETS_ON_RESTART_CONFIG);

        return new NexlaConfigDef(baseConfig)
                .withKey(nexlaKey(COMMON_CONFIG_JSON, STRING, null)
                        .documentation("Common configuration JSON")
                        .displayName("Common configuration JSON"))
                .withKey(nexlaKey(FEEDS, STRING, null)
                        .documentation("Feeds config JSON")
                        .displayName("Feeds config JSON"))

                .withKey(nexlaKey(RUN_ONCE, ConfigDef.Type.BOOLEAN, false)
                        .documentation("Run once")
                        .group(SOURCE_GROUP)
                        .displayName("Run once"))

                .withKey(nexlaKey(SINGLE_SCHEMA, ConfigDef.Type.BOOLEAN, true)
                        .documentation("Single schema")
                        .group(SOURCE_GROUP)
                        .displayName("Single schema"))

                .withKey(nexlaKey(RESET_OFFSETS_ON_RESTART_CONFIG, ConfigDef.Type.BOOLEAN, false)
                        .importance(LOW)
                        .documentation("Reset offsets on restart")
                        .group(CONNECTOR_GROUP)
                        .displayName("Reset offsets on restart"))

                .withKey(nexlaKey(REDIS_CREDS_ENC, STRING, null)
                        .documentation("Redis Creds enc")
                        .group(CONNECTOR_GROUP)
                        .maskValue()
                        .displayName("Redis Creds enc"))

                .withKey(nexlaKey(REDIS_CREDS_ENC_IV, STRING, null)
                        .documentation("Redis Creds enc IV")
                        .group(CONNECTOR_GROUP)
                        .maskValue()
                        .displayName("Redis Creds enc IV"));

    }
}
