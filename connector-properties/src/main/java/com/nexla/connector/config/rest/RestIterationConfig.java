package com.nexla.connector.config.rest;

import com.bazaarvoice.jolt.JsonUtils;
import com.beust.jcommander.internal.Lists;
import com.fasterxml.jackson.core.type.TypeReference;
import com.nexla.common.parse.ParserConfigs;
import com.nexla.common.time.EpochDateTimeFormatter;
import com.nexla.common.time.NexlaTimeUnit;
import com.nexla.common.time.VarUtils;
import com.nexla.common.time.VarUtils.VarInfo;
import com.nexla.connector.config.AbstractNoLoggingConfig;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SourceConnectorConfig;
import com.nexla.connector.config.redis.RedisAuthConfig;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.common.config.ConfigException;
import org.springframework.http.HttpMethod;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.ConfigUtils.*;
import static com.nexla.common.ConnectionType.REST;
import static com.nexla.common.NexlaConstants.MAP_ID;
import static com.nexla.common.NexlaConstants.REDIS_CREDS_ENC;
import static com.nexla.common.NexlaConstants.REDIS_CREDS_ENC_IV;
import static com.nexla.common.NexlaNamingUtils.redisSourceQueueName;
import static com.nexla.common.parse.ParserConfigs.SchemaDetection.SCHEMA_DETECTION_ONCE;
import static com.nexla.connector.config.BaseConnectorConfig.DATE_FORMAT;
import static com.nexla.connector.config.BaseConnectorConfig.DATE_TIME_UNIT;
import static com.nexla.connector.config.BaseConnectorConfig.require;
import static com.nexla.connector.config.BaseConnectorConfig.requireOneOf;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.rest.IterationType.STATIC_URL;
import static com.nexla.connector.config.rest.RestSourceConnectorConfig.SOURCE_GROUP;
import static com.nexla.connector.properties.RestConfigAccessor.*;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Collections.emptySet;
import static java.util.Optional.*;
import static org.apache.kafka.common.config.ConfigDef.Importance.LOW;
import static org.apache.kafka.common.config.ConfigDef.Type.BOOLEAN;
import static org.apache.kafka.common.config.ConfigDef.Type.INT;
import static org.apache.kafka.common.config.ConfigDef.Type.LIST;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class RestIterationConfig extends AbstractNoLoggingConfig {

	public static final TypeReference<List<String>> LIST_TYPE_REFERENCE = new TypeReference<List<String>>() {
	};

	public static final int DEFAULT_RETRY_THRESHOLD = 0;
	public static final int DEFAULT_RETRY_DELAY = 60000;
	public static final int MINIMAL_PARALLELISM_COUNT = 1;
	public static final String REDIS_QUEUE_NAME = "redis.queue.name";
	public static final String NOT_RETRIABLE_CODES = "not.retriable.codes";
	public static final String REQUEST_PARALLELISM_COUNT = "request.parallelism.count";
	public static final String ITERATION_TYPE = "iteration.type";
	public static final String KEY = "key";
	public static final String RESPONSE_NEXT_TOKEN_DATA_PATH = "response.next.token.data.path";
	public static final String RESPONSE_NEXT_URL_DATA_PATH = "response.next.url.data.path";
	public static final String PAGE_PARAM_NAME = "page.param.name";
	public static final String RESPONSE_TOTAL_PAGES_PATH = "response.total.pages.path";
	public static final String START_PAGE_FROM = "start.page.from";
	public static final String START_OFFSET_FROM = "start.offset.from";
	public static final String REST_SINK_MODE = "rest.sink.mode";
	public static final String SKIP_VARIABLES_REPLACEMENT = "skip.var.replacement";
	public static final String RESPONSE_NEXT_TOKEN_HEADER = "response.next.token.header";
	public static final String RESPONSE_NEXT_TOKEN_STOP_VALUE = "response.next.token.stop.value";
	public static final String GRAPHQL_CURSOR_VARIABLE_NAME = "graphql.cursor.name";
	public static final String GRAPHQL_CURSOR_PATH = "response.graphql.cursor.path";
	public static final String GRAPHQL_CURSOR_TEMPLATE = "graphql.cursor.template";
	public static final String GRAPHQL_PAGE_VARIABLE_NAME = "graphql.page.name";
	public static final String GRAPHQL_PAGE_START = "graphql.page.start";
	public static final String GRAPHQL_PAGE_LIMIT = "graphql.page.limit";
	public static final String GRAPHQL_QUERY = BODY_TEMPLATE;
	public static final String ADD_HEADERS_TO_META = "add.headers.to.meta";
	public static final String RESULTS_PASS_THROUGH = "results.pass.through";

	public static final String END_PAGE_TO = "end.page.to";
	public static final String END_OFFSET_TO = "end.offset.to";
	public static final String END_ID_TO = "end.id.to";
	public static final String END_TOKEN_TO = "end.token.to";
	public static final String END_URL_TO = "end.url.to";
	public static final String PARAMS_IN_BODY = "params.in.body";

	public static final String ASYNC_ITERATION_PATH = "async.iteration.path";
	public static final String ASYNC_ITERATION_VALUE = "async.iteration.value";
	public static final String ASYNC_ITERATION_WAIT_RETRY_MS = "async.iteration.wait.ms";
	public static final String ASYNC_ITERATION_WAIT_RETRY_NUM = "async.iteration.retry.num";
	public static final String FILE_RESPONSE_FORMAT = "file.response.format";
	public static final String ASYNC_ITERATION_STOP_PATH = "async.iteration.stop.path";
	public static final String ASYNC_ITERATION_STOP_VALUE = "async.iteration.stop.value";
	public static final String SKIP_EMPTY_HEADERS = "skip.empty.headers";

	public static final String MODE = "mode";
	public static final String PAGING_MODE = "paging";
	public static final String LINK_HEADER_MODE = "link-header";

	public static final String CHARSET_DETECTION_CONFIDENCE_THRESHOLD = ParserConfigs.CHARSET_DETECTION_CONFIDENCE_THRESHOLD;
	public static final String NO_MIME_VALUE = "null";
	public static final String BATCH_SIZE = "batch.size";
	public static final String SUPPORT_DUPLICATE_QUERY_PARAMS = "support.duplicate.query.params";
	public static final String NO_AUTH_ITERATION = "no.auth.iteration";
	public static final String DATA_CREDENTIALS_ID = "data.credentials.id";
	public static final String SURFACE_ERROR_NOTIFICATIONS = "surface.error.notifications";
	public static final String OVERRIDE_WITH_CHARSET = "override.with.charset";
	public static final String CODE_CONTAINER_ID = "code.container.id";

	//	public final List<String> datasetColumns; // TODO
	public final boolean paramIdInclusive;
	public final Optional<String> responseNextUrlDataPath;
	public final Optional<String> paramPageSize;
	public final Optional<String> paramOffset;
	public final String queueName;
	public final Boolean createQueue;
	public final Integer redisMapId;
	public final Boolean paramsInBody;
	public final Boolean skipUrlEncoding;
	public final RestHeaders restHeaders;
	public final boolean defaultHeadersFallback;
	public boolean schemaDetectionOnce = false;
	public Optional<VarInfo> pagingHorizontalUrl;
	public Optional<String> urlTemplateInitRequest;
	public Optional<String> paramIdMacro;
	public Optional<String> pageExpectedRowsMacro;
	public Optional<String> paramOffsetMacro;
	public Optional<String> paramPageSizeMacro;
	public final Boolean addHeadersToMeta;

	public String graphqlCursorName;
	public String graphqlCursorResponsePath;
	public Optional<String> graphqlCursorTemplate;
	public String graphqlPageVariableName;
	public Optional<Integer> graphqlPageStart;
	public Optional<Integer> graphqlPageLimit;
	public Optional<String> responseNextTokenHeader;
	public Optional<String> responseNextTokenStopValue;
	public Optional<String> parseAsFileExtension;
	public Optional<Integer> startMessageOffset;

	public boolean passThroughResults;

	public String mode;
	public String pageParamName;
	public long startPageFrom;
	public Optional<Long> endPageTo;
	public Optional<Long> endIncrementingIdTo;
	public Optional<String> endStringIdTo;
	public Optional<String> endTokenTo;
	public Optional<String> endUrlTo;
	public Optional<Integer> endMessageOffset;

	public Optional<Long> startIncrementingIdFrom;
	public Optional<String> startStringIdFrom;
	public final String paramId;
	public final Optional<String> responseId;

	public final Optional<String> responseNextTokenDataPath;

	public Optional<String> asyncIterationPath;
	public Optional<String> asyncIterationValue;
	public Optional<String> asyncIterationStopPath;
	public Optional<String> asyncIterationStopValue;
	public Integer asyncWaitRetryMs;
	public Integer asyncWaitRetryNum;

	public final String responseDataPath;
	public final Optional<String> responseTotalPagesPath;
	public final Optional<String> responseDataPathAdditional;
	public final Optional<VarInfo> responseDataAdditional;
	public final Optional<String> responseFormat;
	public final Optional<Integer> pageExpectedRows;
	public final Optional<String> iterationStopCondition;
	public final Boolean pageValueIsNumber;

	public final int requestParallelismCount;
	public RedisAuthConfig redisAuthConfig;

	public final IterationType iterationType;

	public final String credentialsDecryptKey;
	public final Boolean unitTest;
	public final String key;
	public final Integer sourceId;
	public final Optional<EpochDateTimeFormatter> dateFormat;
	public final Optional<NexlaTimeUnit> dateTimeUnit;

	public final RestAuthConfig authConfig;
	public final HttpMethod method;
	public final VarInfo url;
	public final Optional<VarInfo> body;
	public final int retryNum;
	public final int retryDelay;
	public final int charsetDetectionThreshold;
	public final List<Integer> notRetryCodes;
	public Optional<Boolean> ignoreError;
	public final Optional<Integer> batchSize;
	public Optional<String> responseData;
	public boolean supportDuplicateQueryParams;
	public final boolean noAuthIteration;
	public final Optional<Integer> dataCredentialsId;
	public final boolean surfaceNotifications;
	public final Optional<String> overrideWithCharset;
	public Optional<Integer> codeContainerId;

	/**
	 * Enables advanced file mode, which allows to have the same file config for iteration as for the file source.
	 */
	public boolean advancedFileMode = false;

	private boolean replaceQueryParams = false;

	public RestIterationConfig(
		Map<String, ?> originals,
		String credentialsDecryptKey,
		Boolean unitTest,
		Integer sourceId,
		Integer charsetDetectionThreshold,
		RestAuthConfig authConfig
	) {
		super(configDef(), originals);
		this.key = getString(KEY);

		this.unitTest = unitTest;
		this.credentialsDecryptKey = credentialsDecryptKey;
		this.sourceId = sourceId;

//		this.datasetColumns = getList(DATASET_COLUMNS);

		this.paramIdInclusive = getBoolean(PARAM_ID_INCLUSIVE);

		this.paramId = getString(PARAM_ID);
		this.paramPageSize = opt(getString(PARAM_PAGE_SIZE));
		this.paramOffset = opt(getString(PARAM_OFFSET));
		this.dataCredentialsId = optInt(getInt(DATA_CREDENTIALS_ID));
		this.responseId = opt(getString(RESPONSE_ID_FIELD_NAME));
		this.noAuthIteration = getBoolean(NO_AUTH_ITERATION);
		this.authConfig = authConfig;
		this.method = HttpMethod.valueOf(getString(METHOD).toUpperCase());

		Boolean skipVarReplacement = getBoolean(SKIP_VARIABLES_REPLACEMENT);
		if (skipVarReplacement) {
			this.url = opt(getString(URL_TEMPLATE)).map(x -> new VarInfo(x, emptySet(), emptyMap())).orElse(null);
			this.body = opt(getString(BODY_TEMPLATE)).map(x -> new VarInfo(x, emptySet(), emptyMap()));
		} else {
			this.url = opt(getString(URL_TEMPLATE)).map(VarUtils::processStringWithVars).orElse(null);
			this.body = opt(getString(BODY_TEMPLATE)).map(VarUtils::processStringWithVars);
		}
		Optional<String> contentType = opt(getString(CONTENT_TYPE)).filter(x -> !NO_MIME_VALUE.equals(x));
		Optional<String> acceptHeader = opt(getString(ACCEPT_HEADER)).filter(x -> !NO_MIME_VALUE.equals(x));
		this.defaultHeadersFallback = getBoolean(DEFAULT_HEADERS_FALLBACK);

		Map<String, String> requestHeaders = parseParametersToMap(getList(REQUEST_HEADERS));

		this.restHeaders = new RestHeaders(requestHeaders, contentType, acceptHeader, opt(getString(SKIP_EMPTY_HEADERS)).map(Boolean::parseBoolean).orElse(false));

		this.responseDataPath = getString(RESPONSE_DATA_PATH);
		this.responseDataPathAdditional = getResponseDataPathAdditional();
		this.responseDataAdditional = opt(getString(RESPONSE_DATA_ADDITIONAL)).map(VarUtils::processStringWithVars);
		this.responseNextTokenDataPath = opt(getString(RESPONSE_NEXT_TOKEN_DATA_PATH));
		this.responseNextUrlDataPath = opt(getString(RESPONSE_NEXT_URL_DATA_PATH));
		this.responseTotalPagesPath = opt(getString(RESPONSE_TOTAL_PAGES_PATH));
		this.responseFormat = opt(getString(RESPONSE_FORMAT));
		this.pageExpectedRows = ofNullable(getInt(PAGE_EXPECTED_ROWS));
		this.iterationStopCondition = ofNullable(getString(ITERATION_STOP_CONDITION)).map(String::toLowerCase);
		this.pageValueIsNumber = getBoolean(PAGE_VALUE_IS_NUMBER);
		this.requestParallelismCount = getInt(REQUEST_PARALLELISM_COUNT);
		this.addHeadersToMeta = getBoolean(ADD_HEADERS_TO_META);

		this.redisMapId = getInt(MAP_ID);
		if (redisMapId != null) {
			this.redisAuthConfig = unitTest ? new RedisAuthConfig((Map) originals, null) : RedisAuthConfig.useNexlaRedisCluster();
		}

		Optional<String> redisQueueName = opt(getString(REDIS_QUEUE_NAME));
		this.queueName = redisQueueName
			.orElseGet(() -> {
				if (redisMapId != null) {
					return redisSourceQueueName(REST, sourceId, redisMapId, of(key));
				} else {
					return null;
				}
			});
		this.createQueue = !redisQueueName.isPresent();
		this.notRetryCodes = StreamEx.of(getList(NOT_RETRIABLE_CODES))
			.map(Integer::parseInt)
			.toList();
		this.ignoreError = Optional.empty();
		this.iterationType = IterationType.fromProperty(getString(ITERATION_TYPE));
		this.dateFormat = opt(getString(DATE_FORMAT)).map(EpochDateTimeFormatter::new);
		this.dateTimeUnit = opt(getString(DATE_TIME_UNIT)).map(NexlaTimeUnit::findByPattern);
		this.retryNum = getInt(RETRY_THRESHOLD_NUMBER);
		this.retryDelay = getInt(RETRY_DELAY);
		this.charsetDetectionThreshold = ofNullable(getInt(CHARSET_DETECTION_CONFIDENCE_THRESHOLD))
			.orElse(charsetDetectionThreshold);
		this.paramsInBody = getBoolean(PARAMS_IN_BODY);
		this.skipUrlEncoding = getBoolean(SKIP_URL_ENCODING);
		this.passThroughResults = optBoolean(getBoolean(RESULTS_PASS_THROUGH)).orElse(false);
		switch (iterationType) {
			case PAGING_INCREMENTING_OFFSET:
				this.startMessageOffset = opt(getString(START_OFFSET_FROM)).map(Integer::parseInt);
				this.endMessageOffset = opt(getString(END_OFFSET_TO)).map(Integer::parseInt);
				this.paramOffsetMacro = opt(getString(PARAM_OFFSET_MACRO));
				this.paramPageSizeMacro = opt(getString(PARAM_PAGE_SIZE_MACRO));
				this.pageExpectedRowsMacro = opt(getString(PAGE_EXPECTED_ROWS_MACRO));
				if (this.paramOffsetMacro.isEmpty() && this.paramPageSizeMacro.isEmpty()) {
					require(PARAM_PAGE_SIZE, paramPageSize);
				}
				if (paramOffsetMacro.isPresent() || paramPageSizeMacro.isPresent()) {
					require(PAGE_EXPECTED_ROWS, pageExpectedRows);
				}
				break;
			case PAGING_INCREMENTING:
				this.startPageFrom = getLong(START_PAGE_FROM);
				this.endPageTo = opt(getString(END_PAGE_TO)).map(Long::parseLong);
				this.paramIdMacro = opt(getString(PARAM_ID_MACRO));
				this.pageExpectedRowsMacro = opt(getInt(PAGE_EXPECTED_ROWS_MACRO));
				requireOneOf(PARAM_ID, ofNullable(paramId), PARAM_ID_MACRO, paramIdMacro);
				checkParamPageSize();
				if (paramIdMacro.isPresent()) {
					require(START_PAGE_FROM, startPageFrom);
				}
				break;
			case RESPONSE_ID_NUMBER:
				require(RESPONSE_ID_FIELD_NAME, responseId);
				this.paramIdMacro = opt(getString(PARAM_ID_MACRO));
				requireOneOf(PARAM_ID, ofNullable(paramId), PARAM_ID_MACRO, paramIdMacro);
				checkParamPageSize();
				this.startIncrementingIdFrom = opt(getString(START_ID_FROM)).map(Long::parseLong);
				this.endIncrementingIdTo = opt(getString(END_ID_TO)).map(Long::parseLong);
				if (paramIdMacro.isPresent()) {
					require(START_ID_FROM, startIncrementingIdFrom);
				}
				break;
			case RESPONSE_ID_STRING:
				require(RESPONSE_ID_FIELD_NAME, responseId);
				this.paramIdMacro = opt(getString(PARAM_ID_MACRO));
				requireOneOf(PARAM_ID, ofNullable(paramId), PARAM_ID_MACRO, paramIdMacro);
				checkParamPageSize();
				this.startStringIdFrom = opt(getString(START_ID_FROM));
				this.endStringIdTo = opt(getString(END_ID_TO));
				if (paramIdMacro.isPresent()) {
					require(START_ID_FROM, startStringIdFrom);
				}
				break;
			case PAGING_NEXT_TOKEN:
				require(RESPONSE_NEXT_TOKEN_DATA_PATH, responseNextTokenDataPath);
				this.endTokenTo = opt(getString(END_TOKEN_TO));
				this.paramIdMacro = opt(getString(PARAM_ID_MACRO));
				this.urlTemplateInitRequest = opt(getString(URL_TEMPLATE_INIT_REQUEST));
				requireOneOf(PARAM_ID, ofNullable(paramId), PARAM_ID_MACRO, paramIdMacro);
				break;
			case PAGING_NEXT_URL:
				require(RESPONSE_NEXT_URL_DATA_PATH, responseNextUrlDataPath);
				this.endUrlTo = opt(getString(END_URL_TO));
				break;
			case DATA_MAP_KEY_QUEUE:
				require(MAP_ID, redisMapId);
				break;
			case ASYNC_ITERATION:
				this.asyncIterationPath = opt(getString(ASYNC_ITERATION_PATH));
				this.asyncIterationValue = opt(getString(ASYNC_ITERATION_VALUE));
				this.asyncWaitRetryMs = getInt(ASYNC_ITERATION_WAIT_RETRY_MS);
				this.asyncWaitRetryNum = getInt(ASYNC_ITERATION_WAIT_RETRY_NUM);
				this.asyncIterationStopPath = opt(getString(ASYNC_ITERATION_STOP_PATH));
				this.asyncIterationStopValue = opt(getString(ASYNC_ITERATION_STOP_VALUE));
				break;
			case BODY_AS_FILE_ITERATION:
				this.parseAsFileExtension = opt(getString(FILE_RESPONSE_FORMAT));
				this.responseNextTokenHeader = opt(getString(RESPONSE_NEXT_TOKEN_HEADER));
				this.responseNextTokenStopValue = opt(getString(RESPONSE_NEXT_TOKEN_STOP_VALUE));
				this.schemaDetectionOnce = getBoolean(SCHEMA_DETECTION_ONCE);
				this.mode = opt(getString(MODE)).orElse("header");

				this.pageParamName = getString(PAGE_PARAM_NAME);
				this.startPageFrom = getLong(START_PAGE_FROM);
				this.endPageTo = opt(getString(END_PAGE_TO)).map(Long::parseLong);
				break;
			case GRAPHQL_CURSOR_ITERATION:
				require(GRAPHQL_QUERY, body);

				this.graphqlCursorName = getString(GRAPHQL_CURSOR_VARIABLE_NAME);
				this.graphqlCursorResponsePath = getString(GRAPHQL_CURSOR_PATH);
				this.graphqlCursorTemplate = opt(getString(GRAPHQL_CURSOR_TEMPLATE));
				break;
			case GRAPHQL_PAGE_ITERATION:
				require(GRAPHQL_QUERY, body);
				require(GRAPHQL_PAGE_VARIABLE_NAME, body);

				this.graphqlPageVariableName = getString(GRAPHQL_PAGE_VARIABLE_NAME);
				this.graphqlPageStart = ofNullable(getInt(GRAPHQL_PAGE_START));
				this.graphqlPageLimit = ofNullable(getInt(GRAPHQL_PAGE_LIMIT));
				break;
			case EMIT_ONCE:
				this.responseData = opt(getString(RESPONSE_DATA));
				break;
			case HORIZONTAL_ITERATION:
				this.pagingHorizontalUrl = opt(getString(PAGING_HORIZONTAL_URL)).map(VarUtils::processStringWithVars);
				break;
			case STATIC_URL:
			case LINK_HEADER:
				break;
            case CODE_CONTAINER_ITERATION:
				this.codeContainerId = optInt(getInt(CODE_CONTAINER_ID));
                break;
            default:
				throw new IllegalArgumentException(iterationType + " is not supported");
		}
		this.batchSize = optInt(getInt(BATCH_SIZE));
		this.advancedFileMode = getBoolean(BODY_AS_FILE_ADVANCED_MODE);
		this.supportDuplicateQueryParams = getBoolean(SUPPORT_DUPLICATE_QUERY_PARAMS);
		this.surfaceNotifications = getBoolean(SURFACE_ERROR_NOTIFICATIONS);
		this.overrideWithCharset = opt(getString(OVERRIDE_WITH_CHARSET));
	}

	private Optional<String> getResponseDataPathAdditional() {
		return opt(getString(RESPONSE_DATA_PATH_ADDITIONAL))
			.map(add -> {
				try {
					JsonUtils.stringToType(add, LIST_TYPE_REFERENCE);
					return add;
				} catch (Exception e) {
					return JsonUtils.toJsonString(List.of(add));
				}
			});
	}

	private void checkParamPageSize() {
		if (paramPageSize.isPresent() && !pageExpectedRows.isPresent()) {
			require(PAGE_EXPECTED_ROWS, pageExpectedRows);
		}
	}

	public static NexlaConfigDef configDef() {

		return new NexlaConfigDef()
			.withKey(nexlaKey(KEY, STRING, "KEY")
				.documentation("Rest Iteration key")
				.group(SOURCE_GROUP)
				.displayName("Rest Iteration key"))

			.withKey(nexlaKey(DATASET_COLUMNS, LIST, emptyList())
				.documentation("Dataset columns")
				.group(SOURCE_GROUP)
				.displayName("Dataset columns"))

			.withKey(nexlaKey(PARAM_ID_INCLUSIVE, BOOLEAN, true)
				.documentation("Is ID parameter inclusive?")
				.group(SOURCE_GROUP)
				.displayName("Is ID parameter inclusive?"))

			.withKey(nexlaKey(BODY_TEMPLATE, STRING, null)
				.documentation("Body template")
				.group(SOURCE_GROUP)
				.displayName("Body template"))

			.withKey(nexlaKey(START_OFFSET_FROM, STRING, null)
				.documentation("Start offset to load data from")
				.group(SOURCE_GROUP)
				.displayName("Start offset to load data from"))

			.withKey(nexlaKey(END_OFFSET_TO, STRING, null)
				.documentation("End offset to load data from")
				.group(SOURCE_GROUP)
				.displayName("End offset to load data from"))

			.withKey(nexlaKey(END_TOKEN_TO, STRING, null)
				.documentation("End token to load data from")
				.group(SOURCE_GROUP)
				.displayName("End token to load data from"))

			.withKey(nexlaKey(END_URL_TO, STRING, null)
				.documentation("End url to load data from")
				.group(SOURCE_GROUP)
				.displayName("End url to load data from"))

			.withKey(nexlaKey(PAGE_PARAM_NAME, STRING, null)
				.documentation("Page url parameter name")
				.group(SOURCE_GROUP)
				.displayName("Page url parameter name"))

			.withKey(nexlaKey(START_PAGE_FROM, ConfigDef.Type.LONG, 1)
				.documentation("Start page to load data from")
				.group(SOURCE_GROUP)
				.displayName("Start page to load data from"))

			.withKey(nexlaKey(END_PAGE_TO, STRING, null)
				.documentation("End page to load data from")
				.group(SOURCE_GROUP)
				.displayName("End page to load data from"))

			.withKey(nexlaKey(START_ID_FROM, ConfigDef.Type.STRING, null)
				.documentation("Start ID to load data from")
				.group(SOURCE_GROUP)
				.displayName("Start ID to load data from"))

			.withKey(nexlaKey(END_ID_TO, ConfigDef.Type.STRING, null)
				.documentation("End ID to load data from")
				.group(SOURCE_GROUP)
				.displayName("End ID to load data from"))

			.withKey(nexlaKey(PARAM_ID, ConfigDef.Type.STRING, null)
				.documentation("Query parameter for ID")
				.group(SOURCE_GROUP)
				.displayName("Query parameter for ID"))

			.withKey(nexlaKey(PARAM_PAGE_SIZE, ConfigDef.Type.STRING, null)
				.documentation("Query parameter for page size")
				.group(SOURCE_GROUP)
				.displayName("Query parameter for page size"))

			.withKey(nexlaKey(PARAM_PAGE_SIZE_MACRO, ConfigDef.Type.STRING, null)
				.documentation("Param page size macros. ")
				.group(SOURCE_GROUP)
				.displayName("Param page size macros"))

			.withKey(nexlaKey(PARAM_OFFSET, ConfigDef.Type.STRING, null)
				.documentation("Query parameter for offset")
				.group(SOURCE_GROUP)
				.displayName("Query parameter for offset"))

			.withKey(nexlaKey(RESPONSE_ID_FIELD_NAME, ConfigDef.Type.STRING, null)
				.documentation("Response ID field")
				.group(SOURCE_GROUP)
				.displayName("ID field"))

			.withKey(nexlaKey(RESPONSE_NEXT_TOKEN_DATA_PATH, ConfigDef.Type.STRING, null)
				.documentation("Response next token path (JsonPath or XmlPath)")
				.group(SOURCE_GROUP)
				.displayName("Response next token path"))

			.withKey(nexlaKey(RESPONSE_NEXT_TOKEN_HEADER, ConfigDef.Type.STRING, null)
				.documentation("Response next token header")
				.group(SOURCE_GROUP)
				.displayName("Response next token header"))

			.withKey(nexlaKey(RESPONSE_NEXT_TOKEN_STOP_VALUE, ConfigDef.Type.STRING, null)
					.documentation("Response next token stop value")
					.group(SOURCE_GROUP)
					.displayName("Response next token stop value"))

			.withKey(nexlaKey(RESPONSE_NEXT_URL_DATA_PATH, ConfigDef.Type.STRING, null)
				.documentation("Response next URL path (JsonPath or XmlPath)")
				.group(SOURCE_GROUP)
				.displayName("Response next URL path"))

			.withKey(nexlaKey(METHOD, ConfigDef.Type.STRING, HttpMethod.GET.name())
				.validator(SourceConnectorConfig.EnumValidator.in(HttpMethod.values()))
				.documentation("API Method")
				.group(SOURCE_GROUP)
				.displayName("API Method"))

			.withKey(nexlaKey(URL_TEMPLATE, ConfigDef.Type.STRING, null)
				.documentation("API URL")
				.group(SOURCE_GROUP)
				.displayName("API URL"))

			.withKey(nexlaKey(SKIP_URL_ENCODING, BOOLEAN, false)
				.documentation("Skip URL encoding")
				.group(SOURCE_GROUP)
				.displayName("Skip URL encoding for this iteration. Treats the value literally as-is."))

			.withKey(nexlaKey(RESPONSE_DATA, ConfigDef.Type.STRING, null)
				.documentation("Response data substitution")
				.group(SOURCE_GROUP)
				.displayName("Response data substitution"))

			.withKey(nexlaKey(RESPONSE_DATA_PATH, ConfigDef.Type.STRING, null)
				.documentation("Data path")
				.group(SOURCE_GROUP)
				.displayName("Data path as XPath or JsonPath"))

			.withKey(nexlaKey(RESPONSE_DATA_PATH_ADDITIONAL, ConfigDef.Type.STRING, null)
				.documentation("Additional data path")
				.group(SOURCE_GROUP)
				.displayName("Data path as XPath or JsonPath"))

			.withKey(nexlaKey(RESPONSE_DATA_ADDITIONAL, ConfigDef.Type.STRING, null)
					.documentation("Additional JSON data to be appended to the response")
					.group(SOURCE_GROUP)
					.displayName("Additional JSON data to be appended to the response"))

			.withKey(nexlaKey(RESPONSE_FORMAT, ConfigDef.Type.STRING, null)
				.validator(ConfigDef.ValidString.in(null, "xml", "json", "plain"))
				.documentation("Response format")
				.group(SOURCE_GROUP)
				.displayName("Response format"))

			.withKey(nexlaKey(ITERATION_TYPE, ConfigDef.Type.STRING, STATIC_URL.property)
				.validator(ConfigDef.ValidString.in(StreamEx.of(IterationType.values()).map(a -> a.property).toArray(String.class)))
				.documentation("Iteration type")
				.group(SOURCE_GROUP)
				.displayName("Iteration type"))

			.withKey(nexlaKey(PAGE_EXPECTED_ROWS, ConfigDef.Type.INT, null)
				.documentation("Expected rows on page")
				.group(SOURCE_GROUP)
				.displayName("Expected rows on page"))

			.withKey(nexlaKey(ITERATION_STOP_CONDITION, ConfigDef.Type.STRING, null)
					.documentation("REST iteration stop condition (default, norecords)")
					.group(SOURCE_GROUP)
					.displayName("REST iteration stop condition (default, norecords)"))

			.withKey(nexlaKey(PAGE_VALUE_IS_NUMBER, BOOLEAN, false)
				.documentation("Is the 'expected rows on page' value a number or a string")
				.group(SOURCE_GROUP)
				.displayName("Is the 'expected rows on page' value a number or a string"))

			.withKey(nexlaKey(MAP_ID, ConfigDef.Type.INT, null)
				.importance(LOW)
				.documentation("Map id")
				.group(SOURCE_GROUP)
				.displayName("Redis map id"))

			.withKey(nexlaKey(REDIS_QUEUE_NAME, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Redis queue name")
				.group(SOURCE_GROUP)
				.displayName("Redis queue name "))

			.withKey(nexlaKey(DATE_FORMAT, ConfigDef.Type.STRING, "yyyy-MM-dd")
				.importance(LOW)
				.documentation("DateTime var format")
				.group(SOURCE_GROUP)
				.displayName("DateTime var format"))

			.withKey(nexlaKey(DATE_TIME_UNIT, ConfigDef.Type.STRING, "dd")
				.importance(LOW)
				.documentation("DateTime add/sub time unit")
				.group(SOURCE_GROUP)
				.displayName("DateTime add/sub time unit"))

			.withKey(nexlaKey(RETRY_DELAY, ConfigDef.Type.INT, DEFAULT_RETRY_DELAY)
				.importance(LOW)
				.documentation("Delays between retries on empty result")
				.group(SOURCE_GROUP)
				.displayName("Delays between retries on empty result"))

			.withKey(nexlaKey(RETRY_THRESHOLD_NUMBER, ConfigDef.Type.INT, DEFAULT_RETRY_THRESHOLD)
				.importance(LOW)
				.documentation("Retry threshold number on empty result. 0 for no retries")
				.group(SOURCE_GROUP)
				.displayName("Retry threshold number on empty result. 0 for no retries"))

			.withKey(nexlaKey(REQUEST_PARALLELISM_COUNT, ConfigDef.Type.INT, MINIMAL_PARALLELISM_COUNT)
				.validator((name, value) -> {
					int parallelism = Integer.parseInt(value.toString());
					if (parallelism < 1) {
						throw new ConfigException(REQUEST_PARALLELISM_COUNT + " should be greater than 0");
					}
				})
				.importance(LOW)
				.documentation("Number of requests to be sent at once")
				.group(SOURCE_GROUP)
				.displayName("Request parallelism"))

			.withKey(nexlaKey(REDIS_CREDS_ENC, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Redis Creds Encrypted for enabling iteration via datamaps ")
				.group(SOURCE_GROUP)
				.displayName("URL details template"))

			.withKey(nexlaKey(REDIS_CREDS_ENC_IV, ConfigDef.Type.STRING, null)
				.importance(LOW)
				.documentation("Redis Creds Encrypted IV for enabling iteration via datamaps ")
				.group(SOURCE_GROUP)
				.displayName("URL details template"))

			.withKey(nexlaKey(CHARSET_DETECTION_CONFIDENCE_THRESHOLD, ConfigDef.Type.INT, null)
				.importance(LOW)
				.documentation("Confidence threshold for charset detection. 0 for no detection")
				.group(SOURCE_GROUP)
				.displayName("Confidence threshold for charset detection. 0 for no detection"))

			.withKey(nexlaKey(PARAMS_IN_BODY, BOOLEAN, false)
				.documentation("Include parameters in request body")
				.group(SOURCE_GROUP)
				.displayName("Include parameters in request body"))

			.withKey(nexlaKey(NOT_RETRIABLE_CODES, LIST, Lists.newArrayList("400","401", "402", "403", "404", "422"))
				.documentation("Not retry on those codes")
				.group(SOURCE_GROUP)
				.displayName("Not retry on those codes"))

			.withKey(nexlaKey(REQUEST_HEADERS, ConfigDef.Type.LIST, emptyList())
				.group(SOURCE_GROUP)
				.displayName("Headers"))

			.withKey(nexlaKey(CONTENT_TYPE, ConfigDef.Type.STRING, null)
				.documentation("Content type")
				.group(SOURCE_GROUP)
				.displayName("Content type"))

			.withKey(nexlaKey(ACCEPT_HEADER, ConfigDef.Type.STRING, null)
				.documentation("Accept header")
				.group(SOURCE_GROUP)
				.displayName("Accept header"))

			.withKey(nexlaKey(DEFAULT_HEADERS_FALLBACK, BOOLEAN, true)
				.documentation("Fallback to default headers if contentType | accept headers are not set")
				.group(SOURCE_GROUP)
				.displayName("Fallback to default headers if contentType | accept headers are not set"))

			.withKey(nexlaKey(SKIP_VARIABLES_REPLACEMENT, BOOLEAN, false)
				.documentation("Skip variables replacement for URL and body")
				.group(SOURCE_GROUP)
				.displayName("Skip variables replacement for URL and body"))

			.withKey(nexlaKey(SKIP_EMPTY_HEADERS, STRING, null)
				.documentation("Skip headers with empty value")
				.group(SOURCE_GROUP)
				.displayName("Skip headers with empty value"))

			.withKey(nexlaKey(MODE, STRING, LINK_HEADER_MODE)
				.validator(ConfigDef.ValidString.in(LINK_HEADER_MODE, PAGING_MODE))
				.documentation("Body as file iteration mode")
				.group(SOURCE_GROUP)
				.displayName("Body as file iteration mode"))

			.withKey(nexlaKey(ASYNC_ITERATION_PATH, STRING, null)
				.documentation("Async iteration path")
				.group(SOURCE_GROUP)
				.displayName("Async iteration path"))

			.withKey(nexlaKey(ASYNC_ITERATION_VALUE, STRING, null)
				.documentation("Async iteration value")
				.group(SOURCE_GROUP)
				.displayName("Async iteration value"))

			.withKey(nexlaKey(ASYNC_ITERATION_WAIT_RETRY_MS, INT, 60000)
				.documentation("Async iteration wait retry ms")
				.group(SOURCE_GROUP)
				.displayName("Async iteration wait retry ms"))

			.withKey(nexlaKey(ASYNC_ITERATION_WAIT_RETRY_NUM, INT, 60)
				.documentation("Async iteration wait retry amount")
				.group(SOURCE_GROUP)
				.displayName("Async iteration wait retry amount"))

			.withKey(nexlaKey(FILE_RESPONSE_FORMAT, STRING, null)
				.documentation("File type for Parse as File iteration: [csv, txt, json, xml]")
				.group(SOURCE_GROUP)
				.displayName("Mime type for Parse as File iteration: [csv, txt, json, xml]"))

			.withKey(nexlaKey(ASYNC_ITERATION_STOP_PATH, STRING, null)
				.documentation("Async iteration stop trigger path")
				.group(SOURCE_GROUP)
				.displayName("Async iteration stop trigger path"))

			.withKey(nexlaKey(ASYNC_ITERATION_STOP_VALUE, STRING, null)
				.documentation("Async iteration stop trigger value")
				.group(SOURCE_GROUP)
				.displayName("Async iteration stop trigger value"))

			.withKey(nexlaKey(BATCH_SIZE, INT, null)
				.documentation("Batch size")
				.group(SOURCE_GROUP)
				.displayName("Batch size"))

			.withKey(nexlaKey(GRAPHQL_CURSOR_TEMPLATE, STRING, null)
				.documentation("GraphQL cursor template for the initial iteration")
				.group(SOURCE_GROUP)
				.displayName("GraphQL cursor template for the initial iteration"))

			.withKey(nexlaKey(RESULTS_PASS_THROUGH, BOOLEAN, false)
				.documentation("Prepends results from a previous iteration step to the current step's results")
				.group(SOURCE_GROUP)
				.displayName("Result Pass Through"))

			.withKey(nexlaKey(GRAPHQL_CURSOR_VARIABLE_NAME, STRING, null)
					.documentation("GraphQL cursor variable name")
					.group(SOURCE_GROUP)
					.displayName("GraphQL cursor variable name"))

			.withKey(nexlaKey(GRAPHQL_CURSOR_PATH, STRING, null)
					.documentation("GraphQL cursor JSON path")
					.group(SOURCE_GROUP)
					.displayName("GraphQL cursor JSON path"))

			.withKey(nexlaKey(GRAPHQL_PAGE_VARIABLE_NAME, STRING, null)
					.documentation("GraphQL page variable name in GraphQL payload")
					.group(SOURCE_GROUP)
					.displayName("GraphQL page variable name in GraphQL payload"))

			.withKey(nexlaKey(GRAPHQL_PAGE_START, INT, null)
					.documentation("GraphQL iteration page start")
					.group(SOURCE_GROUP)
					.displayName("GraphQL iteration page start"))

			.withKey(nexlaKey(RESPONSE_TOTAL_PAGES_PATH, STRING, null)
					.documentation("Response total pages JSON path")
					.group(SOURCE_GROUP)
					.displayName("Response total pages JSON path"))

			.withKey(nexlaKey(GRAPHQL_PAGE_LIMIT, INT, null)
					.documentation("GraphQL iteration page limit")
					.group(SOURCE_GROUP)
					.displayName("GraphQL iteration page limit"))

			.withKey(nexlaKey(SUPPORT_DUPLICATE_QUERY_PARAMS, BOOLEAN, false)
				.documentation("Support duplicate query parameters, true by default")
				.group(SOURCE_GROUP)
				.displayName("Support duplicate query parameters"))

			.withKey(nexlaKey(PAGING_HORIZONTAL_URL, STRING, null)
				.documentation("URL to iterate horizontally")
				.group(SOURCE_GROUP)
				.displayName("URL to iterate horizontally"))

			.withKey(nexlaKey(SCHEMA_DETECTION_ONCE, ConfigDef.Type.BOOLEAN, false)
				.importance(LOW)
				.documentation("Schema detection: detect schema once")
				.group(SOURCE_GROUP)
				.displayName("Schema detection: detect schema once"))

			.withKey(nexlaKey(URL_TEMPLATE_INIT_REQUEST, STRING, null)
				.documentation("Url template for the initial request")
				.group(SOURCE_GROUP)
				.displayName("Url template for the initial request in case if initial request differs" +
							" from the iteration later on (e.g. request to retrieve a token)."))

			.withKey(nexlaKey(PARAM_ID_MACRO, STRING, null)
				.documentation("Param id macros. Used to change current current iteration on the flight")
				.group(SOURCE_GROUP)
				.displayName("Param id macros"))

			.withKey(nexlaKey(PAGE_EXPECTED_ROWS_MACRO, STRING, null)
				.documentation("Page expected rows macros. Used to change current page size on the flight")
				.group(SOURCE_GROUP)
				.displayName("Page expected rows macros"))

			.withKey(nexlaKey(PARAM_OFFSET_MACRO, STRING, null)
				.documentation("Param offset macros. Used to change current offset on the flight")
				.group(SOURCE_GROUP)
				.displayName("Param offset macros"))

			.withKey(nexlaKey(BODY_AS_FILE_ADVANCED_MODE, ConfigDef.Type.BOOLEAN, false)
				.importance(LOW)
				.documentation("Body as file: enables advanced file mode, which allows to have the same file config for iteration as for the file source.")
				.group(SOURCE_GROUP)
				.displayName("Body as file: enables advanced file mode."))

			.withKey(nexlaKey(NO_AUTH_ITERATION, BOOLEAN, false)
				.documentation("Set Auth type of iteration to NONE")
				.group(SOURCE_GROUP)
				.displayName("Set Auth type of iteration to NONE"))

			.withKey(nexlaKey(DATA_CREDENTIALS_ID, INT, null)
				.documentation("Iteration-specific data credentials ID")
				.group(SOURCE_GROUP)
				.displayName("Iteration-specific data credentials ID"))

			.withKey(nexlaKey(ADD_HEADERS_TO_META, BOOLEAN, false)
				.documentation("Add response headers to metadata on response ingestion")
				.group(SOURCE_GROUP)
				.displayName("Add response headers to metadata on response ingestion"))

			.withKey(nexlaKey(SURFACE_ERROR_NOTIFICATIONS, BOOLEAN, false)
				.documentation("Surface all error notifications")
				.group(SOURCE_GROUP)
				.displayName("Surface all error notifications"))

			.withKey(nexlaKey(OVERRIDE_WITH_CHARSET, STRING, null)
				.documentation("Force override to ingest response with given charset")
				.group(SOURCE_GROUP)
				.displayName("Surface all error notifications"))

			.withKey(nexlaKey(CODE_CONTAINER_ID, INT, null)
				.documentation("Code Container ID")
				.group(SOURCE_GROUP)
				.displayName("Code Container ID"))

			;

	}

	public boolean getReplaceQueryParams() {
		return replaceQueryParams;
	}

	public void setReplaceQueryParams(boolean replaceQueryParams) {
		this.replaceQueryParams = replaceQueryParams;
	}
}
