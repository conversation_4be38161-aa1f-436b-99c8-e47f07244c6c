package com.nexla.connector.config.big_query;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SinkConnectorConfig;
import com.nexla.connector.config.file.BigQueryAuthConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import one.util.streamex.StreamEx;

import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.List;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig.FILE_FORMAT;
import static com.nexla.connector.properties.SqlConfigAccessor.INSERT_MODE;
import static com.nexla.connector.properties.SqlConfigAccessor.PRIMARY_KEY;
import static java.util.Collections.emptyList;
import static org.apache.kafka.common.config.ConfigDef.Type.BOOLEAN;
import static org.apache.kafka.common.config.ConfigDef.Type.INT;
import static org.apache.kafka.common.config.ConfigDef.Type.LIST;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class BigQuerySinkConnectorConfig extends SinkConnectorConfig {

	public static final String LOAD_MODE = "load.mode";
	public static final String DATASET = "database";
	public static final String TABLE = "table";
	public static final String INTERMEDIATE_FORMAT = "intermediate.format";
	public static final String MAX_BAD_RECORDS = "max.bad.records";
	public static final String IGNORE_UNKNOWN = "ignore.unknown";
	public static final String ALLOW_ADDITION_RELAXATION = "allow.addition.relaxation";
	public static final String TRUNCATE_BEFORE_LOAD = "truncate.before.load";
	public static final String ID_FIELDS = "id.fields";
	public static final String ARRAY_FIELDS = "array.fields";
	public static final String PARTITIONING_COLUMN = "partitioning.column";
	public static final String CLUSTERING_COLUMNS = "clustering.columns";
	public static final String UPSERT_TEMP_TABLE = "upsert.temp.table";
	public static final String UPSERT_MERGE_QUERY = "upsert.merge.query";
	public static final String CLEANUP_TEMP_TABLES = "cleanup.temp.tables";

	public static final String BIGQ_AUTO_DETECT = "bigquery.auto.detect";

	public final BigQueryAuthConfig authConfig;
	public final String dataset;
	public final String table;
	public final IntermediateFormat intermediateFormat;
	public final Integer maxBadRecords;
	public final Boolean ignoreUnknown;
	public final Boolean allowAdditionRelaxation;
	public final Boolean truncOnLoad;
	public final Optional<WarehouseCopyFileFormat> fileFormat;
	public final InsertMode insertMode;
	public final Set<String> idFields;
	public final Set<String> arrayFields;
	public final Optional<String> partitioningColumn;
	public final List<String> clusteringingColumns;
	public final String upsertTempTable;
	public final String upsertMergeQuery;
	public final LoadMode loadMode;
	public final boolean cleanupTempTables;

	public final boolean autodetect;

	public enum IntermediateFormat {
		JSON, CSV
	}

	public enum LoadMode {
		STREAMING, BATCH
	}

	public BigQuerySinkConnectorConfig(Map<String, String> originals) {
		super(configDef(), originals);

		Map<String, String> authMap = unitTest ? originals : getCreds(decryptKey, credsEnc, credsEncIv);
		this.authConfig = new BigQueryAuthConfig(authMap, credsId);

		this.loadMode = LoadMode.valueOf(getString(LOAD_MODE).toUpperCase());

		this.dataset = getString(DATASET);
		this.table = getString(TABLE);
		this.intermediateFormat = IntermediateFormat.valueOf(getString(INTERMEDIATE_FORMAT).toUpperCase());
		this.maxBadRecords = getInt(MAX_BAD_RECORDS);
		this.ignoreUnknown = getBoolean(IGNORE_UNKNOWN);
		this.allowAdditionRelaxation = getBoolean(ALLOW_ADDITION_RELAXATION);
		this.truncOnLoad = getBoolean(TRUNCATE_BEFORE_LOAD);
		this.fileFormat = opt(getString(FILE_FORMAT))
			.map(WarehouseCopyFileFormat::fromString);
		this.insertMode = InsertMode.valueOf(getString(INSERT_MODE).toUpperCase());

		List<String> idList = getList(ID_FIELDS);
		List<String> arrayColumnsList = getList(ARRAY_FIELDS);
		List<String> primaryKeyList = getList(PRIMARY_KEY);

		this.idFields = new HashSet<>(idList.isEmpty() ? primaryKeyList : idList);
		this.arrayFields = new HashSet<>(arrayColumnsList);
		this.upsertTempTable = getString(UPSERT_TEMP_TABLE);
		this.upsertMergeQuery = getString(UPSERT_MERGE_QUERY);
		this.partitioningColumn = opt(getString(PARTITIONING_COLUMN));
		this.clusteringingColumns = getList(CLUSTERING_COLUMNS);
		this.cleanupTempTables = getBoolean(CLEANUP_TEMP_TABLES);
		this.autodetect = getBoolean(BIGQ_AUTO_DETECT);
	}

	public static NexlaConfigDef configDef() {
		return new NexlaConfigDef(sinkConfigDef())
			.withKey(nexlaKey(DATASET, STRING, null))
			.withKey(nexlaKey(TABLE, STRING, null))
			.withKey(nexlaKey(INTERMEDIATE_FORMAT, STRING, IntermediateFormat.JSON.name()))
			.withKey(nexlaKey(MAX_BAD_RECORDS, INT, 100000))
			.withKey(nexlaKey(IGNORE_UNKNOWN, BOOLEAN, true))
			.withKey(nexlaKey(ALLOW_ADDITION_RELAXATION, BOOLEAN, false))
			.withKey(nexlaKey(TRUNCATE_BEFORE_LOAD, BOOLEAN, false))
			.withKey(nexlaKey(FILE_FORMAT, STRING, null)
				.documentation("Unload format: " + StreamEx.of(WarehouseCopyFileFormat.values()).map(Enum::toString).joining(" | "))
				.displayName("Unload format"))
			.withKey(nexlaKey(INSERT_MODE, STRING, InsertMode.INSERT.toString()))
			.withKey(nexlaKey(ID_FIELDS, LIST, emptyList()))
			.withKey(nexlaKey(ARRAY_FIELDS, LIST, emptyList()))
			.withKey(nexlaKey(PRIMARY_KEY, LIST, emptyList()))
			.withKey(nexlaKey(UPSERT_TEMP_TABLE, STRING, null))
			.withKey(nexlaKey(UPSERT_MERGE_QUERY, STRING, null))
			.withKey(nexlaKey(PARTITIONING_COLUMN, STRING, null))
			.withKey(nexlaKey(CLUSTERING_COLUMNS, LIST, emptyList()))
			.withKey(nexlaKey(LOAD_MODE, STRING, LoadMode.BATCH.toString()))
			.withKey(nexlaKey(CLEANUP_TEMP_TABLES, BOOLEAN, false))
			.withKey(nexlaKey(BIGQ_AUTO_DETECT, BOOLEAN, true));
	}
}
