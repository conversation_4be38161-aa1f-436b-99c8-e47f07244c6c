package com.nexla.connector.config.documentdb.firebase;

import com.nexla.common.ConnectionType;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.file.GCPAuthConfig;
import org.apache.kafka.common.config.ConfigDef;

import java.util.Map;
import java.util.Optional;

import static com.nexla.common.NexlaConstants.CREDENTIALS_TYPE;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;

public class FirebaseAuthConfig extends GCPAuthConfig {


	public static final String JSON_CREDS = "json_creds";

	public final String jsonCreds;

	public final Optional<ConnectionType> credentialsType;

	public FirebaseAuthConfig(Map<String, ?> parsedConfig, Integer credsId) {
		super(configDef(), parsedConfig, credsId);

		this.jsonCreds = getString(JSON_CREDS);

		this.credentialsType = Optional.of(getString(CREDENTIALS_TYPE))
			.map(String::toUpperCase)
			.map(ConnectionType::fromString);
	}

	public static NexlaConfigDef configDef() {
		return new NexlaConfigDef(GCPAuthConfig.configDef())
			.withKey(nexlaKey(JSON_CREDS, ConfigDef.Type.STRING, null))
			.withKey(nexlaKey(CREDENTIALS_TYPE, ConfigDef.Type.STRING, null))
			;
	}
}
