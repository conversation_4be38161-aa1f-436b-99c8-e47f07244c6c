package com.nexla.connector.config.databricks;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.AzureAuthConfig;
import com.nexla.connector.config.file.GCPAuthConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.nexla.common.ConnectionType.*;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class DatabricksAuthConfig extends BaseAuthConfig {

    public static final String SPARK_MASTER = "spark.master";
    public static final String SPARK_APP_NAME = "spark.app.name";
    private static final String AUTH_GROUP = "auth-group";
    public static final NexlaConfigDef CONFIG_DEF = authConfigDef();

    public final String sparkMaster;
    public final String sparkAppName;

    public final Optional<AWSAuthConfig> awsAuthConfig;
    public final Optional<GCPAuthConfig> gcpAuthConfig;
    public final Optional<AzureAuthConfig> azureAuthConfig;

    public DatabricksAuthConfig(Map<String, ?> originals, Integer credsId) {
        super(CONFIG_DEF, originals, credsId);
        this.sparkMaster = getString(SPARK_MASTER);
        this.sparkAppName = getString(SPARK_APP_NAME);
        this.awsAuthConfig = credentialsType
            .filter(type -> Objects.equals(DATABRICKS_AWS, type))
                .map(type -> new AWSAuthConfig(originals, null));
        this.gcpAuthConfig = credentialsType
            .filter(type -> Objects.equals(DATABRICKS_GCP, type))
                .map(type -> new GCPAuthConfig(GCPAuthConfig.configDef(), originals, null));
        this.azureAuthConfig = credentialsType
            .filter(type -> Objects.equals(DATABRICKS_AZURE, type))
                .map(type -> new AzureAuthConfig(originals, null));
    }

    @Override
    public AWSAuthConfig asAWS() {
        return this.awsAuthConfig.get();
    }

    public AzureAuthConfig asAzure() {
        return this.azureAuthConfig.get();
    }

    public GCPAuthConfig asGCP() {
        return this.gcpAuthConfig.get();
    }

    private static NexlaConfigDef authConfigDef() {

        return baseAuthConfigDef()
                .withKey(nexlaKey(SPARK_MASTER, STRING, "local[*]")
                        .documentation("Spark master")
                        .group(AUTH_GROUP)
                        .displayName("Spark Master"))
                .withKey(nexlaKey(SPARK_APP_NAME, STRING, "nexla-spark-app")
                        .documentation("Spark App Name")
                        .group(AUTH_GROUP)
                        .displayName("Spark App Name"));
    }
}
