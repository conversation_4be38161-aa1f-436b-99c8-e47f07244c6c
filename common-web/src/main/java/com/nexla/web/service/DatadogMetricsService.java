package com.nexla.web.service;

import com.nexla.admin.client.Org;
import com.nexla.admin.client.Owner;
import com.nexla.common.ConnectionType;
import com.nexla.common.ResourceType;
import com.timgroup.statsd.Event;
import com.timgroup.statsd.NonBlockingStatsDClientBuilder;
import com.timgroup.statsd.StatsDClient;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.nexla.common.NexlaConstants.ConnectionTypeCategory.FILE;
import static com.nexla.web.service.Tags.*;

@Service
public class DatadogMetricsService {

	private static final Logger logger = LoggerFactory.getLogger(DatadogMetricsService.class);

	private final Optional<StatsDClient> statsd;

	@SneakyThrows
	public DatadogMetricsService(String host, Integer port, String env, boolean enabled) {
		List<String> defaultTags = new ArrayList<>();

		String hostname = InetAddress.getLocalHost().getHostName();
		defaultTags.add(tag(HOST_KEY, hostname));

		defaultTags.add(tag(TAG_ENV, env));
		defaultTags.add(tag(CLIENT_TAG_KEY, CLIENT_TAG_VAL));
		String[] defaultTagsParam = defaultTags.toArray(new String[0]);

		if (enabled) {
			NonBlockingStatsDClientBuilder builder = new NonBlockingStatsDClientBuilder()
				.prefix("")
				.hostname(host)
				.port(port)
				.constantTags(defaultTagsParam);

			this.statsd = Optional.of(builder.build());
		} else {
			this.statsd = Optional.empty();
		}
	}

	public void sendTimeSeriesMetric(String metricName, List<String> tags, Long metricVal) {
		if (metricName != null && metricVal != null) {
			logger.debug("Sending Datadog Gauge Metric with name {}, tags {}, value {}", metricName, tags, metricVal);
			statsd.ifPresent(client -> client.gauge(metricName, metricVal, tags.toArray(new String[0])));
		}
	}

	public void sendEvent(String title, String text, List<String> tags, Event.AlertType status) {
		Event event = Event.builder().withTitle(title).withText(text).withAlertType(status).build();
		logger.debug("Sending Datadog event with title {}, tags {}, status {}, text {}", title, tags, status.name(), text);

		statsd.ifPresent(client -> client.recordEvent(event, tags.toArray(new String[0])));
	}

	void sendCounterMetric(String metricName, List<String> tags) {
		logger.debug("Sending Datadog counter with name {}, tags {}", metricName, tags);
		statsd.ifPresent(client -> client.increment(metricName, tags.toArray(new String[0])));
	}

	public void incrementMetric(String metricName, List<String> tags, Long count) {
		logger.debug("Sending Datadog count by delta {} with name {}, tags {}", count, metricName, tags);
		if (count != null) {
			statsd.ifPresent(client -> client.count(metricName, count, tags.toArray(new String[0])));
		}
	}

	public static void addGlobalTags(
		ResourceType resourceType, Integer resourceId,
		List<String> tags, String resourceLocation,
		ConnectionType connectionType, Org org,
		Owner owner, String name,
		String format,
		String bucketName,
		String status
	) {
		tags.add(TAG_RESOURCE_ID + TAG_SEPARATOR + resourceId);
		tags.add(tag(TAG_RESOURCE_TYPE, resourceType.name()));
		tags.add(tag(TAG_STATUS, status));
		String channelName = (connectionType == null || connectionType.category == null) ? "" : connectionType.category.name();

		if (org != null) {
			tags.add(TAG_ORG_ID + TAG_SEPARATOR + org.getId());
			tags.add(tag(TAG_ORG_NAME, org.getName()));
		}
		if (owner != null) {
			tags.add(TAG_RESOURCE_OWNER_ID + TAG_SEPARATOR + owner.getId());
			tags.add(tag(TAG_RESOURCE_OWNER_NAME, owner.getFullName()));
		}
		tags.add(tag(TAG_RESOURCE_NAME, name));
		if (connectionType != null) {
			tags.add(tag(TAG_CONNECTION_TYPE, connectionType.name()));
			// this describes source of event. TAG_RESOURCE_ENDPOINT = resource_endpoint is not best naming choice,
			// we need to review it at some point in coordination with solutions team
			// for file bucket is the source
			if (connectionType.category == FILE) {
				tags.add(tag(TAG_RESOURCE_ENDPOINT, bucketName));
			} else {
				tags.add(tag(TAG_RESOURCE_ENDPOINT, resourceLocation));
			}
		}
		tags.add(tag(TAG_RESOURCE_FORMAT, format));
		tags.add(tag(TAG_RESOURCE_CHANNEL, channelName));
	}
}
