package com.nexla.web.auth;

import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

public class ReusableRequestWrapper extends HttpServletRequestWrapper {

	private final byte[] bodyContent;

	public ReusableRequestWrapper(HttpServletRequest request) throws IOException {
		super(request);
		this.bodyContent = IOUtils.toByteArray(request.getReader(), StandardCharsets.UTF_8);
	}

	@Override
	@SneakyThrows
	public ServletInputStream getInputStream() {
		ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bodyContent);
		return new WrappedServletInputStream(byteArrayInputStream);
	}

	@Override
	public BufferedReader getReader() {
		return new BufferedReader(new InputStreamReader(this.getInputStream()));
	}

	private static class WrappedServletInputStream extends ServletInputStream {
		private final ByteArrayInputStream byteArrayInputStream;

		public WrappedServletInputStream(ByteArrayInputStream byteArrayInputStream) {
			this.byteArrayInputStream = byteArrayInputStream;
		}

		@Override
		public boolean isFinished() {
			return true;
		}

		@Override
		public boolean isReady() {
			return true;
		}

		@Override
		public void setReadListener(ReadListener readListener) {
		}

		public int read() {
			return byteArrayInputStream.read();
		}
	}
}
