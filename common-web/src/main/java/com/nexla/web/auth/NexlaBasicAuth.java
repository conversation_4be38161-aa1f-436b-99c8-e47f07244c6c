package com.nexla.web.auth;

import lombok.Getter;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.configurers.SessionManagementConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;

import java.util.Base64;

@Configuration
@EnableWebSecurity
@Getter
public class NexlaBasicAuth extends WebSecurityConfigurerAdapter {

	public static final String REALM = "NEXLA_BASIC_AUTH";

	@Value("${username:nexla}")
	protected String username;

	@Value("${password:nexla!@#}")
	protected String password;

	@Autowired
	public void configureGlobalSecurity(AuthenticationManagerBuilder auth) throws Exception {
		auth.inMemoryAuthentication().withUser(username).password("{noop}" + password).roles("ADMIN");
	}

	@Override
	protected void configure(HttpSecurity http) {
		getBasicHttpSecurity(http);
	}

	@SneakyThrows
	protected SessionManagementConfigurer<HttpSecurity> getBasicHttpSecurity(HttpSecurity http) {
		return http.csrf().disable()
			.authorizeRequests()
			.anyRequest()
			//.antMatchers("/user/**")
			.hasRole("ADMIN")
			.and().httpBasic()
			.realmName(REALM)
			.authenticationEntryPoint(getBasicAuthEntryPoint())
			.and().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);
	}

	@Bean
	public CustomBasicAuthenticationEntryPoint getBasicAuthEntryPoint() {
		return new CustomBasicAuthenticationEntryPoint();
	}

	/* To allow Pre-flight [OPTIONS] request from browser */
	@Override
	public void configure(WebSecurity web) {
		web.ignoring().antMatchers(HttpMethod.OPTIONS, "/**");
		//Health checks on mesos
		web.ignoring().antMatchers(HttpMethod.GET, "/actuator/info");
	}

	public String getBasicAuthCode() {
		String authString = username + ":" + password;
		return new String(Base64.getEncoder().encode(authString.getBytes()));
	}
}
