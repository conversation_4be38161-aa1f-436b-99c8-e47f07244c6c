package com.nexla.web.auth.services;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AuthResource;
import com.nexla.common.ResourceType;
import com.nexla.web.auth.AdminApiAuthenticationFilter;
import com.nexla.web.auth.NexlaBasicAuth;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.security.web.util.matcher.AnyRequestMatcher;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.nexla.common.NexlaConstants.NEXLA_DATASET_ID;

@Configuration
@EnableWebSecurity
public class SyncServiceBasicAuth extends NexlaBasicAuth {

	public static final String ACCESS_MODE = "read";

	public static final Pattern SYNC_PATTERN = Pattern.compile("/sync/(\\d*)");
	public static final Pattern SYNC_NOID_PATTERN = Pattern.compile("/sync");
	public static final Pattern INMEMORY_PATTERN = Pattern.compile("/inmemory/(\\d*)");
	public static final Pattern INMEMORY_STATUS_PATTERN = Pattern.compile("/inmemory/(\\d*)/status/(.*)");

	@Autowired
	private AdminApiClient adminAPIUtils;

	@Override
	@SneakyThrows
	protected void configure(HttpSecurity http) {
		http.addFilterBefore(new RereadableHttpCallFilter(), BasicAuthenticationFilter.class)
			.csrf().disable()
			.authorizeRequests()
			.anyRequest()
			.hasRole("ADMIN")
			.and().exceptionHandling().defaultAuthenticationEntryPointFor(getBasicAuthEntryPoint(), AnyRequestMatcher.INSTANCE)
			.and().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
			.and().addFilterBefore(getAdminApiAuthenticationFilter(), BasicAuthenticationFilter.class);
	}

	public AdminApiAuthenticationFilter getAdminApiAuthenticationFilter() {
		return new AdminApiAuthenticationFilter(adminAPIUtils, username, password) {

			@Override
			@SneakyThrows
			public AuthResource getRequestedResource(HttpServletRequest request) {
				String requestUri = request.getRequestURI();
				Matcher matcher1 = SYNC_PATTERN.matcher(requestUri);
				if (matcher1.matches()) {
					Integer id = Integer.valueOf(matcher1.group(1));
					return new AuthResource(ResourceType.DATASET, id, ACCESS_MODE);
				}
				Matcher matcher2 = SYNC_NOID_PATTERN.matcher(requestUri);
				if (matcher2.matches()) {
					Map<String, Object> jsonToMap = JsonUtils.jsonToMap(request.getInputStream());
					Integer id = Integer.valueOf(jsonToMap.get(NEXLA_DATASET_ID).toString());
					return new AuthResource(ResourceType.DATASET, id, ACCESS_MODE);
				}
				Matcher matcher3 = INMEMORY_PATTERN.matcher(requestUri);
				if (matcher3.matches()) {
					Integer id = Integer.valueOf(matcher3.group(1));
					return new AuthResource(ResourceType.SINK, id, ACCESS_MODE);
				}
				Matcher matcher4 = INMEMORY_STATUS_PATTERN.matcher(requestUri);
				if (matcher4.matches()) {
					Integer id = Integer.valueOf(matcher4.group(1));
					return new AuthResource(ResourceType.SINK, id, ACCESS_MODE);
				}
				throw new RuntimeException("Unknown URI " + requestUri);
			}
		};
	}

}
