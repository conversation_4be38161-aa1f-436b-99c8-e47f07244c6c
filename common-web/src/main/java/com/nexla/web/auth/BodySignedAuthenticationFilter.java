package com.nexla.web.auth;

import com.google.common.collect.ImmutableList;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSource;
import com.nexla.common.NexlaHashUtils;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;

public class BodySignedAuthenticationFilter extends GenericFilterBean {

	private static final Logger logger = LoggerFactory.getLogger(BodySignedAuthenticationFilter.class);
	public static final String HASH_REQUEST_PARAM = "hash_request_param";
	public static final String HASH_SECRET_KEY = "hash_secret_key";
	public static final String HASH_ALGORITHM = "hash_algorithm";

	private final AdminApiClient adminAPIUtils;
	private final String username;
	private final String password;

	public BodySignedAuthenticationFilter(AdminApiClient adminAPIUtils, String username, String password) {
		this.adminAPIUtils = adminAPIUtils;
		this.username = username;
		this.password = password;
	}

	private Optional<Integer> getDataSourceId(ServletRequest request) {
		String requestPath = ((HttpServletRequest) request).getRequestURI();
		String[] segments = requestPath.split("/");
		if (segments.length > 2) {
			String idStr = segments[2];
			if (idStr.chars().allMatch(Character::isDigit)) {
				return Optional.of(Integer.parseInt(idStr));
			}
		}
		return Optional.empty();
	}

	private Optional<String> getSignature(HttpServletRequest request, String hashRequestParam) {
		logger.debug("Hash Request param=" + hashRequestParam);
		String accessToken = request.getHeader(hashRequestParam);
		if (accessToken == null) {
			accessToken = request.getParameter(hashRequestParam);
		}
		logger.debug("accessToken=" + accessToken);
		return Optional.ofNullable(accessToken);
	}

	@Override
	@SneakyThrows
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
		HttpServletRequest httpRequest = (HttpServletRequest) request;
		Optional<Integer> datasourceId = getDataSourceId(request);
		if (datasourceId.isPresent()) {
			DataSource datasource = adminAPIUtils.getDataSource(datasourceId.get()).get();
			String hashRequestParam = datasource.getStringConfigParam(HASH_REQUEST_PARAM);
			String hashSecretKey = datasource.getStringConfigParam(HASH_SECRET_KEY);
			String hashAlgorithm = datasource.getStringConfigParam(HASH_ALGORITHM);
			if (hashRequestParam != null && hashSecretKey != null) {
				Optional<String> requestSignature = getSignature((HttpServletRequest) request, hashRequestParam);
				if (requestSignature.isPresent()) {
					ReusableRequestWrapper wrappedRequest = new ReusableRequestWrapper(httpRequest);
					authentiateAndRegister(hashSecretKey, requestSignature.get(), hashAlgorithm, wrappedRequest);
					chain.doFilter(wrappedRequest, response);
					return;
				}
			}

		}
		chain.doFilter(request, response);
	}

	@SneakyThrows
	private void authentiateAndRegister(String hashSecretKey, String requestSignature, String algorithm, ReusableRequestWrapper wrappedRequest) {
		String data = IOUtils.toString(wrappedRequest.getReader());
		String calculatedHash = NexlaHashUtils.hashAndBase64Encode(data, hashSecretKey, "utf-8", algorithm);
		logger.info("data=" + data);
		logger.info("requestSignature=" + requestSignature);
		logger.info("calculatedHash=" + calculatedHash);
		if (calculatedHash.equals(requestSignature)) {
			List<GrantedAuthority> authorities = ImmutableList.of(new SimpleGrantedAuthority("ROLE_ADMIN"));
			UsernamePasswordAuthenticationToken authReq
				= new UsernamePasswordAuthenticationToken(username, password, authorities);
			SecurityContext sc = SecurityContextHolder.getContext();
			sc.setAuthentication(authReq);
		}
	}

}
