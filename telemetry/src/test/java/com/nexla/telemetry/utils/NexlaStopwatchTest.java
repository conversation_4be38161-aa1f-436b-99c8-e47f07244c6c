package com.nexla.telemetry.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.nexla.test.UnitTests;
import java.util.concurrent.TimeUnit;
import org.junit.Before;
import org.junit.Test;
import org.junit.experimental.categories.Category;

@Category(UnitTests.class)
public class NexlaStopwatchTest {

  private NexlaStopwatch stopwatch;

  @Before
  public void setUp() {
    stopwatch = new NexlaStopwatch("TestStopwatch");
  }

  @Test
  public void testConstructorWithId() {
    assertEquals("TestStopwatch", stopwatch.getId());
    assertEquals(0, stopwatch.getTotalTimeNanos());
  }

  @Test
  public void testConstructorWithoutId() {
    final NexlaStopwatch defaultStopwatch = new NexlaStopwatch();
    assertEquals("", defaultStopwatch.getId());
    assertEquals(0, defaultStopwatch.getTotalTimeNanos());
  }

  @Test
  public void testStartAndStop() {
    stopwatch.start("Task1");
    assertEquals("Task1", stopwatch.getCurrentTaskName());
    sleep(1);
    stopwatch.stop();
    assertNull(stopwatch.getCurrentTaskName());
    assertTrue(stopwatch.getTotalTimeNanos() > 0);
    assertEquals(1, stopwatch.getTaskList().size());
  }

  @Test
  public void testStartWhileRunning() {
    stopwatch.start("Task1");
    assertEquals("Task1", stopwatch.getCurrentTaskName());
    stopwatch.start("Task2");
    assertEquals("Task2", stopwatch.getCurrentTaskName());
    stopwatch.stop();
    assertNull(stopwatch.getCurrentTaskName());
    String output = stopwatch.toString();
    System.out.println(output);
    assertTrue(output.contains("Task1"));
    assertTrue(output.contains("Task2"));
  }

  @Test(expected = IllegalStateException.class)
  public void testStopWithoutStart() {
    stopwatch.stop();
  }

  @Test
  public void testPrettyPrint() {
    stopwatch.start("Task1");
    stopwatch.stop();
    final String output = stopwatch.prettyPrint();
    System.out.println(output);
    assertTrue(output.contains("Task1"));
    assertTrue(output.contains("seconds"));
  }

  @Test
  public void testPrettyPrintWithTimeUnit() {
    stopwatch.start("Task1");
    stopwatch.stop();
    final String output = stopwatch.prettyPrint(TimeUnit.MILLISECONDS);
    System.out.println(output);
    assertTrue(output.contains("Task1"));
    assertTrue(output.contains("milliseconds"));
  }

  @Test
  public void testShortSummary() {
    stopwatch.start("Task1");
    stopwatch.stop();
    String summary = stopwatch.shortSummary();
    assertTrue(summary.contains("TestStopwatch"));
    assertTrue(summary.contains("seconds"));
  }

  @Test
  public void testGetTotalTimeSeconds() {
    stopwatch.start("Task1");
    sleep(1);
    stopwatch.stop();
    assertTrue(stopwatch.getTotalTimeSeconds() > 0);
  }

  @Test
  public void testGetTotalTimeMillis() {
    stopwatch.start("Task1");
    sleep(1);
    stopwatch.stop();
    assertTrue(stopwatch.getTotalTimeMillis() > 0);
  }

  @Test
  public void testGetTotalTimeWithTimeUnit() {
    stopwatch.start("Task1");
    sleep(1);
    stopwatch.stop();
    assertTrue(stopwatch.getTotalTime(TimeUnit.MILLISECONDS) > 0);
  }

  @Test
  public void testToString() {
    stopwatch.start("Task1");
    stopwatch.stop();
    String result = stopwatch.toString();
    assertTrue(result.contains("Task1"));
    assertTrue(result.contains("%"));
  }

  @Test
  public void testTaskInfoGetters() {
    NexlaStopwatch.TaskInfo taskInfo = new NexlaStopwatch.TaskInfo("Task1", 1000000);
    assertEquals("Task1", taskInfo.getTaskName());
    assertEquals(1000000, taskInfo.getTimeNanos());
    assertEquals(1, taskInfo.getTimeMillis());
    assertEquals(0.001, taskInfo.getTimeSeconds(), 0.0001);
    assertEquals(1, taskInfo.getTime(TimeUnit.MILLISECONDS), 0.0001);
  }

  private static void sleep(final long ms) {
    try {
      Thread.sleep(ms);
    } catch (final Exception ignored) {
      // Ignore
    }
  }
}
