package com.nexla.telemetry;

import com.nexla.telemetry.dto.*;
import io.micrometer.core.instrument.MeterRegistry;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.function.Supplier;


public class NoopTelemetry implements Telemetry {
	public static final NoopTelemetry NOOP_TELEMETRY = new NoopTelemetry();
	public static final QuietAutoClosable NOOPP_AUTO_CLOSEABLE = new QuietAutoClosable(() -> {});
	public static final NexlaTimer NOOP_TIMER = () -> NOOPP_AUTO_CLOSEABLE;
	public static final NexlaGauge NOOP_GAUGE = (value) -> {};
	public static final NexlaCounter NOOP_COUNTER = (value) -> {};
	public static final NexlaHistogram NOOP_HISTOGRAM = (value) -> {};
	public static final NexlaScheduledGauge NOOP_SCHEDULED_GAUGE = () -> {};

	@Override
	public MeterRegistry getMeterRegistry() {
		return null;
	}

	@Override
	public Map<String, String> systemLabels() {
		return Map.of();
	}

	@Override
	public NexlaGauge getGauge(String name, List<Label> labels) {
		return NOOP_GAUGE;
	}

	@Override
	public NexlaTimer getGaugeTimer(String name, List<Label> labels) {
		return NOOP_TIMER;
	}

	@Override
	public NexlaCounter getCounter(String name, List<Label> labels) {
		return NOOP_COUNTER;
	}

	@Override
	public NexlaHistogram getHistogram(String name, List<Label> labels, double[] buckets) {
		return NOOP_HISTOGRAM;
	}

	@Override
	public NexlaHistogram getPushHistogram(String name, List<Label> groupKeys, List<Label> labels, double[] buckets) {
		return NOOP_HISTOGRAM;
	}

	@Override
	public NexlaTimer getHistogramTimer(String name, List<Label> labels, double[] buckets) {
		return NOOP_TIMER;
	}

	@Override
	public void recordOrPushGauge(String name, List<Label> groupKeys, Double value, List<Label> labels) {
	}

	@Override
	public void recordGauge(String name, double value) {
	}

	@Override
	public void recordGauge(String name, double value, List<Label> labels) {
	}

	@Override
	public void recordGaugeDuration(String name, List<Label> labels, Runnable runnable) {
		runnable.run();
	}

	@Override
	public <T> T recordGaugeDuration(String name, List<Label> labels, Supplier<T> supplier) {
		return supplier.get();
	}

	@Override
	public <T> T recordGaugeDuration(String name, List<Label> labels, Callable<T> callable) throws Exception {
		return callable.call();
	}

	@Override
	public void recordOrPushCounter(String name, List<Label> groupKeys, List<Label> labels) {
	}

	@Override
	public void pushGaugeDuration(String name, List<Label> groupKeys, Runnable runnable) {
		runnable.run();
	}

	@Override
	public void pushGaugeDuration(String name, List<Label> groupKeys, List<Label> labels, Runnable runnable) {
		runnable.run();
	}


	@Override
	public void pushGauge(String name, List<Label> groupKeys, Double value) {
	}

	@Override
	public void pushGauge(String name, List<Label> groupKeys, Double value, List<Label> labels) {
	}

	@Override
	public void recordCounter(String name) {
	}

	@Override
	public void recordCounter(String name, List<Label> labels) {
	}

	@Override
	public void recordCounter(String name, double value) {
	}

	@Override
	public void recordCounter(String name, double value, List<Label> labels) {

	}
	public void recordHistogramDuration(String name, List<Label> labels, Runnable runnable) {
		runnable.run();
	}

	@Override
	public <T> T recordHistogramDuration(String name, List<Label> labels, Supplier<T> supplier) {
		return supplier.get();
	}

	@Override
	public <T> T recordHistogramDuration(String name, List<Label> labels, Callable<T> callable) throws Exception {
		return callable.call();
	}

	@Override
	public void recordHistogram(String name, double value) {
	}

	@Override
	public void recordHistogram(String name, double value, List<Label> labels) {
	}

	@Override
	public void registerPushGauge(String name, List<Label> groupKeys, List<Label> labels) {
	}

	@Override
	public void registerPushCounter(String name, List<Label> groupingKey, List<Label> labels) {
	}

	@Override
	public void pushCounter(String name, List<Label> groupKeys) {
	}

	@Override
	public void pushCounter(String name, List<Telemetry.Label> groupKeys, List<Telemetry.Label> labels) {
	}

	@Override
	public void pushCounter(String name, List<Label> groupKeys, List<Label> labels, Double amount) {
	}
}
