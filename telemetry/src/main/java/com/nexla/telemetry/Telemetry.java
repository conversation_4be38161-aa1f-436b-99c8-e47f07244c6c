package com.nexla.telemetry;

import com.nexla.telemetry.dto.NexlaCounter;
import com.nexla.telemetry.dto.NexlaGauge;
import com.nexla.telemetry.dto.NexlaTimer;
import com.nexla.telemetry.dto.NexlaHistogram;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.function.Supplier;

public interface Telemetry {
	MeterRegistry getMeterRegistry();
	Map<String, String> systemLabels();

	NexlaGauge getGauge(String name, List<Label> labels);
	NexlaTimer getGaugeTimer(String name, List<Label> labels);
	NexlaCounter getCounter(String name, List<Label> labels);
	NexlaHistogram getHistogram(String name, List<Label> labels, double[] buckets);
	NexlaHistogram getPushHistogram(String name, List<Label> groupKeys, List<Label> labels, double[] buckets);
	NexlaTimer getHistogramTimer(String name, List<Label> labels, double[] buckets);

	void recordOrPushGauge(String name, List<Label> groupKeys, Double value, List<Label> labels);

	void recordGauge(String name, double value);
	void recordGauge(String name, double value, List<Label> labels);

	void recordGaugeDuration(String name, List<Label> labels, Runnable runnable);
	<T> T recordGaugeDuration(String name, List<Label> labels, Supplier<T> supplier);
	<T> T recordGaugeDuration(String name, List<Label> labels, Callable<T> callable) throws Exception;

	void recordOrPushCounter(String name, List<Label> groupKeys, List<Label> labels);
	void recordCounter(String name);
	void recordCounter(String name, List<Label> labels);

	void recordCounter(String name, double value);
	void recordCounter(String name, double value, List<Label> labels);
	void recordHistogramDuration(String name, List<Label> labels, Runnable runnable);
	<T> T recordHistogramDuration(String name, List<Label> labels, Supplier<T> supplier);
	<T> T recordHistogramDuration(String name, List<Label> labels, Callable<T> callable) throws Exception;

	void recordHistogram(String name, double value);
	void recordHistogram(String name, double value, List<Label> labels);

	void registerPushGauge(String name, List<Label> groupKeys, List<Label> labels);

	void pushGauge(String name, List<Label> groupKeys, Double value);
	void pushGauge(String name, List<Label> groupKeys, Double value, List<Label> labels);

	void pushGaugeDuration(String name, List<Label> groupKeys, Runnable runnable);
	void pushGaugeDuration(String name, List<Label> groupKeys, List<Label> labels, Runnable runnable);

	void registerPushCounter(String name, List<Label> groupKeys, List<Label> labels);

	void pushCounter(String name, List<Label> groupKeys);
	void pushCounter(String name, List<Label> groupKeys, List<Label> labels);

	void pushCounter(String name, List<Label> groupKeys, List<Label> labels, Double amount);

	@Data
	class Label {
		private final String name;
		private final String value;
	}
}
