package com.nexla.telemetry.jmx;

import com.nexla.telemetry.Telemetry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Simple runnable, which will report used heap as prometheus gauge on a minute basis.
 */
public class JmxExporter {
    private final Telemetry telemetry;
    private static final Logger log = LoggerFactory.getLogger(JmxExporter.class.getName());

    private final Runnable metricsFetcher = new Runnable() {
        @Override
        public void run() {
            long totalHeapMem = Runtime.getRuntime().totalMemory();
            long freeHeapMem = Runtime.getRuntime().freeMemory();
            long usedMem = totalHeapMem - freeHeapMem;
            telemetry.recordGauge("java_lang_Memory_HeapMemoryUsage_used", usedMem);
        }
    };

    public JmxExporter(Telemetry telemetry) {
        this.telemetry = telemetry;
    }

    public void enable() {
        ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
        executorService.scheduleAtFixedRate(this.metricsFetcher, 0, 60, TimeUnit.SECONDS);
        log.info("JMX export enabled for java_lang_Memory_HeapMemoryUsage_used");
    }
}
