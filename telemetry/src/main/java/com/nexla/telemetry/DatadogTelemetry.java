package com.nexla.telemetry;

import com.nexla.telemetry.dto.NexlaCounter;
import com.nexla.telemetry.dto.NexlaGauge;
import com.nexla.telemetry.dto.NexlaHistogram;
import com.nexla.telemetry.dto.NexlaTimer;
import com.timgroup.statsd.NonBlockingStatsDClientBuilder;
import com.timgroup.statsd.StatsDClient;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Getter
public class DatadogTelemetry implements Telemetry {

	private static final Logger LOGGER = LoggerFactory.getLogger(DatadogTelemetry.class);
	private static final NoopTelemetry NOOP_TELEMETRY = NoopTelemetry.NOOP_TELEMETRY;

	@Getter
	private final StatsDClient statsd;

	public DatadogTelemetry(String agentHost, int port, String appName, String envType) {
		String[] defaultTags = {ServerDimension.APP_NAME() + ":" + appName, ServerDimension.ENV_TYPE() + ":" + envType};
		StringBuilder namespace = new StringBuilder("nexla").append(".").append(appName);
		NonBlockingStatsDClientBuilder builder = new NonBlockingStatsDClientBuilder()
			.prefix(namespace.toString())
			.hostname(agentHost)
			.port(port)
			.constantTags(defaultTags);

		this.statsd = builder.build();
		LOGGER.info("Datadog is setup to emit telemetry to {}:{} with envType:{} and appName:{}", agentHost, port, envType, appName);
	}

	@Override
	public MeterRegistry getMeterRegistry() {
		return null;
	}

	@Override
	public Map<String, String> systemLabels() {
		return Map.of();
	}

	@Override
	public NexlaGauge getGauge(String name, List<Label> labels) {
		return NOOP_TELEMETRY.getGauge(name, labels);
	}

	@Override
	public NexlaTimer getGaugeTimer(String name, List<Label> labels) {
		return NOOP_TELEMETRY.getGaugeTimer(name, labels);
	}

	@Override
	public NexlaCounter getCounter(String name, List<Label> labels) {
		return NOOP_TELEMETRY.getCounter(name, labels);
	}

	@Override
	public NexlaHistogram getHistogram(String name, List<Label> labels, double[] buckets) {
		return NOOP_TELEMETRY.getHistogram(name, labels, buckets);
	}

	@Override
	public NexlaHistogram getPushHistogram(String name, List<Label> groupKeys, List<Label> labels, double[] buckets) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public NexlaTimer getHistogramTimer(String name, List<Label> labels, double[] buckets) {
		return NOOP_TELEMETRY.getHistogramTimer(name, labels, buckets);
	}

	@Override
	public void recordOrPushGauge(String name, List<Label> groupKeys, Double value, List<Label> labels) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public void recordGauge(String name, double value) {
		recordGauge(name, value, Collections.emptyList());
	}

	@Override
	public void recordGauge(String name, double value, List<Label> labels) {
		statsd.gauge(name, value, convertLabels(labels));
	}

	@Override
	public void recordGaugeDuration(String name, List<Label> labels, Runnable runnable) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public <T> T recordGaugeDuration(String name, List<Label> labels, Supplier<T> supplier) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public <T> T recordGaugeDuration(String name, List<Label> labels, Callable<T> callable) throws Exception {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public void recordOrPushCounter(String name, List<Label> groupKeys, List<Label> labels) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public void recordCounter(String name) {
		recordCounter(name, Collections.emptyList());
	}

	@Override
	public void recordCounter(String name, double value) {
		recordCounter(name, value, Collections.emptyList());
	}

	@Override
	public void recordCounter(String name, List<Label> labels) {
		statsd.increment(name, convertLabels(labels));
	}

	@Override
	public void recordCounter(String name, double value, List<Label> labels) {
		statsd.count(name, value, convertLabels(labels));
	}

	public void recordHistogramDuration(String name, List<Label> labels, Runnable runnable) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public <T> T recordHistogramDuration(String name, List<Label> labels, Supplier<T> supplier) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public <T> T recordHistogramDuration(String name, List<Label> labels, Callable<T> callable) throws Exception {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public void recordHistogram(String name, double value) {
		recordHistogram(name, value, Collections.emptyList());
	}

	@Override
	public void recordHistogram(String name, double value, List<Label> labels) {
		statsd.histogram(name, value, convertLabels(labels));
	}

	@Override
	public void pushGaugeDuration(String name, List<Label> groupingKey, Runnable runnable) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public void pushGaugeDuration(String name, List<Label> groupingKey, List<Label> labels, Runnable runnable) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public void pushGauge(String name, List<Label> groupingKey, Double value) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public void pushGauge(String name, List<Label> groupingKey, Double value, List<Label> labels) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public void registerPushCounter(String name, List<Label> groupingKey, List<Label> labels) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public void pushCounter(String name, List<Label> groupingKey, List<Label> labels) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public void registerPushGauge(String name, List<Label> groupKeys, List<Label> labels) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public void pushCounter(String name, List<Label> groupKeys) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	@Override
	public void pushCounter(String name, List<Label> groupKeys, List<Label> labels, Double amount) {
		throw new IllegalArgumentException("Not supported for datadog yet");
	}

	private String[] convertLabels(List<Label> tags) {
		List<String> labels = tags
			.stream()
			.map(t -> t.getName() + ":" + t.getValue())
			.collect(Collectors.toList());
		return labels.toArray(new String[0]);
	}
}
