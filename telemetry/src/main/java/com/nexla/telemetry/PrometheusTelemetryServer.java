package com.nexla.telemetry;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.Optional;

import io.prometheus.client.exporter.HTTPServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PrometheusTelemetryServer {
    public static int DEFAULT_PORT = 9000;
    private static final Logger log = LoggerFactory.getLogger(PrometheusTelemetryServer.class.getName());
    private volatile HTTPServer httpServer = null;
    private final int port;

    public PrometheusTelemetryServer(Optional<Integer> telemetryPort) {
        this.port = telemetryPort.orElse(DEFAULT_PORT);
    }

    public void start() {
        try {
            if (this.httpServer == null) {
                this.httpServer = new HTTPServer(new InetSocketAddress(port), PrometheusTelemetry.COLLECTOR_REGISTRY, true);
            }
        } catch (IOException e) {
            log.info("Can't start http server for Prometheus metrics", e);
        }
    }

    public void stop() {
        if (this.httpServer != null) {
            this.httpServer.close();
            this.httpServer = null;
        }
    }
}
