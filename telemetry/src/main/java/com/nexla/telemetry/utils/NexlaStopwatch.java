package com.nexla.telemetry.utils;

import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import lombok.Getter;

/**
 * A simple stopwatch utility for timing multiple tasks. It provides the total execution time as
 * well as the duration of each individual named task.
 */
public class NexlaStopwatch {
  @Getter private final String id;

  /** Start time of the current task. */
  private long startTimeNanos;

  /** Total running time. -- GETTER -- Get the total time for all tasks in nanoseconds. */
  @Getter private long totalTimeNanos;

  /** Name of the current task. */
  @Getter private String currentTaskName;

  /** List of all tasks executed. */
  @Getter private final List<TaskInfo> taskList = new ArrayList<>(1);

  public NexlaStopwatch() {
    this("");
  }

  public NexlaStopwatch(final String id) {
    this.id = id;
  }

  /**
   * Start a named task.
   *
   * @param taskName the name of the task to start
   */
  public void start(final String taskName) {
    if (this.currentTaskName != null) {
      // if current task is running, stop it
      this.stop();
    }
    this.currentTaskName = taskName;
    this.startTimeNanos = System.nanoTime();
  }

  /**
   * Stop the current task.
   *
   * <p>The results are undefined if timing methods are called without invoking at least one pair of
   * {@code start()} / {@code stop()} methods.
   */
  public void stop() throws IllegalStateException {
    if (this.currentTaskName == null) {
      throw new IllegalStateException("Can't stop StopWatch: it's not running");
    }
    final long lastTime = System.nanoTime() - this.startTimeNanos;
    this.totalTimeNanos += lastTime;
    this.taskList.add(new TaskInfo(this.currentTaskName, lastTime));
    this.currentTaskName = null;
  }

  /**
   * Generate a table describing all tasks performed in seconds (with decimal points in nanosecond
   * precision).
   */
  public String prettyPrint() {
    return prettyPrint(TimeUnit.SECONDS);
  }

  /**
   * Generate a table describing all tasks performed in the requested time unit (with decimal points
   * in nanosecond precision).
   *
   * @param timeUnit the unit to use for rendering total time and task time
   */
  public String prettyPrint(final TimeUnit timeUnit) {
    final NumberFormat nf = NumberFormat.getNumberInstance(Locale.ENGLISH);
    nf.setMaximumFractionDigits(9);
    nf.setGroupingUsed(false);

    final NumberFormat pf = NumberFormat.getPercentInstance(Locale.ENGLISH);
    pf.setMinimumIntegerDigits(2);
    pf.setGroupingUsed(false);

    final StringBuilder sb = new StringBuilder(128);
    sb.append("StopWatch '").append(getId()).append("': ");
    final String total =
        (timeUnit == TimeUnit.NANOSECONDS
            ? nf.format(getTotalTimeNanos())
            : nf.format(getTotalTime(timeUnit)));
    sb.append(total).append(" ").append(timeUnit.name().toLowerCase(Locale.ENGLISH));
    final int width = Math.max(sb.length(), 40);
    sb.append("\n");

    final String line = "-".repeat(width) + "\n";
    String unitName = timeUnit.name();
    unitName = unitName.charAt(0) + unitName.substring(1).toLowerCase(Locale.ENGLISH);
    unitName = String.format("%-12s", unitName);
    sb.append(line);
    sb.append(unitName).append("  %       Task name\n");
    sb.append(line);

    int digits = total.indexOf('.');
    if (digits < 0) {
      digits = total.length();
    }
    nf.setMinimumIntegerDigits(digits);
    nf.setMaximumFractionDigits(10 - digits);

    for (final TaskInfo task : this.taskList) {
      sb.append(
          String.format(
              "%-14s",
              (timeUnit == TimeUnit.NANOSECONDS
                  ? nf.format(task.getTimeNanos())
                  : nf.format(task.getTime(timeUnit)))));
      sb.append(String.format("%-8s", pf.format(task.getTimeSeconds() / getTotalTimeSeconds())));
      sb.append(task.getTaskName()).append('\n');
    }

    return sb.toString();
  }

  /** Get a short description of the total running time in seconds. */
  public String shortSummary() {
    return "StopWatch '" + getId() + "': " + getTotalTimeSeconds() + " seconds";
  }

  /** Get the total time for all tasks in seconds. */
  public double getTotalTimeSeconds() {
    return getTotalTime(TimeUnit.SECONDS);
  }

  /** Get the total time for all tasks in milliseconds. */
  public long getTotalTimeMillis() {
    return TimeUnit.NANOSECONDS.toMillis(this.totalTimeNanos);
  }

  /**
   * Get the total time for all tasks in the requested time unit (with decimal points in nanosecond
   * precision).
   *
   * @param timeUnit the unit to use
   */
  public double getTotalTime(final TimeUnit timeUnit) {
    return (double) this.totalTimeNanos / TimeUnit.NANOSECONDS.convert(1, timeUnit);
  }

  /** Generate an informative string describing all tasks performed in seconds. */
  @Override
  public String toString() {
    final StringBuilder sb = new StringBuilder(shortSummary());
    for (final TaskInfo task : this.taskList) {
      sb.append("; [")
          .append(task.getTaskName())
          .append("] took ")
          .append(task.getTimeSeconds())
          .append(" seconds");
      final long percent = Math.round(100.0 * task.getTimeSeconds() / getTotalTimeSeconds());
      sb.append(" = ").append(percent).append('%');
    }
    return sb.toString();
  }

  /** Nested class to hold data about one task executed within the {@link NexlaStopwatch}. */
  @Getter
  public static final class TaskInfo {

    /** -- GETTER -- Get the name of this task. */
    private final String taskName;

    /** -- GETTER -- Get the time this task took in nanoseconds. */
    private final long timeNanos;

    TaskInfo(final String taskName, final long timeNanos) {
      this.taskName = taskName;
      this.timeNanos = timeNanos;
    }

    /** Get the time this task took in milliseconds. */
    public long getTimeMillis() {
      return TimeUnit.NANOSECONDS.toMillis(this.timeNanos);
    }

    /** Get the time this task took in seconds. */
    public double getTimeSeconds() {
      return getTime(TimeUnit.SECONDS);
    }

    /**
     * Get the time this task took in the requested time unit (with decimal points in nanosecond
     * precision).
     *
     * @param timeUnit the unit to use
     */
    public double getTime(final TimeUnit timeUnit) {
      return (double) this.timeNanos / TimeUnit.NANOSECONDS.convert(1, timeUnit);
    }
  }
}
