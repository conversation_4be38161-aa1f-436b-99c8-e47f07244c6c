package com.nexla.connector.sql.sink.sqlserver;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.admin.client.pipeline.PDataSource;
import com.nexla.admin.client.pipeline.Pipeline;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaConstants;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.DbTestUtils.DbData;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.jdbc.JdbcAuthConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.probe.sql.SqlConnectorService;
import com.nexla.probe.sql.connection.SingleConnection;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.SqlServerDialect;
import lombok.SneakyThrows;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testcontainers.containers.JdbcDatabaseContainer;
import org.testcontainers.containers.MSSQLServerContainer;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.NexlaConstants.CREDS_ENC;
import static com.nexla.common.NexlaConstants.CREDS_ENC_IV;
import static com.nexla.common.NexlaConstants.LISTING_APP_SERVER_URL;
import static com.nexla.common.NexlaConstants.SINK_ID;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.jdbc.JdbcAuthConfig.DATABASE_NAME;
import static com.nexla.connector.config.jdbc.JdbcAuthConfig.PASSWORD;
import static com.nexla.connector.config.jdbc.JdbcAuthConfig.URL;
import static com.nexla.connector.config.jdbc.JdbcAuthConfig.USERNAME;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.MAPPING;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.PARALLELISM;
import static com.nexla.connector.properties.SqlConfigAccessor.INSERT_MODE;
import static com.nexla.connector.properties.SqlConfigAccessor.PRIMARY_KEY;
import static com.nexla.connector.properties.SqlConfigAccessor.TABLE;
import static org.mockito.Mockito.mock;

public class SqlServerJdbcSinkTaskTestHelper {

	private final static String SCHEMA_NAME = "schemaname";
	final static SingleConnection sc = new SingleConnection();
	final static DbDialect dbDialect = new SqlServerDialect();

	@SneakyThrows
	static void recreateTable(MappingConfig mappingConfig, JdbcDatabaseContainer db, Map<String, String> props) {

		cleanTable(db, props.get("table"));

		props.put(MAPPING, toJsonString(mappingConfig));
		JdbcSinkConnectorConfig abstractConfig = new JdbcSinkConnectorConfig(props);

		AdminApiClient client = mock(AdminApiClient.class);
		Map<String, Object> map = Maps.newHashMap();
		PDataSource dataSource = new PDataSource();

		dataSource.setConnectionType(ConnectionType.SQLSERVER);
		dataSource.setId(1);

		var dataSink = new DataSink();
		dataSink.setId(1);
		dataSink.setConnectionType(ConnectionType.SQLSERVER);
		dataSink.setSinkConfig(Map.of(PARALLELISM, "1"));
		dataSink.setStatus(ResourceStatus.ACTIVE);

		Pipeline pipeline = new Pipeline(Lists.newArrayList(), dataSource, dataSink);

		Mockito.when(client.getPipeline(1)).thenReturn(Optional.of(pipeline));
		Mockito.when(client.getDataSink(1)).thenReturn(Optional.of(dataSink));

		SqlConnectorService sqlConnectorService = new SqlConnectorService(null, client);

		sqlConnectorService.createDestination(abstractConfig, Optional.empty());
	}

	@SneakyThrows
	static void cleanTable(JdbcDatabaseContainer db, String tableName) {
		try (Connection connection = db.createConnection("")) {
			connection.createStatement().execute("DROP TABLE " + dbDialect.q(tableName));
			connection.commit();
		} catch (Exception e) {
		}
	}

	public static Connection createConnection(DbData db) {
		Map<String, String> parsedconfig = Maps.newHashMap();
		parsedconfig.put("password", db.password);
		parsedconfig.put("url", db.url);
		parsedconfig.put("username", db.username);
		parsedconfig.put("credentials_type", "SQLSERVER");
		return sc.getConnection(new JdbcAuthConfig(parsedconfig, 123));
	}

	@SneakyThrows
	static List<Map<String, String>> selectRows(MSSQLServerContainer db, boolean schemaSet, Set<String> columns, String tableName) {
		List<Map<String, String>> rows = new ArrayList<>();
		String qualifiedName = schemaSet ? dbDialect.getQualifiedTableName(tableName, SCHEMA_NAME, DATABASE_NAME) : tableName;
		try (
			Connection connection = db.createConnection("");
			ResultSet resultSet = connection.createStatement().executeQuery("SELECT * FROM " + dbDialect.q(qualifiedName))
		) {
			connection.setAutoCommit(false);
			try {
				while (resultSet.next()) {
					Map<String, String> row = new HashMap<>();
					for (String column : columns) {
						row.put(column, resultSet.getObject(column).toString());
					}
					rows.add(row);
				}
			} finally {
				connection.rollback();
			}
		}
		return rows;
	}

	static NexlaMessage createMessage(LinkedHashMap<String, Object> rawMessage) {
		NexlaMessage message = new NexlaMessage(rawMessage);
		NexlaMetaData nexlaMetaData = new NexlaMetaData();
		nexlaMetaData.setRunId(1L);
		message.setNexlaMetaData(nexlaMetaData);
		nexlaMetaData.setTrackerId(Tracker.parse("u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010"));
		return message;
	}

	static Map<String, String> getProperties(JdbcDatabaseContainer db, String credsType, String tableName) {
		Map<String, String> properties = Maps.newHashMap();
		properties.put(TABLE, tableName);
		properties.put(PRIMARY_KEY, "id");
		properties.put(NexlaConstants.CREDENTIALS_TYPE, credsType);
		properties.put(DATABASE_NAME, "master");
		properties.put(PARALLELISM, "1");
		properties.put(URL, db.getJdbcUrl());
		properties.put(USERNAME, db.getUsername());
		properties.put(PASSWORD, db.getPassword());
		properties.put(SINK_ID, "1");
		properties.put(CREDS_ENC, "1");
		properties.put(CREDS_ENC_IV, "1");
		properties.put(UNIT_TEST, "true");
		properties.put(INSERT_MODE, "insert");
		properties.put(LISTING_APP_SERVER_URL, "!23");
		return properties;
	}
}
