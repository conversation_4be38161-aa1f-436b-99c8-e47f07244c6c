package com.nexla.connector.sql.sink;

import com.nexla.common.sink.TopicPartition;
import com.nexla.connector.NexlaMessageContext;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;

import static com.bazaarvoice.jolt.JsonUtils.stringToType;
import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.FileUtils.closeSilently;

class FileCollection implements DataCollection {

	private int size = 0;

	private final Map<TopicPartition, Long> lastOffsetMap = new HashMap<>();

	private File file;
	private FileWriter fileWriter;
	private BufferedWriter bufferedWriter;

	@Override
	public int size() {
		return size;
	}

	@Override
	public Map<TopicPartition, Long> lastOffsetMap() {
		return lastOffsetMap;
	}

	@Override
	@SneakyThrows
	public void add(NexlaMessageContext message) {
		if (file == null) {
			openFile();
		}
		lastOffsetMap.put(message.topicPartition, message.kafkaOffset);
		size++;

		bufferedWriter.append(toJsonString(message));
		bufferedWriter.newLine();
	}

	@SneakyThrows
	private void openFile() {
		this.file = Files.createTempFile("", "").toFile();
		this.fileWriter = new FileWriter(file);
		this.bufferedWriter = new BufferedWriter(fileWriter);
	}

	@Override
	@SneakyThrows
	public void closeResources() {
		closeSilently(bufferedWriter, fileWriter);
	}

	public void deleteFile() {
		file.delete();
	}

	@Override
	@SneakyThrows
	public StreamEx<NexlaMessageContext> readAll() {
		FileReader fileReader = new FileReader(file);
		BufferedReader bufferedReader = new BufferedReader(fileReader);
		return StreamEx.of(
			bufferedReader
				.lines()
				.map(line -> stringToType(line, NexlaMessageContext.class))
				.onClose(() -> closeSilently(bufferedReader, fileReader)));
	}
}
