package com.nexla.connector.sql.sink;

import com.nexla.connect.common.connector.BaseSinkConnector;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.connector.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * JdbcSinkConnector is a Kafka Connect Connector implementation that exports data
 * from Kafka to DB.
 */
public class JdbcSinkConnector extends BaseSinkConnector {
    private static final Logger logger = LoggerFactory.getLogger(JdbcSinkConnector.class);
    public static final String JDBC_SINK_TELEMETRY_NAME = "jdbc-sink";

    private JdbcSinkConnectorConfig config;

    @Override
    protected String telemetryAppName() {
        return JDBC_SINK_TELEMETRY_NAME;
    }

    @Override
    public void start(Map<String, String> props) {
        super.start(props);
        this.config = parseConfig(configProperties);
    }

    private JdbcSinkConnectorConfig parseConfig(Map<String, String> props) {
        return new JdbcSinkConnectorConfig(props);
    }

    @Override
    public Class<? extends Task> taskClass() {
        Class<? extends Task> clazz = getTaskClass();
        logger.info("[SINK-{}] is starting with class {}", config.sinkId, clazz.getName());
        return clazz;
    }

    private Class<? extends Task> getTaskClass() {
        if (config.cdcEnabled) {
            return CdcSinkTask.class;
        } else if (config.isEltFlowType()) {
            return JdbcELTSinkTask.class;
        }
        return JdbcSinkTask.class;
    }

    @Override
    public ConfigDef config() {
        return JdbcSinkConnectorConfig.configDef();
    }
}
