package com.nexla.connector.sql.processor;

import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connect.common.cdc.OperationType;

import java.util.Optional;

public class OpProcessorFactory {

    public static Optional<BaseOpProcessor> getOpProcessor(OperationType op,
                                                           Optional<JdbcSinkConnectorConfig> configOpt,
                                                           TableProcessor tableProcessor) {

        if (configOpt.isEmpty()) {
            tableProcessor.getLogger().info("CDC: JdbcSinkConnectorConfig is empty, nothing to process");
            return Optional.empty();
        }

        JdbcSinkConnectorConfig config = configOpt.get();

        if (config.mappingConfig.isEmpty()) {
            tableProcessor.getLogger().warn("CDC: Mapping Config should not be empty");
            return Optional.empty();
        }

        switch (op) {
            case READ:
            case CREATE:
                return Optional.of(new InsertOpProcessor(config, tableProcessor));
            case UPDATE:
                return Optional.of(new UpdateOpProcessor(config, tableProcessor));
            case DELETE:
                if (config.mappingConfig.get().isReplayRowDeletions()) {
                    return Optional.of(new DeleteOpProcessor(config, tableProcessor));
                }
                break;
            case TRUNCATE:
                return Optional.of(new TruncateOpProcessor(config, tableProcessor));
        }
        return Optional.empty();
    }
}
