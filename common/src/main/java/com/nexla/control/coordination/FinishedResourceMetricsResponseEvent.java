package com.nexla.control.coordination;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.nexla.common.ResourceType;
import com.nexla.common.metrics.ResourceMetric;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FinishedResourceMetricsResponseEvent implements CoordinationMessage {

    private final String messageId;
    private final Integer originNodeId;
    private final Long runId;
    private final Map<ResourceType, List<ResourceMetric>> data;
    private final Long timeMs;
    private final Long createdAt = System.currentTimeMillis();

    @Override
    public CoordinationEventType getCoordinationEventType() {
        return CoordinationEventType.RESOURCE_METRICS_DONE;
    }
}