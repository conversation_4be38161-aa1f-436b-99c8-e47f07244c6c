package com.nexla.control.coordination;

import com.nexla.common.ResourceType;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Data
public class HeartbeatConnectorStateCoordination implements CoordinationMessage {
	private final String messageId;
	private final ResourceType resourceType;
	private final Integer resourceId;
	private final Long timeMs;
	private final Long createdAt = System.currentTimeMillis();

	private final Map<String, String> context = new HashMap<>();

	@Override
	public CoordinationEventType getCoordinationEventType() {
		return CoordinationEventType.HEARTBEAT_CONNECTOR_STATE;
	}
}
