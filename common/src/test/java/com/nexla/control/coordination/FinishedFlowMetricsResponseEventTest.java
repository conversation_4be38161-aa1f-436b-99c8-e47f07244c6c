package com.nexla.control.coordination;

import com.nexla.common.ResourceType;
import com.nexla.common.StreamUtils;
import com.nexla.common.metrics.MetricWithErrors;
import com.nexla.common.metrics.ResourceMetric;
import com.nexla.test.UnitTests;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.experimental.categories.Category;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Category(UnitTests.class)
public class FinishedFlowMetricsResponseEventTest {

    private static final Integer ORIGIN_NODE_ID = 123;
    private static final Integer SOURCE_ID = 1;
    private static final Integer DATASET_ID_01 = 2;
    private static final Integer DATASET_ID_02 = 3;
    private static final Integer SINK_ID = 4;
    private static final Long TIME_MS = 1707332679778L;
    private static final Long RUN_ID = 1707332678642L;

    @Test
    public void testSerialization() {

        final String messageId = UUID.randomUUID().toString();
        MetricWithErrors sourceMetrics = new MetricWithErrors(100, 20, 5);
        MetricWithErrors dataset01Metrics = new MetricWithErrors(50, 10, 5);
        MetricWithErrors dataset02Metrics = new MetricWithErrors(50, 10, 0);
        MetricWithErrors sinkMetrics = new MetricWithErrors(50, 10, 0);

        ResourceMetric resourceMetric01 = new ResourceMetric(SOURCE_ID, sourceMetrics);
        ResourceMetric resourceMetric02 = new ResourceMetric(DATASET_ID_01, dataset01Metrics);
        ResourceMetric resourceMetric03 = new ResourceMetric(DATASET_ID_02, dataset02Metrics);
        ResourceMetric resourceMetric04 = new ResourceMetric(SINK_ID, sinkMetrics);

        Map<ResourceType, List<ResourceMetric>> data = new HashMap<>();
        data.put(ResourceType.SOURCE, List.of(resourceMetric01));
        data.put(ResourceType.DATASET, List.of(resourceMetric02, resourceMetric03));
        data.put(ResourceType.SINK, List.of(resourceMetric04));

        FinishedResourceMetricsResponseEvent responseEvent = new FinishedResourceMetricsResponseEvent(
                messageId, ORIGIN_NODE_ID, RUN_ID, data, TIME_MS);
        String responseEventStr = "{" +
                "\"coordinationEventType\":\"RESOURCE_METRICS_DONE\"," +
                "\"messageId\":\"" + messageId + "\"," +
                "\"originNodeId\":\"" + ORIGIN_NODE_ID + "\"," +
                "\"runId\":" + RUN_ID + "," +
                "\"data\": {" +
                "   \"SOURCE\": [" +
                "       {" +
                "           \"id\": "+SOURCE_ID+","+
                "           \"metrics\": {" +
                "              \"records\":" + sourceMetrics.getRecords() + "," +
                "              \"size\":" + sourceMetrics.getSize() + "," +
                "              \"errors\":" + sourceMetrics.getErrors() +
                "           }" +
                "       }"+
                "   ]," +
                "   \"DATASET\": [" +
                "       {" +
                "           \"id\": "+DATASET_ID_01+","+
                "           \"metrics\": {" +
                "              \"records\":" + dataset01Metrics.getRecords() + "," +
                "              \"size\":" + dataset01Metrics.getSize() + "," +
                "              \"errors\":" + dataset01Metrics.getErrors() +
                "           }" +
                "       },"+
                "       {" +
                "           \"id\": "+DATASET_ID_02+","+
                "           \"metrics\": {" +
                "              \"records\":" + dataset02Metrics.getRecords() + "," +
                "              \"size\":" + dataset02Metrics.getSize() + "," +
                "              \"errors\":" + dataset02Metrics.getErrors() +
                "           }" +
                "       }"+
                "   ]," +
                "   \"SINK\": [" +
                "       {" +
                "           \"id\": "+SINK_ID+","+
                "           \"metrics\": {" +
                "              \"records\":" + sinkMetrics.getRecords() + "," +
                "              \"size\":" + sinkMetrics.getSize() + "," +
                "              \"errors\":" + sinkMetrics.getErrors() +
                "           }" +
                "       }"+
                "   ]" +
                "}," +
                "\"timeMs\":" + TIME_MS + "," +
                "\"createdAt\":1707337340755}";

        FinishedResourceMetricsResponseEvent deserializedFromObject = (FinishedResourceMetricsResponseEvent)
                StreamUtils.jsonUtil().stringToType(StreamUtils.jsonUtil().toJsonString(responseEvent), CoordinationMessage.class);

        FinishedResourceMetricsResponseEvent deserializedFromStr = (FinishedResourceMetricsResponseEvent)
                StreamUtils.jsonUtil().stringToType(responseEventStr, CoordinationMessage.class);

        Assertions.assertThat(responseEvent).isEqualTo(deserializedFromObject);
        Assertions.assertThat(responseEvent).isEqualToIgnoringGivenFields(deserializedFromStr, "createdAt");
        Assertions.assertThat(deserializedFromObject.getData()).isNotNull();
        Assertions.assertThat(deserializedFromStr.getData()).isNotNull();
    }
}
