package com.nexla.connect.common;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.admin.client.flownode.FlowNodeDatasource;
import com.nexla.common.Resource;
import com.nexla.common.ResourceType;
import com.nexla.common.exception.AuthFailException;
import com.nexla.common.exception.NexlaException;
import com.nexla.common.exception.NexlaExceptionChecker;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.connect.common.postponedFlush.PipelineProgressTracker;
import com.nexla.connector.config.SinkConnectorConfig;
import lombok.SneakyThrows;
import org.apache.commons.math3.util.Pair;
import org.joda.time.DateTime;
import org.quartz.CronExpression;

import java.io.File;
import java.text.ParseException;
import java.time.ZoneId;
import java.util.Date;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.connect.common.BaseSinkTask.CRITICAL_FAILURE;

/**
 * Class contains extracted helper functions to check if Sinks needs to be flushed.
 * Potentially can be transformed into different flush strategies in future
 */
public abstract class PostponedFlush {
	public static final String FORCE_FLUSH_FOLDER_PATH = "/tmp/forceflush/";
	private static final long INACTIVITY_SAFETY_THRESHOLD_MINUTES = 30;
	public static final int MAX_DEFAULT_RETRIES = 3;
	private final PipelineProgressTracker pipelineProgressTracker;
	private final Optional<CronExpression> flushCronExpression;
	private final long safeDelayMs;
	private final long inactivityDelayMs;
	private final int sinkId;
	private final NexlaLogger logger;
	private final AdminApiClient adminApiClient;
	private final FlowNodeDatasource dataSource;
	private Optional<Date> nextCronFlushTime;
	private final AtomicInteger failedRetries = new AtomicInteger(0);
	private final AtomicReference<Exception> lastOriginalRetryCause = new AtomicReference<>(null);

	private final Optional<Integer> flushBatchSize;

	private boolean forceFlush;

	private final int firstFlushDelayMin;
	private DateTime firstFlushTsThreshold;

	public PostponedFlush(SinkConnectorConfig sinkConfig, AdminApiClient adminApiClient, PipelineProgressTracker pipelineProgressTracker, NexlaLogger logger) {
		DateTime initTs = nowUTC();
		this.logger = logger;
		this.sinkId = sinkConfig.sinkId;
		this.adminApiClient = adminApiClient;
		this.dataSource = adminApiClient.getFlowByResource(new Resource(sinkId, ResourceType.SINK)).get().getDataSources().get(0);
		this.flushCronExpression = sinkConfig.flushCron.flatMap(this::getCronExpression);
		this.nextCronFlushTime = flushCronExpression.map(x -> x.getNextValidTimeAfter(initTs.toDate()));
		this.pipelineProgressTracker = pipelineProgressTracker;
		this.safeDelayMs = TimeUnit.MINUTES.toMillis(INACTIVITY_SAFETY_THRESHOLD_MINUTES);
		this.inactivityDelayMs = TimeUnit.MINUTES.toMillis(sinkConfig.inactivityTimeoutBeforeFlushMin);
		this.flushBatchSize = sinkConfig.flushBatchSize;
		this.firstFlushTsThreshold = initTs.plusMinutes(sinkConfig.unitTest ? 0 : sinkConfig.firstFlushDelayMin);
		this.forceFlush = false;

		this.firstFlushDelayMin = sinkConfig.firstFlushDelayMin;
		resetFirstFlushTsThreshold();
	}

	public PostponedFlush(BaseSinkTask<?> sinkTask, PipelineProgressTracker pipelineProgressTracker, NexlaLogger logger) {
		this(sinkTask.config, sinkTask.adminApiClient, pipelineProgressTracker, logger);
	}

	/**
	 * If there is no buffered pipeline data - return false
	 * If there is buffered pipeline data - return true if the last record processing happened more than delayMs time ago
	 */
	protected abstract boolean dataMatureMs(long delayMs);

	/**
	 *  Return true if sink has not received new records for the last delayMs time and there is no accumulated data
	 */
	protected abstract boolean noDataMatureMs(long delayMs);

	/**
	 * Returns true if there is buffered data, otherwise - false
	 */
	protected abstract boolean hasAccumulatedRecords();

	/**
	 * Return the amount of accumulated data for stateful sinks.
	 * Used to trigger intermediate flush operations when batch size exceeded.
	 * Do not overload if intermediate flush operation is not triggered by accumulated data amount.
	 */
	protected long intermediateFlushBatchSize() {
		return 0;
	}

	/**
	 * Override to return true if there is a satisfied condition to trigger intermediate flush operation for statefull sinks
	 */
	protected boolean customIntermediateFlushTrigger() {
		return false;
	}

	/**
	 * Override to return true if there is a satisfied condition that must prevent any flush operation
	 */
	protected boolean preventFlushTrigger() {
		return false;
	}

	public void incrementFailures(Exception cause) {
		this.failedRetries.incrementAndGet();
		this.lastOriginalRetryCause.set(cause);
	}

	public void resetFailures() {
		this.failedRetries.set(0);
		this.lastOriginalRetryCause.set(null);
	}

	public void resetFirstFlushTsThreshold() {
		this.firstFlushTsThreshold = nowUTC().plusMinutes(firstFlushDelayMin);
		logger.info("Reset firstFlushTsThreshold to {}", this.firstFlushTsThreshold);
	}

	@SneakyThrows
	public ReadyToFlush readyToFlush() {
		Optional<Exception> originalRetryCause = Optional.ofNullable(this.lastOriginalRetryCause.get());

		// do not retry if 'AuthFailException'
		if(originalRetryCause.isPresent()
				&& NexlaExceptionChecker.checkParentException(originalRetryCause.get(), AuthFailException.class)) {
			logger.warn(String.format("Failed retries due to invalid credentials for sink id [%d]", sinkId));
			throw new NexlaException(
					"Failed retries due to invalid credentials: " + originalRetryCause.get().getMessage(),
					originalRetryCause.get()).setData(CRITICAL_FAILURE, true);
		}

		if (this.failedRetries.get() >= MAX_DEFAULT_RETRIES) {
			logger.warn("Failed retries [{}] exceeded for sink id [{}]", MAX_DEFAULT_RETRIES, sinkId);
			// should always be present, failedRetries do not increment on their own without an exception
			throw new NexlaException(
					"Failed retries exceeded " + MAX_DEFAULT_RETRIES + " due to: " + originalRetryCause.get().getMessage(),
					originalRetryCause.get()).setData(CRITICAL_FAILURE, true);
		}

		if (this.forceFlush) { // before cdc ALTER table we need to flush
			this.forceFlush = false;
			return ReadyToFlush.FLUSH;
		}

		if (preventFlushTrigger()) {
			return ReadyToFlush.NOT_READY;
		}

		if (!(pipelineProgressTracker.isInmemoryPipeline()) && nowUTC().isBefore(firstFlushTsThreshold)) {
			logger.info("Prevent flush operation: first flush timestamp threshold not reached");
			return ReadyToFlush.NOT_READY;
		}

		Pair<Boolean, Boolean> pipelineState = calculatePipelineState();
		Boolean readyToFlush = pipelineState.getFirst();
		Boolean allDataWritten = pipelineState.getSecond();

		boolean flushByBatchSize = flushBatchSize.map(maxBatchSize -> intermediateFlushBatchSize() > maxBatchSize).orElse(false);
		boolean intermediateFlushTrigger = customIntermediateFlushTrigger();

		boolean extraFlushChecks = shouldForceFlush() || shouldCronFlush() || flushByBatchSize || intermediateFlushTrigger;

		boolean shouldFlush = (extraFlushChecks || readyToFlush) && isSinkActive();

		if (extraFlushChecks) {
			logger.debug("Ready to flush: flushByBatchSize={} intermediateFlushTrigger={}", flushByBatchSize, intermediateFlushTrigger);
		}

		return new ReadyToFlush(shouldFlush, shouldFlush && readyToFlush, allDataWritten);
	}

	protected Pair<Boolean, Boolean> calculatePipelineState() {
		boolean streamSource = dataSource.getConnectionType().isStreaming() || !dataSource.getConnectionType().isSourceConnectorType();

		// Use memoization for lazy execution when execution can be avoided
		Supplier<Boolean> sourceFinished = NonThreadSafeLazy.memoize(() -> !streamSource && pipelineProgressTracker.isSourceFinished(dataSource));
		Supplier<Boolean> thereIsNoLag = NonThreadSafeLazy.memoize(() -> pipelineProgressTracker.noMoreRecordsToRead(sinkId));

		// Added dataMatureMs(TimeUnit.MINUTES.toMillis(2)) check in order to reduce the number of lag checks in Kafka
		// this check does not apply to inmemory pipelines
		boolean extraDataMatureCheck = pipelineProgressTracker.isInmemoryPipeline() || dataMatureMs(TimeUnit.MINUTES.toMillis(2));

		boolean timedOutWriters = dataMatureMs(safeDelayMs) ||
				(dataMatureMs(inactivityDelayMs) && extraDataMatureCheck && thereIsNoLag.get());

		Supplier<Boolean> timedOutSink = NonThreadSafeLazy.memoize(() -> noDataMatureMs(safeDelayMs) || (noDataMatureMs(inactivityDelayMs) && sourceFinished.get() && thereIsNoLag.get()));

		boolean flushByTimeout = timedOutWriters && (streamSource || sourceFinished.get());

		Boolean allDataWrittenFlush = dataMatureMs(0) && extraDataMatureCheck && sourceFinished.get() && thereIsNoLag.get();

		// flush data if:
		// 1. SAFE CASE: 30 minutes mature data + there is some data
		// 2. SOURCE STREAMING CASE: inactivityDelayMs (~5 minutes) mature + no lag + source is (streaming or stopped connector)
		// 3. SOURCE CONNECTOR CASE: there is some data + no lag + source is stopped connector

		boolean readyToFlush = flushByTimeout || allDataWrittenFlush;

		logger.debug("Ready to flush: {}: dataMatureMs(0) = {} dataMatureMs(safeDelayMs) = {} dataMatureMs(inactivityDelayMs) = {} " +
						"timedOutWriters = {} thereIsNoLag = {} sourceFinished = {}",
				readyToFlush, dataMatureMs(0), dataMatureMs(safeDelayMs), dataMatureMs(inactivityDelayMs),
				timedOutWriters, thereIsNoLag, sourceFinished);

		return new Pair<>(readyToFlush, allDataWrittenFlush || !hasAccumulatedRecords() && timedOutSink.get());
	}

	public void setForceFlush(boolean forceFlush) {
		this.forceFlush = forceFlush;
	}

	private boolean isSinkActive() {
		boolean isActive = adminApiClient
				.getDataSink(sinkId)
				.map(ds -> ds.getStatus() == ResourceStatus.ACTIVE)
				.orElseGet(() -> {
					logger.warn("Sink {} was not retrieved from the AdminApi. Assuming it is active", sinkId);
					return true;
				});
		if (!isActive) {
			logger.info("Prevent flush operation: sink is not active");
		}
		return isActive;
	}

	private boolean shouldForceFlush() {
		File file = findForceFlushFile(sinkId);
		boolean fileExists = file.exists();
		if (fileExists) {
			logger.info("Force Sink {} to flush. Deleting force flush file", sinkId);
			if (!file.delete()) {
				logger.warn("Failed to delete force flush file {}", file);
			}
		}
		return fileExists;
	}

	private File findForceFlushFile(Integer dataSinkId) {
		return new File(FORCE_FLUSH_FOLDER_PATH.concat(String.valueOf(dataSinkId)));
	}

	private boolean shouldCronFlush() {
		if (nextCronFlushTime.isEmpty()) {
			return false;
		}

		boolean shouldFlush = this.nextCronFlushTime
				.map(scheduledTime -> nowUTC().isAfter(scheduledTime.getTime()))
				.orElse(false);

		if (shouldFlush) {
			this.nextCronFlushTime = this.flushCronExpression.map(x -> x.getNextValidTimeAfter(nowUTC().toDate()));
			logger.info("Flush sink {} by cron, next Flush time: {}", sinkId, nextCronFlushTime);
		}
		return shouldFlush;
	}

	@SneakyThrows
	private Optional<CronExpression> getCronExpression(String c) {
		try {
			CronExpression cron = new CronExpression(c);
			cron.setTimeZone(TimeZone.getTimeZone(ZoneId.of("UTC")));
			return Optional.of(cron);
		} catch (ParseException e) {
			logger.warn("Could not parse cron expression {} of sink {}, returning empty", c, sinkId, e);
			return Optional.empty();
		}
	}

}
