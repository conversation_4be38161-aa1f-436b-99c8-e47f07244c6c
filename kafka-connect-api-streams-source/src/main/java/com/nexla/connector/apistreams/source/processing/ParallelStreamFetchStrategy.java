package com.nexla.connector.apistreams.source.processing;


import com.google.common.collect.Lists;
import com.nexla.apistreams.spec.BaseEltStream;
import com.nexla.connect.common.SourceRecordCreator;
import org.apache.commons.collections4.CollectionUtils;
import scala.util.Left;
import scala.util.Right;

import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

public class ParallelStreamFetchStrategy implements StreamFetchStrategy {

  private final Queue<BaseEltStream> streamsQueue;

  private final int maxParallelStreams;

  public ParallelStreamFetchStrategy(List<BaseEltStream> streams, int maxParallelStreams) {
    this.streamsQueue = Lists.newLinkedList(streams);
    this.maxParallelStreams = maxParallelStreams;
  }

  @Override
  public StreamFetchResult iterate() {
    List<BaseEltStream> streams = getNextStreamBatch();
    if (CollectionUtils.isNotEmpty(streams)) {
      return executeStreamsAsync(streams);
    }
    return null;
  }

  private synchronized List<BaseEltStream> getNextStreamBatch() {
    int index = 0;
    List<BaseEltStream> streams = Lists.newArrayList();
    while (index++ < maxParallelStreams) {
      if (streamsQueue.isEmpty()){
        break;
      }
      streams.add(streamsQueue.poll());
    }
    return streams;
  }

  private StreamFetchResult executeStreamsAsync(List<BaseEltStream> currentStreams) {
    Map<String,List<SourceRecordCreator>> sources = new ConcurrentHashMap<>();
    Map<String,List<StreamMetricResult>> metrics = new ConcurrentHashMap<>();

    for (BaseEltStream stream : currentStreams) {
      sources.put(stream.getStreamName(), new CopyOnWriteArrayList<>()); // todo replace with ConcurrentLinkedQueue? Would be faster
      metrics.put(stream.getStreamName(), new CopyOnWriteArrayList<>());
    }

    CompletableFuture.allOf(currentStreams
      .stream()
      .map(stream -> CompletableFuture.supplyAsync(() -> {
        try {
          List<SourceRecordCreator> records = stream.execute();
          return new Left<>(records);
        } catch (Exception e) {
          return new Right<>(e.getMessage());
        }
      })
      .whenComplete((either, ex) -> {
        if (either.isLeft()) {
          List<SourceRecordCreator> records = (List<SourceRecordCreator>) either.left().get();
          sources.get(stream.getStreamName()).addAll(records);
          stream.getLastResult().ifPresent(lastResult -> {
            metrics.get(stream.getStreamName()).add(new StreamMetricResult(lastResult, records.size(), 0, null, stream.getStreamName()));
            if (!records.isEmpty()){
              streamsQueue.add(stream);
            }
          });
        } else {
          metrics.get(stream.getStreamName()).add(new StreamMetricResult(null, 0, 1, (String) either.right().get(), stream.getStreamName()));
        }
      }))
      .toArray(CompletableFuture[]::new))
      .join();

    return new StreamFetchResult(sources, metrics);
  }
}
