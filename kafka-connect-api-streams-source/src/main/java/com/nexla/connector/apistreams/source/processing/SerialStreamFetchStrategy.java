package com.nexla.connector.apistreams.source.processing;


import com.nexla.apistreams.spec.BaseEltStream;
import com.nexla.connect.common.SourceRecordCreator;

import java.util.*;

public class SerialStreamFetchStrategy implements StreamFetchStrategy {

  private final Queue<BaseEltStream> streamsQueue;

  public SerialStreamFetchStrategy(List<BaseEltStream> streams) {
    this.streamsQueue = new LinkedList<>();
    streamsQueue.addAll(streams);
  }

  @Override
  public StreamFetchResult iterate() {
    BaseEltStream currentStream = streamsQueue.poll();
    if (Objects.nonNull(currentStream)) {
      try {
        List<SourceRecordCreator> records = currentStream.execute();

        List<StreamMetricResult> streamMetricResult = currentStream.getLastResult()
          .map(it -> new StreamMetricResult(it, records.size(), 0, null, currentStream.getStreamName()))
          .map(List::of)
          .orElse(List.of());

        if (!records.isEmpty()) {
          currentStream.getLastResult().ifPresent(lastResult -> streamsQueue.add(currentStream));
        }

        return new StreamFetchResult(Map.of(currentStream.getStreamName(), records), Map.of(currentStream.getStreamName(), streamMetricResult));
      } catch (Exception e) {
        return new StreamFetchResult(Map.of(currentStream.getStreamName(), List.of()), Map.of(currentStream.getStreamName(), List.of(new StreamMetricResult(null, 0, 1, e.getMessage(), currentStream.getStreamName()))));
      }
    }
    return null;
  }
}
