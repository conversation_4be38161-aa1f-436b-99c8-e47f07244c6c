package com.nexla.connector.apistreams.source.processing;

import com.nexla.rest.pojo.RestIterationResult;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.UUID;

@EqualsAndHashCode
@Getter
public class StreamMetricResult {

  private final UUID unique;
  private final RestIterationResult result;
  private final Integer recordsCount;
  private final Integer errorCount;
  private final String errorMessage;

  private final String streamName;

  public StreamMetricResult(RestIterationResult result, Integer recordsCount, Integer  errorCount, String errorMessage, String streamName) {
    this.unique = UUID.randomUUID();
    this.result = result;
    this.recordsCount = recordsCount;
    this.errorCount = errorCount;
    this.errorMessage = errorMessage;
    this.streamName = streamName;
  }
}
