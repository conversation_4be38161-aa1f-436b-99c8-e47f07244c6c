package com.nexla.connector.apistreams.source.processing;

import com.nexla.connect.common.SourceRecordCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public class StreamFetchResult {

  private final Map<String, List<SourceRecordCreator>> records;

  private final Map<String, List<StreamMetricResult>> metricResult;

  public int getRecordCount() {
    return records.values().stream().mapToInt(List::size).sum();
  }
}
