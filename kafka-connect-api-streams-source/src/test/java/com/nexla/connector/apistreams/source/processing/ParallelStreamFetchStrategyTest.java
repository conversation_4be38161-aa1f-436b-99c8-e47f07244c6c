package com.nexla.connector.apistreams.source.processing;

import com.nexla.apistreams.spec.BaseEltStream;
import com.nexla.connect.common.SourceRecordCreator;
import com.nexla.rest.pojo.RestIterationResult;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

public class ParallelStreamFetchStrategyTest {

  @Test
  public void testIterateValidatingSequence() {
    var sourceRecordCreatorMock = Mockito.mock(SourceRecordCreator.class);
    var restIterationResultMock = Mockito.mock(RestIterationResult.class);
    var stream1 = Mockito.mock(BaseEltStream.class);
    var stream2 = Mockito.mock(BaseEltStream.class);
    var stream3 = Mockito.mock(BaseEltStream.class);

    when(stream1.execute()).thenReturn(List.of(sourceRecordCreatorMock, sourceRecordCreatorMock, sourceRecordCreatorMock));
    when(stream1.getStreamName()).thenReturn("stream1");
    when(stream1.getLastResult()).thenReturn(Optional.of(restIterationResultMock));
    when(stream2.execute()).thenReturn(List.of(sourceRecordCreatorMock));
    when(stream2.getStreamName()).thenReturn("stream2");
    when(stream2.getLastResult()).thenReturn(Optional.of(restIterationResultMock));
    when(stream3.execute()).thenReturn(List.of(sourceRecordCreatorMock, sourceRecordCreatorMock));
    when(stream3.getStreamName()).thenReturn("stream3");
    when(stream3.getLastResult()).thenReturn(Optional.of(restIterationResultMock));

    var fetchStrategy = new ParallelStreamFetchStrategy(List.of(stream1, stream2, stream3), 1);

    var result1 = fetchStrategy.iterate();
    assertNotNull(result1);
    assertEquals(3, result1.getRecords().get("stream1").size());
    assertEquals(1, result1.getMetricResult().get("stream1").size());
    assertEquals("stream1", result1.getMetricResult().get("stream1").get(0).getStreamName());
    assertEquals(Integer.valueOf(3), result1.getMetricResult().get("stream1").get(0).getRecordsCount());

    var result2 = fetchStrategy.iterate();
    assertNotNull(result2);
    assertEquals(1, result2.getRecords().get("stream2").size());
    assertEquals(1, result2.getMetricResult().get("stream2").size());
    assertEquals("stream2", result2.getMetricResult().get("stream2").get(0).getStreamName());
    assertEquals(Integer.valueOf(1), result2.getMetricResult().get("stream2").get(0).getRecordsCount());

    var result3 = fetchStrategy.iterate();
    assertNotNull(result3);
    assertEquals(2, result3.getRecords().get("stream3").size());
    assertEquals(1, result3.getMetricResult().get("stream3").size());
    assertEquals("stream3", result3.getMetricResult().get("stream3").get(0).getStreamName());
    assertEquals(Integer.valueOf(2), result3.getMetricResult().get("stream3").get(0).getRecordsCount());

    when(stream1.execute()).thenReturn(List.of());
    when(stream1.getLastResult()).thenReturn(Optional.empty());
    when(stream2.execute()).thenReturn(List.of());
    when(stream2.getLastResult()).thenReturn(Optional.empty());
    when(stream3.execute()).thenReturn(List.of());
    when(stream3.getLastResult()).thenReturn(Optional.empty());

    var result4 = fetchStrategy.iterate();
    assertNotNull(result4);
    assertEquals(0, result4.getRecords().get("stream1").size());
    assertEquals(0, result4.getMetricResult().get("stream1").size());

    var result5 = fetchStrategy.iterate();
    assertNotNull(result5);
    assertEquals(0, result5.getRecords().get("stream2").size());
    assertEquals(0, result5.getMetricResult().get("stream2").size());

    var result6 = fetchStrategy.iterate();
    assertNotNull(result6);
    assertEquals(0, result6.getRecords().get("stream3").size());
    assertEquals(0, result6.getMetricResult().get("stream3").size());

    var result7 = fetchStrategy.iterate();
    assertNull(result7);
  }

  @Test
  public void testIterateValidatingGrouping() {
    var sourceRecordCreatorMock = Mockito.mock(SourceRecordCreator.class);
    var restIterationResultMock = Mockito.mock(RestIterationResult.class);
    var stream1 = Mockito.mock(BaseEltStream.class);
    var stream2 = Mockito.mock(BaseEltStream.class);
    var stream3 = Mockito.mock(BaseEltStream.class);

    when(stream1.execute()).thenReturn(List.of(sourceRecordCreatorMock, sourceRecordCreatorMock, sourceRecordCreatorMock));
    when(stream1.getStreamName()).thenReturn("stream1");
    when(stream1.getLastResult()).thenReturn(Optional.of(restIterationResultMock));
    when(stream2.execute()).thenReturn(List.of(sourceRecordCreatorMock));
    when(stream2.getStreamName()).thenReturn("stream2");
    when(stream2.getLastResult()).thenReturn(Optional.of(restIterationResultMock));
    when(stream3.execute()).thenReturn(List.of(sourceRecordCreatorMock, sourceRecordCreatorMock));
    when(stream3.getStreamName()).thenReturn("stream3");
    when(stream3.getLastResult()).thenReturn(Optional.of(restIterationResultMock));

    var fetchStrategy = new ParallelStreamFetchStrategy(List.of(stream1, stream2, stream3), 2);

    var result1 = fetchStrategy.iterate();
    assertNotNull(result1);
    assertEquals(4, result1.getRecords().values().stream().mapToInt(List::size).sum());
    assertEquals(2, result1.getMetricResult().values().stream().mapToInt(List::size).sum());

    when(stream1.execute()).thenReturn(List.of());
    when(stream1.getLastResult()).thenReturn(Optional.empty());
    when(stream2.execute()).thenReturn(List.of());
    when(stream2.getLastResult()).thenReturn(Optional.empty());

    var result2 = fetchStrategy.iterate();
    assertNotNull(result2);
    assertEquals(2, result2.getRecords().values().stream().mapToInt(List::size).sum());
    assertEquals(1, result2.getMetricResult().values().stream().mapToInt(List::size).sum());

    when(stream3.execute()).thenReturn(List.of());
    when(stream3.getLastResult()).thenReturn(Optional.empty());

    var result3 = fetchStrategy.iterate();
    assertNotNull(result3);
    assertEquals(0, result3.getRecords().values().stream().mapToInt(List::size).sum());
    assertEquals(0, result3.getMetricResult().values().stream().mapToInt(List::size).sum());

    var result4 = fetchStrategy.iterate();
    assertNull(result4);
  }
}