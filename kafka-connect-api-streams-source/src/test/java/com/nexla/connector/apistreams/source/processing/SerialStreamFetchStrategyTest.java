package com.nexla.connector.apistreams.source.processing;

import com.nexla.apistreams.spec.BaseEltStream;
import com.nexla.connect.common.SourceRecordCreator;
import com.nexla.rest.pojo.RestIterationResult;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

public class SerialStreamFetchStrategyTest {

  @Test
  public void testIterate() {
    var sourceRecordCreatorMock = Mockito.mock(SourceRecordCreator.class);
    var restIterationResultMock = Mockito.mock(RestIterationResult.class);
    var stream1 = Mockito.mock(BaseEltStream.class);
    var stream2 = Mockito.mock(BaseEltStream.class);
    var stream3 = Mockito.mock(BaseEltStream.class);
    var stream4 = Mockito.mock(BaseEltStream.class);

    when(stream1.execute()).thenReturn(List.of(sourceRecordCreatorMock, sourceRecordCreatorMock, sourceRecordCreatorMock));
    when(stream1.getStreamName()).thenReturn("stream1");
    when(stream1.getLastResult()).thenReturn(Optional.of(restIterationResultMock));
    when(stream2.execute()).thenReturn(List.of(sourceRecordCreatorMock));
    when(stream2.getStreamName()).thenReturn("stream2");
    when(stream2.getLastResult()).thenReturn(Optional.of(restIterationResultMock));
    when(stream3.execute()).thenReturn(List.of(sourceRecordCreatorMock, sourceRecordCreatorMock));
    when(stream3.getStreamName()).thenReturn("stream3");
    when(stream3.getLastResult()).thenReturn(Optional.of(restIterationResultMock));
    when(stream4.execute()).thenReturn(List.of());
    when(stream4.getStreamName()).thenReturn("stream4");
    when(stream4.getLastResult()).thenReturn(Optional.empty());

    var fetchStrategy = new SerialStreamFetchStrategy(List.of(stream1, stream2, stream3, stream4));

    var result1 = fetchStrategy.iterate();
    verifyResult(result1, stream1.getStreamName(), 3, 1);

    when(stream1.execute()).thenReturn(List.of());
    when(stream1.getLastResult()).thenReturn(Optional.empty());

    var result2 = fetchStrategy.iterate();
    verifyResult(result2, stream2.getStreamName(), 1, 1);

    when(stream2.execute()).thenReturn(List.of());
    when(stream2.getLastResult()).thenReturn(Optional.empty());

    var result3 = fetchStrategy.iterate();
    verifyResult(result3, stream3.getStreamName(), 2, 1);

    when(stream3.execute()).thenReturn(List.of());
    when(stream3.getLastResult()).thenReturn(Optional.empty());

    var result4 = fetchStrategy.iterate();
    verifyResult(result4, stream4.getStreamName(), 0, 0);

    var result5 = fetchStrategy.iterate();
    verifyResult(result5, stream4.getStreamName(), 0, 0);

    var result6 = fetchStrategy.iterate();
    verifyResult(result6, stream4.getStreamName(), 0, 0);

    var result7 = fetchStrategy.iterate();
    verifyResult(result7, stream4.getStreamName(), 0, 0);

    var result8 = fetchStrategy.iterate();
    assertNull(result8);
  }

  private void verifyResult(StreamFetchResult result, String streamName, int recordsSize, int metricsResultSize) {
    assertNotNull(result);
    assertEquals(recordsSize, result.getRecords().values().stream().mapToInt(List::size).sum());
    assertEquals(metricsResultSize, result.getMetricResult().values().stream().mapToInt(List::size).sum());
    if (metricsResultSize > 0) {
      assertEquals(Integer.valueOf(recordsSize), result.getMetricResult().get(streamName).get(0).getRecordsCount());
      assertEquals(streamName, result.getMetricResult().get(streamName).get(0).getStreamName());
    }
  }
}