package com.nexla.client;

import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.nexla.common.Resource;
import com.nexla.common.StreamUtils;
import com.nexla.common.VersionComparator;
import com.nexla.common.logging.TimeLogger;
import com.nexla.listing.client.HealthResponse;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;
import java.util.concurrent.ExecutionException;

import static com.nexla.common.AppUtils.authorizationHeader;
import static com.nexla.listing.client.ListingClient.DEFAULT_VERSION;
import static java.util.concurrent.TimeUnit.MINUTES;
import static java.util.concurrent.TimeUnit.SECONDS;
import static org.springframework.http.HttpMethod.GET;
import static org.springframework.http.HttpMethod.POST;
import static org.springframework.web.util.UriComponentsBuilder.fromHttpUrl;

public class JavaJobSchedulerClient {
	private static final Logger LOGGER = LoggerFactory.getLogger(JavaJobSchedulerClient.class);
	private static final String ALL_SERVICES_KEY = "";

	private final HttpHeaders authorizationHeader;
	private final String ctrlServer;
	private final RestTemplate restTemplate;
	private final Supplier<String> version;

	// Cache contains 1 entry ONLY for all services list
	public final LoadingCache<String, Set<String>> runningContainersCache;

	public JavaJobSchedulerClient(
		String ctrlServer,
		String nexlaUsername,
		String nexlaPassword,
		RestTemplate restTemplate
	) {
		this.restTemplate = restTemplate;
		this.ctrlServer = ctrlServer;
		this.authorizationHeader = authorizationHeader(nexlaUsername, nexlaPassword);
		this.version = Suppliers.memoizeWithExpiration(() -> getVersionRetry(ctrlServer, restTemplate), 5, MINUTES);
		this.runningContainersCache = CacheBuilder
				.newBuilder()
				.expireAfterWrite(150, SECONDS)
				.refreshAfterWrite(1, MINUTES)
				.build(new CacheLoader<>() {
					@Override
					public Set<String> load(String key) {
						try (TimeLogger ignored = new TimeLogger(LOGGER, "Refresh running services cache")) {
							return restTemplate
									.exchange(
											ctrlServer + "/containers_cache/running",
											GET,
											new HttpEntity<>(authorizationHeader),
											new ParameterizedTypeReference<Set<String>>() {})
									.getBody();
						}
					}
				});
	}

	@SneakyThrows
	private String getVersionRetry(String server, RestTemplate restTemplate) {
		try {
			HttpEntity request = new HttpEntity<>(authorizationHeader);
			UriComponentsBuilder url = fromHttpUrl(server + "/health");

			HealthResponse body = restTemplate.exchange(url.toUriString(), GET, request, HealthResponse.class)
				.getBody();

			LOGGER.info("Got version: " + StreamUtils.jsonUtil().toJsonString(body));

			return body.getVersion().orElse(DEFAULT_VERSION);
		} catch (Exception e) {
			return DEFAULT_VERSION;
		}
	}

	public boolean isServiceRunning(String serviceName) throws ExecutionException {
		return VersionComparator.isVersionGE(getVersion(), "2.12.0")
				? runningContainersCache.get(ALL_SERVICES_KEY).contains(serviceName)
				: isServiceRunning_2_11(serviceName);
	}

	@Deprecated(since = "release/v2.12.0")
	public boolean isServiceRunning_2_11(String serviceName) {
		HttpEntity request = new HttpEntity<>(authorizationHeader);
		Map map = restTemplate.exchange(ctrlServer + "/container_status/" + serviceName, GET, request, Map.class).getBody();
		return Boolean.parseBoolean(map.get("status").toString());
	}

	public void heartbeatConnector(Resource resource, Long runId, boolean trace) {
		List<String> path = Arrays.asList(ctrlServer,
			"connector",
			"heartbeat",
			resource.type.toString(),
			String.valueOf(resource.id),
			String.valueOf(runId));

		String url = StreamEx.of(path).joining("/");

		try {
			HttpEntity request = new HttpEntity<>(authorizationHeader);
			restTemplate.exchange(url + "?trace=" + trace, POST, request, String.class);
		} catch (Exception e) {
			LOGGER.error("", e);
		}
	}

	public String getVersion() {
		return version.get();
	}

}
