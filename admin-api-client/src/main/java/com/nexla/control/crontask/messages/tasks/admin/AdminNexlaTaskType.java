package com.nexla.control.crontask.messages.tasks.admin;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum AdminNexlaTaskType {

	LISTING_TOKEN_REFRESH,
	START_SOURCE_TASK,
	SCHEMA_DETECTION_TASK,
	WRITE_DONE_TRIGGER
	;

	@SuppressWarnings("unused")
	@JsonCreator
	public static AdminNexlaTaskType fromString(String string) {
		return AdminNexlaTaskType.valueOf(string.toUpperCase());
	}

}
