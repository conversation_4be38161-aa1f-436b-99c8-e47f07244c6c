package com.nexla.control.crontask.messages.tasks.admin;

import com.nexla.admin.client.DataCredentials;
import lombok.Data;

import java.util.List;

import static com.nexla.control.crontask.messages.tasks.admin.AdminNexlaTaskType.LISTING_TOKEN_REFRESH;


@Data
public class TokenRefreshTask implements AdminRequestTask {

	private final List<DataCredentials> dataCredentials;

	private final String messageId;
	private final Long timestamp;

}
