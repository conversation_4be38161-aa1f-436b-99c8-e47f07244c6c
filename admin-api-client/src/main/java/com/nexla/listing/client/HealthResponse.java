package com.nexla.listing.client;

import com.nexla.control.health.ExpectedHealthEvent;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Data
public class HealthResponse {

    private final Optional<String> version;

    private final Optional<List<ExpectedHealthEvent>> events;

    private final Optional<Map<String, Set<String>>> monitoredConsumerGroups;

}
