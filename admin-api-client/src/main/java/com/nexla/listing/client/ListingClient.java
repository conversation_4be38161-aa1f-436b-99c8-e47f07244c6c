package com.nexla.listing.client;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.google.common.collect.Sets;
import com.nexla.FileDescription;
import com.nexla.common.*;
import com.nexla.common.NexlaFile.FileSourceType;
import com.nexla.control.ListingFileStatus;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

import static com.nexla.common.AppUtils.authorizationHeader;
import static com.nexla.common.ConnectionType.API_MULTI;
import static com.nexla.common.ConnectionType.BIGQUERY;
import static com.nexla.common.ConnectionType.BOX;
import static com.nexla.common.ConnectionType.DROPBOX;
import static com.nexla.common.ConnectionType.GCS;
import static com.nexla.common.ConnectionType.GDRIVE;
import static com.nexla.common.ConnectionType.ONEDRIVE;
import static com.nexla.common.ConnectionType.REST;
import static com.nexla.common.ConnectionType.SHAREPOINT;
import static com.nexla.common.ConverterUtils.toInteger;
import static com.nexla.common.ConverterUtils.toLong;
import static com.nexla.common.ListingResourceType.FILE;
import static java.util.Collections.singletonMap;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.joining;
import static org.springframework.http.HttpMethod.*;
import static org.springframework.http.HttpStatus.NO_CONTENT;
import static org.springframework.util.StringUtils.isEmpty;
import static org.springframework.web.util.UriComponentsBuilder.fromHttpUrl;

public class ListingClient {

	public static final Set<ConnectionType> REFRESHABLE_TYPES = Sets.newHashSet(REST, BOX, GCS, BIGQUERY, GDRIVE, DROPBOX, SHAREPOINT, ONEDRIVE, API_MULTI);

	public static final String DEFAULT_VERSION = "0.0.1";
	private final static Logger LOGGER = LoggerFactory.getLogger(ListingClient.class);
	private final static ParameterizedTypeReference<ListingScheduleResponse> LISTING_SCHEDULE_RESPONSE = new ParameterizedTypeReference<>() {};

	private final HttpHeaders authorizationHeader;
	private final String listingAppServer;
	private final RestTemplate restTemplate;
	private final Supplier<String> version;

	private final static int LISTING_RETRY_SLEEP_TIME = 5;
	private final static int LISTING_RETRY_ATTEMPT_NUMBER = 30;

	private final static Retryer<ResponseEntity<String>> LISTING_RETRYER = RetryerBuilder.<ResponseEntity<String>>newBuilder()
		.retryIfException()
		.withWaitStrategy(WaitStrategies.fixedWait(LISTING_RETRY_SLEEP_TIME, TimeUnit.SECONDS))
		.withStopStrategy(StopStrategies.stopAfterAttempt(LISTING_RETRY_ATTEMPT_NUMBER))
		.build();

	private final static Retryer<ResponseEntity<List<Map<String, String>>>> LISTING_METADATA_RETRYER = RetryerBuilder.<ResponseEntity<List<Map<String, String>>>>newBuilder()
		.retryIfException()
		.withWaitStrategy(WaitStrategies.fixedWait(LISTING_RETRY_SLEEP_TIME, TimeUnit.SECONDS))
		.withStopStrategy(StopStrategies.stopAfterAttempt(LISTING_RETRY_ATTEMPT_NUMBER))
		.build();

	private final static Retryer<ResponseEntity<List<ListedFile>>> LISTING_FILES_RETRYER = RetryerBuilder.<ResponseEntity<List<ListedFile>>>newBuilder()
		.retryIfException()
		.withWaitStrategy(WaitStrategies.fixedWait(LISTING_RETRY_SLEEP_TIME, TimeUnit.SECONDS))
		.withStopStrategy(StopStrategies.stopAfterAttempt(LISTING_RETRY_ATTEMPT_NUMBER))
		.build();

	private final static Retryer<ListingScheduleResponse> LISTING_ONCE_RETRY = RetryerBuilder.<ListingScheduleResponse>newBuilder()
		.retryIfException()
		.withWaitStrategy(WaitStrategies.fixedWait(5, TimeUnit.SECONDS))
		// should retry and stop after 2nd attempt
		.withStopStrategy(StopStrategies.stopAfterAttempt(2))
		.build();

	public ListingClient(
		String listingAppServer,
		String nexlaUsername,
		String nexlaPassword,
		RestTemplate restTemplate
	) {
		this.restTemplate = restTemplate;
		this.listingAppServer = listingAppServer;
		this.authorizationHeader = authorizationHeader(nexlaUsername, nexlaPassword);
		this.version = Suppliers.memoize(() -> getVersionRetry(listingAppServer, restTemplate));
	}

	@SneakyThrows
	private String getVersionRetry(String server, RestTemplate restTemplate) {
		try {
			HttpEntity request = new HttpEntity<>(authorizationHeader);
			UriComponentsBuilder url = fromHttpUrl(server + "/health");

			HealthResponse body = restTemplate.exchange(url.toUriString(), GET, request, HealthResponse.class)
				.getBody();

			LOGGER.info("Got version: " + StreamUtils.jsonUtil().toJsonString(body));

			return body.getVersion().orElse(DEFAULT_VERSION);
		} catch (Exception e) {
			return DEFAULT_VERSION;
		}
	}

	@SneakyThrows
	public Map<String, Map> getFileMetadata(int sourceId, Set<String> fullPaths, Optional<Integer> page, Optional<Integer> pageSize) {
		Map<String, Object> params = Map.of("page", page.orElse(1), "size", pageSize.orElse(100));
		HttpEntity request = new HttpEntity<>(singletonMap("filePaths", fullPaths), authorizationHeader);
		// this is a POST because the list of paths can be lots of characters
		ResponseEntity<List<Map<String, String>>> exchange = LISTING_METADATA_RETRYER.call(() -> restTemplate.exchange(
			String.format("%s/files/%d/metadata", listingAppServer, sourceId), POST, request,
			new ParameterizedTypeReference<List<Map<String, String>>>() {
			}, params));
		List<Map<String, String>> body = exchange.getBody();
		return StreamEx.of(body)
			.filter(v -> v.get("metadata") != null)
			.toMap(
				k -> k.get("name"),
				v -> JsonUtils.stringToType(v.get("metadata"), Map.class),
				(map1, map2) -> {
					// SHOULD NOT happen normally.
					LOGGER.warn("Returned metadata for two files with the same file path (id1: {}, id2: {}), sourceId: {}", map1.get("id"), map2.get("id"), sourceId);
					return map2;
				});
	}

	@SneakyThrows
	public List<ListedFile> listFiles(
		int sourceId,
		Optional<FileSourceType> sourceType,
		Set<ListingFileStatus> statuses,
		Optional<Boolean> original,
		Optional<Integer> pageNumber,
		Optional<Integer> pageSize,
		Optional<LocalDateTime> createdAtFrom,
		Optional<LocalDateTime> createdAtTo
	) {
		HttpEntity request = new HttpEntity<>(authorizationHeader);
		ParameterizedTypeReference<List<ListedFile>> ref = new ParameterizedTypeReference<List<ListedFile>>() {
		};

		Optional<String> statusesOpt = ofNullable(statuses)
			.filter(CollectionUtils::isNotEmpty)
			.map(s -> s.stream().map(v -> "status=" + v).collect(joining("&")));

		String params = Stream
			.of(
				sourceType.map(v -> "source=" + v.name()),
				statusesOpt,
				original.map(v -> "original=" + v),
				pageNumber.map(v -> "page=" + v),
				pageSize.map(v -> "size=" + v),
				createdAtFrom.map(v -> "createdAtFrom=" + v),
				createdAtTo.map(v -> "createdAtTo=" + v)
			)
			.filter(Optional::isPresent)
			.map(Optional::get)
			.collect(joining("&"));

		return LISTING_FILES_RETRYER.call(() -> restTemplate.exchange(
				listingAppServer + "/files/" + sourceId + (isEmpty(params) ? "" : "?" + params),
				GET, request, ref))
			.getBody();
	}

	@SneakyThrows
	public List<TmpFile> listTmpFiles(
		Resource resource,
		Long upstreamId
	) {
		HttpEntity<?> request = new HttpEntity<>(authorizationHeader);

		String params = Stream
			.of(
				Optional.ofNullable(resource).map(v -> "resource_id=" + v.id),
				Optional.ofNullable(resource).map(v -> "resource_type=" + v.type.name()),
				Optional.ofNullable(upstreamId).map(v -> "origin_listing_file_id=" + v)
			)
			.filter(Optional::isPresent)
			.map(Optional::get)
			.collect(joining("&"));

		ResponseEntity<List<TmpFile>> entity = restTemplate.exchange(
				listingAppServer + "/tmp_files" + (StringUtils.isBlank(params) ? "" : "?" + params),
				GET, request,
				new ParameterizedTypeReference<List<TmpFile>>() {}
		);

		return entity.getBody();
	}

	public void heartBeat(int sourceId, long fileId) {
		HttpEntity request = new HttpEntity<>(authorizationHeader);
		UriComponentsBuilder url = fromHttpUrl(listingAppServer + "/files/" + sourceId + "/file/" + fileId + "/heartbeat");

		retry(url.toUriString(), POST, request);
	}

	@SneakyThrows
	public void setFileStatus(int sourceId, long fileId, ListingFileStatus status, Optional<Long> lastMessageOffset, Optional<String> message) {
		this.setFileStatus(sourceId, fileId, status, lastMessageOffset, of(false), message);
	}

	@SneakyThrows
	public void setFileStatus(int sourceId, long fileId, ListingFileStatus status, Optional<Long> lastMessageOffset, Optional<Boolean> schemaDetectionAttempted, Optional<String> message) {
		HttpEntity request = new HttpEntity<>(authorizationHeader);
		UriComponentsBuilder url = fromHttpUrl(listingAppServer + "/files/" + sourceId + "/file/" + fileId + "/status/" + status.name().toLowerCase());

		lastMessageOffset.ifPresent(offset -> url.queryParam("lastMessageOffset", offset));
		schemaDetectionAttempted.ifPresent(attempted -> url.queryParam("schemaDetectionAttempted", attempted));
		message.ifPresent(m -> url.queryParam("message", m));

		retry(url.toUriString(), POST, request);;
	}

	@SneakyThrows
	public void externalArchiveListing(int sourceId, long fileId, List<FileDescription> files) {
		String resuestBody = JsonUtils.toJsonString(files);
		HttpEntity request = new HttpEntity<>(resuestBody, authorizationHeader);

		UriComponentsBuilder url = fromHttpUrl(listingAppServer + "/files/" + sourceId + "/update_archive/" + fileId);
		retry(url.toUriString(), POST, request);
	}

	public Optional<List<MinimalNexlaFile>> takeFiles(int sourceId, int howMany) {
		Long start = System.currentTimeMillis();
		HttpEntity request = new HttpEntity<>(this.authorizationHeader);
		ResponseEntity<String> fileResponse = this.retry(this.listingAppServer + "/files/" + sourceId + "/takeMany/" + howMany, HttpMethod.POST, request);

		if (fileResponse.getStatusCode() == HttpStatus.NO_CONTENT) {
			return Optional.empty();
		} else {
			List<Object> dataForAllFiles = JsonUtils.jsonToList(fileResponse.getBody());
			List<MinimalNexlaFile> results = new ArrayList();
			Iterator dataForAllFilesIterator = dataForAllFiles.iterator();

			while (dataForAllFilesIterator.hasNext()) {
				Object singleFile = dataForAllFilesIterator.next();
				Map<String, Object> singleFileEntry = (Map)singleFile;
				results.add(this.toMinimalNexlaFile(singleFileEntry));
			}
			Long finish = System.currentTimeMillis();
			LOGGER.info("Taking [{}] files for source [{}] took {} ms.", howMany, sourceId, finish - start);
			return Optional.of(results);
		}
	}

	private MinimalNexlaFile toMinimalNexlaFile(Map<String, Object> data) {
		MinimalNexlaFile file = new MinimalNexlaFile();
		file.setId(toLong(data.get("id")));
		file.setFullPath(data.get("fullPath").toString());
		return file;
	}

	public ListingResult takeFile(int sourceId) {
		try {
			HttpEntity request = new HttpEntity<>(authorizationHeader);
			ResponseEntity<String> fileResponse = retry(listingAppServer + "/files/" + sourceId + "/take", POST, request);

			Boolean headerListingInProgress = Boolean.valueOf(
				fileResponse
					.getHeaders()
					.toSingleValueMap()
					.getOrDefault(NexlaConstants.HEADER_LISTING_IN_PROGRESS, "false")
			);

			if (fileResponse.getStatusCode() == NO_CONTENT) {
				return new ListingResult(Optional.empty(), headerListingInProgress);
			} else {
				Map<String, Object> data = JsonUtils.jsonToMap(fileResponse.getBody());
				return new ListingResult(of(toNexlaFile(data)), headerListingInProgress);
			}
		} catch (Exception e) {
			LOGGER.info("Error during listing communication, considering Listing is in progress", e);
			return new ListingResult(Optional.empty(), true);
		}
	}

	private NexlaFile toNexlaFile(Map<String, Object> data) {
		NexlaFile file = new NexlaFile(
			toLong(data.get("id")),
			toLong(data.get("linkToOriginal")),
			FileSourceType.valueOf(data.get("source").toString()),
			data.get("fullPath").toString(),
			toLong(data.get("size")),
			"",
			data.get("hash") != null ? data.get("hash").toString() : null,
			null,
			toLong(data.get("lastModified")),
			FILE);

		file.setLastMessageOffset(ofNullable(data.get("lastMessageOffset")).map(ConverterUtils::toLong));
		file.setMetadata(ofNullable(data.get("metadata")).map(m -> (Map<String, Object>) m));

		return file;
	}

	/**
	 * Attempts to retrieve an Adaptive Flow Task from the service.
	 * Uses POST {serverURL including port}/tasks/take
	 *
	 * @param sourceId The ID of the source for which to retrieve a task.
	 * @return An {@link AdaptiveFlowTaskResult} containing an {@link Optional}
	 * of AdaptiveFlowTask if a task is successfully retrieved, or an empty {@link Optional} if no task
	 * is available or an error occurs.
	 */
	public AdaptiveFlowTaskResult takeAdaptiveFlowTask(int sourceId) {
		try {
			HttpEntity request = new HttpEntity<>(authorizationHeader);
			ResponseEntity<String> fileResponse = retry(listingAppServer + "/adaptive_flow_tasks/" + sourceId + "/take", POST, request);

			Boolean maintenanceInProgress = Boolean.valueOf(
				fileResponse
					.getHeaders()
					.toSingleValueMap()
					.getOrDefault(NexlaConstants.HEADER_LISTING_IN_PROGRESS, "false")
			);

			if (fileResponse.getStatusCode() == NO_CONTENT) {
				return new AdaptiveFlowTaskResult(Optional.empty(), maintenanceInProgress);
			} else {
				Map<String, Object> data = JsonUtils.jsonToMap(fileResponse.getBody());
				return new AdaptiveFlowTaskResult(of(toAdaptiveFlowTask(data)), maintenanceInProgress);
			}
		} catch (Exception e) {
			LOGGER.info("Error during adaptive flow tasks service communication, considering templated flow tasks is in progress", e);
			return new AdaptiveFlowTaskResult(Optional.empty(), true);
		}
	}
	public List<AdaptiveFlowTask> listAdaptiveFlowTasks(int resourceId,
														Optional<Long> maybeId,
														Optional<String> maybeStatus,
														Optional<Integer> maybePage,
														Optional<Integer> maybeSize) {
		try {
			UriComponentsBuilder uriBuilder = UriComponentsBuilder
				.fromHttpUrl(listingAppServer + "/adaptive_flow_tasks")
				.queryParam("resource_id", resourceId);

			maybeId.ifPresent(taskId -> uriBuilder.queryParam("id", taskId));
			maybeStatus.ifPresent(stat -> uriBuilder.queryParam("status", stat));
			maybePage.ifPresent(page -> uriBuilder.queryParam("page", page));
			maybeSize.ifPresent(size -> uriBuilder.queryParam("size", size));

			String uri = uriBuilder.toUriString();
			HttpEntity<?> request = new HttpEntity<>(authorizationHeader);
			ResponseEntity<String> response = retry(uri, HttpMethod.GET, request);

			if (response.getStatusCode().is2xxSuccessful()) {
				List<Object> data = JsonUtils.jsonToList(response.getBody());
				List<AdaptiveFlowTask> tasks = StreamEx.of(data)
					.map(it -> (Map<String, Object>) it)
					.map(ListingClient::toAdaptiveFlowTask)
					.toList();
				LOGGER.info("tasks list assembled");
				return tasks;
			} else {
				LOGGER.error("Failed to list adaptive flow tasks, HTTP Status: {}", response.getStatusCode());
				return Collections.emptyList();
			}
		} catch (Exception e) {
			LOGGER.error("Exception while listing adaptive flow tasks", e);
			return Collections.emptyList();
		}
	}

	/**
	 * Updates the status of an Adaptive Flow Task.
	 * Uses POST {serverURL including port}/tasks/{taskId} with "status":"newstatus"
	 *
	 * @param taskId       The ID of the task to update.
	 * @param status The new status to set for the task.
	 * @throws Exception If an error occurs during communication with the
	 * adaptive flow tasks service. The underlying exception
	 * is re-thrown.
	 */
	public void updateAdaptiveTaskStatus(int sourceId, long taskId, AdaptiveFlowTask.TaskStatus status, Optional<String> message) {
		HttpEntity request = new HttpEntity<>(authorizationHeader);
		UriComponentsBuilder url = fromHttpUrl(listingAppServer + "/adaptive_flow_tasks/" + sourceId + "/task/" + taskId + "/status/" + status.name().toLowerCase());
		message.ifPresent(m -> url.queryParam("message", m));

		retry(url.toUriString(), POST, request);
	}

	public static AdaptiveFlowTask toAdaptiveFlowTask(Map<String, Object> data) {
		AdaptiveFlowTask task = new AdaptiveFlowTask(
			toLong(data.get("id")),
			JsonUtils.jsonToMap(data.get("parameters").toString()),
			toInteger(data.get("sourceId")),
			toInteger(data.get("flowId")),
			toInteger(data.get("orgId")),
			AdaptiveFlowTask.TaskStatus.valueOf(data.get("status").toString()),
			LocalDateTime.parse(data.get("createdAt").toString()).toInstant(ZoneOffset.UTC).toEpochMilli(),
			LocalDateTime.parse(data.get("updatedAt").toString()).toInstant(ZoneOffset.UTC).toEpochMilli(),
			data.get("message").toString());

		return task;
	}


	public boolean refreshRestToken(int credId) {
		HttpEntity request = new HttpEntity<>(authorizationHeader);
		UriComponentsBuilder url = fromHttpUrl(listingAppServer + "/token/refresh/" + credId);
		try {
			ResponseEntity<String> result = retry(url.toUriString(), POST, request);
			return result.getStatusCode().is2xxSuccessful();
		} catch (Exception e) {
			LOGGER.debug("token refresh error", e);
			return false;
		}
	}

	public String getVersion() {
		return version.get();
	}

	@SneakyThrows
	private ResponseEntity<String> retry(String url, HttpMethod httpMethod, HttpEntity request) {
		return LISTING_RETRYER.call(() -> restTemplate.exchange(url, httpMethod, request, String.class));
	}

}
