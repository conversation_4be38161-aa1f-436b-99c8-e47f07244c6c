package com.nexla.listing.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.control.ListingFileStatus;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

@Getter
@Setter
public class ListedFile {

	public final Long id;
	public final String fullPath;
	public final Long size;
	public final Long lastModified;
	public final String hash;
	public final Optional<Map<String, String>> metadata;
	public final LocalDateTime createdAt;
	public final ListingFileStatus status;

	@JsonCreator
	public ListedFile(
		@JsonProperty("id") Long id,
		@JsonProperty("fullPath") String fullPath,
		@JsonProperty("size") Long size,
		@JsonProperty("lastModified") Long lastModified,
		@JsonProperty("hash") String hash,
		@JsonProperty("metadata") Optional<Map<String, String>> metadata,
		@JsonProperty("createdAt") LocalDateTime createdAt,
		@JsonProperty("status") String status
	) {
		this.id = id;
		this.fullPath = fullPath;
		this.size = size;
		this.lastModified = lastModified;
		this.hash = hash;
		this.metadata = metadata;
		this.createdAt = createdAt;
		this.status = ListingFileStatus.valueOf(status);
	}
}
