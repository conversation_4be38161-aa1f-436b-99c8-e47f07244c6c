package com.nexla.listing.client;

import com.github.rholder.retry.*;
import com.google.common.base.Preconditions;
import com.nexla.common.Resource;
import com.nexla.common.sink.TopicPartition;
import com.nexla.control.coordination.SinkOffset;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.AppUtils.authorizationHeader;
import static org.springframework.http.HttpMethod.*;
import static org.springframework.http.HttpStatus.NOT_FOUND;
import static org.springframework.web.util.UriComponentsBuilder.fromHttpUrl;

public class ListingCoordinationClient {

	private static final Logger LOGGER = LoggerFactory.getLogger(ListingCoordinationClient.class);

	private final HttpHeaders authorizationHeader;
	private final String listingAppUrl;
	private final RestTemplate restTemplate;

	public ListingCoordinationClient(
		String listingAppUrl,
		String nexlaUsername,
		String nexlaPassword,
		RestTemplate restTemplate
	) {
		this.restTemplate = restTemplate;
		this.listingAppUrl = listingAppUrl;
		this.authorizationHeader = authorizationHeader(nexlaUsername, nexlaPassword);
	}

	/**
	 * Notify about ongoing flush operation for specific resource and flushId.
	 */
	public void notifyFlush(UUID flushId, Resource resource) {
		HttpEntity request = new HttpEntity<>(authorizationHeader);
		UriComponentsBuilder url = fromHttpUrl(listingAppUrl + "/flush/notify/" + resource.type.toString() + "/" + resource.id + "/" + flushId.toString());
		restTemplate.exchange(url.toUriString(), POST, request, String.class);
	}

	/**
	 * Notify about finished flush operation for specific resource and flushId.
	 * @return flag that indicates if all flush operations for the same sink are finished
	 */
	public boolean flushFinished(UUID flushId, Resource resource) throws Exception {
		HttpEntity request = new HttpEntity<>(authorizationHeader);
		UriComponentsBuilder url = fromHttpUrl(listingAppUrl + "/flush/finish/" + resource.type.toString() + "/" + resource.id + "/" + flushId.toString());
		Retryer<ResponseEntity<FlushFinished>> retryer = RetryerBuilder.<ResponseEntity<FlushFinished>>newBuilder()
			.retryIfException()
			.retryIfResult(result -> result.getStatusCode().is5xxServerError())
			.withRetryListener(new RetryListener() {
				@Override
				public <V> void onRetry(Attempt<V> attempt) {
					if (attempt.getAttemptNumber() > 1) {
						LOGGER.warn("[{}] Retrying to check flush operation finished status for sink={}", attempt.getAttemptNumber(), resource.id);
					}
				}
			})
			.withWaitStrategy(WaitStrategies.randomWait(30, TimeUnit.SECONDS))
			.withStopStrategy(new StopAfterAttemptOrNotFoundStrategy(3))
			.build();

		final ResponseEntity<FlushFinished> response = retryer.call(
				() -> restTemplate.exchange(url.toUriString(), POST, request, FlushFinished.class));
		return response.getBody().finished;
	}

	@SneakyThrows
	public void updateSinkOffsets(int sinkId, List<SinkOffset> sinkOffsets) {

		HttpEntity request = new HttpEntity<>(toJsonString(sinkOffsets), authorizationHeader);

		Retryer<ResponseEntity<String>> retryer = RetryerBuilder.<ResponseEntity<String>>newBuilder()
			.retryIfException()
			.retryIfResult(result ->
				result.getStatusCode().is5xxServerError()
			)
			.withRetryListener(new RetryListener() {
				@Override
				public <V> void onRetry(Attempt<V> attempt) {
					if (attempt.getAttemptNumber() > 1) {
						LOGGER.warn("[{}] Retrying update offsets call for sink={}, offsets={}", attempt.getAttemptNumber(), sinkId, sinkOffsets);
					}
				}
			})
			.withWaitStrategy(WaitStrategies.randomWait(30, TimeUnit.SECONDS))
			.withStopStrategy(new StopAfterAttemptOrNotFoundStrategy(100))
			.build();

		retryer.call(() -> restTemplate.postForEntity(
			listingAppUrl + "/sink/" + sinkId + "/offset", request, String.class));

	}

	public Optional<Map<TopicPartition, Long>> getSinkOffsets(int sinkId) {
		// TODO FIXME Fix offsets commit logic in 2.13. Remove this method
		return getSinkOffsets(sinkId, 0 , () -> {});
	}

	@SneakyThrows
	public Optional<Map<TopicPartition, Long>> getSinkOffsets(int sinkId, long ts, Runnable onRetry) {
		HttpEntity request = new HttpEntity<>(authorizationHeader);
		ParameterizedTypeReference<List<SinkOffset>> ref = new ParameterizedTypeReference<>() {
		};
		UriComponentsBuilder builder = fromHttpUrl(listingAppUrl + "/sink/" + sinkId + "/offset");
//				.queryParam("ts", ts);					// TODO FIXME Fix offsets commit logic in 2.13. Add parameter to use the new approach

		Retryer<ResponseEntity<List<SinkOffset>>> retryer = RetryerBuilder.<ResponseEntity<List<SinkOffset>>>newBuilder()
			.retryIfException()
			.retryIfResult(result ->
				result.getStatusCode().is5xxServerError()
			)
			.withRetryListener(new RetryListener() {
				@Override
				public <V> void onRetry(Attempt<V> attempt) {
					if (attempt.getAttemptNumber() > 1) {
						LOGGER.warn("[{}] Retrying get offsets call for sink={}", attempt.getAttemptNumber(), sinkId);
						if (onRetry != null) {
							onRetry.run();
						}
					}
				}
			})
			.withWaitStrategy(WaitStrategies.randomWait(30, TimeUnit.SECONDS))
			.withStopStrategy(new StopAfterAttemptOrNotFoundStrategy(100))
			.build();

		List<SinkOffset> offsets = retryer.call(() -> restTemplate
						.exchange(builder.build().encode().toUri(), GET, request, ref))
				.getBody();

		return Optional.ofNullable(offsets)
				.filter(CollectionUtils::isNotEmpty)
				.map(list -> StreamEx
						.of(list)
						.toMap(o -> new TopicPartition(o.topic, Integer.parseInt(o.partition)), o -> o.offset));
	}

	@SneakyThrows
	public Optional<Map<TopicPartition, Long>> deleteSinkOffsets(int sinkId) {
		HttpEntity request = new HttpEntity<>(authorizationHeader);
		ParameterizedTypeReference<List<SinkOffset>> ref = new ParameterizedTypeReference<>() {
		};
		UriComponentsBuilder builder = fromHttpUrl(listingAppUrl + "/sink/" + sinkId + "/offset");

		Retryer<ResponseEntity<List<SinkOffset>>> retryer = RetryerBuilder.<ResponseEntity<List<SinkOffset>>>newBuilder()
				.retryIfException()
				.retryIfResult(result ->
						result.getStatusCode().is5xxServerError()
				)
				.withRetryListener(new RetryListener() {
					@Override
					public <V> void onRetry(Attempt<V> attempt) {
						if (attempt.getAttemptNumber() > 1) {
							LOGGER.warn("[{}] Retrying delete offsets call for sink={}", attempt.getAttemptNumber(), sinkId);
						}
					}
				})
				.withWaitStrategy(WaitStrategies.exponentialWait(30, TimeUnit.SECONDS))
				.withStopStrategy(new StopAfterAttemptOrNotFoundStrategy(20))
				.build();

		List<SinkOffset> offsets = retryer.call(() -> restTemplate
						.exchange(builder.build().encode().toUri(), DELETE, request, ref))
				.getBody();

		return Optional.ofNullable(offsets)
				.filter(CollectionUtils::isNotEmpty)
				.map(list -> StreamEx
						.of(list)
						.toMap(o -> new TopicPartition(o.topic, Integer.parseInt(o.partition)), o -> o.offset));
	}

	private static final class StopAfterAttemptOrNotFoundStrategy implements StopStrategy {

		private static final Logger LOGGER = LoggerFactory.getLogger(ListingCoordinationClient.class);
		private final int maxAttemptNumber;

		public StopAfterAttemptOrNotFoundStrategy(int maxAttemptNumber) {
			Preconditions.checkArgument(maxAttemptNumber >= 1, "maxAttemptNumber must be >= 1 but is %d", maxAttemptNumber);
			this.maxAttemptNumber = maxAttemptNumber;
		}

		@Override
		public boolean shouldStop(Attempt failedAttempt) {
			if (failedAttempt.hasException() && failedAttempt.getExceptionCause() instanceof HttpClientErrorException) {
				if (NOT_FOUND == ((HttpClientErrorException) failedAttempt.getExceptionCause()).getStatusCode()) {
					LOGGER.warn("Sink was not found");
					return true;
				}
			}
			return failedAttempt.getAttemptNumber() >= maxAttemptNumber;
		}
	}

}
