package com.nexla.listing.client;

import com.fasterxml.jackson.annotation.JsonProperty;

@lombok.Data
public class TmpFile {
    @JsonProperty("id")
    private long id;
    @JsonProperty("resource_type")
    private String resourceType;
    @JsonProperty("resource_id")
    private long resourceId;
    @JsonProperty("flow_id")
    private long flowId;
    @JsonProperty("run_id")
    private long runId;
    @JsonProperty("stage")
    private String stage;
    @JsonProperty("status")
    private String status;
    @JsonProperty("origin_listed_file")
    private long originListedFile;
    @JsonProperty("origin_tmp_file")
    private long originTmpFile;
    @JsonProperty("full_path")
    private String fullPath;
    @JsonProperty("hash")
    private String hash;
    @JsonProperty("file_size")
    private long fileSize;
    @JsonProperty("message")
    private String message;
    @JsonProperty("meta")
    private String meta;
}
