package com.nexla.admin.client;

import com.nexla.telemetry.Telemetry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

import java.util.Optional;

public class AdminApiClientBuilder {
    public static volatile AdminApiClient INSTANCE;

    private static final Logger LOGGER = LoggerFactory.getLogger(AdminApiClientBuilder.class);

    private String dataPlaneUid;
    private String enrichmentUrl;
    private String appName = "nexla";
    private boolean noCache = false;
    private boolean expandResources = true;
	private boolean runtimeStatusUpdateEnabled = true;
    private Optional<Telemetry> telemetry = Optional.empty();

	public AdminApiClient create(String apiCredentialsServer,
								 String apiAccessKey,
								 RestTemplate restTemplate) {
		if (INSTANCE == null) {
			synchronized (AdminApiClientBuilder.class) {
				if (INSTANCE == null) {
					if (dataPlaneUid == null) {
						LOGGER.warn("Attribute dataplane.uid is absent in application-creds. X-Nexla-Dataplane can not be attached to request header");
					}

					INSTANCE = new AdminApiClient(appName, apiCredentialsServer, apiAccessKey, dataPlaneUid, enrichmentUrl, restTemplate, telemetry, noCache, expandResources, runtimeStatusUpdateEnabled);
				}
			}
		}

        return INSTANCE;
    }


    public AdminApiClientBuilder setDataPlaneUid(String dataPlaneUid) {
        this.dataPlaneUid = dataPlaneUid;
        return this;
    }

    public AdminApiClientBuilder setEnrichmentUrl(String enrichmentUrl) {
        this.enrichmentUrl = enrichmentUrl;
        return this;
    }

    public AdminApiClientBuilder setAppName(String appName) {
        this.appName = appName;
        return this;
    }

    public AdminApiClientBuilder setNoCache(boolean noCache) {
        this.noCache = noCache;
        return this;
    }

    public AdminApiClientBuilder setExpandResources(boolean expandResources) {
        this.expandResources = expandResources;
        return this;
    }

    public AdminApiClientBuilder setTelemetry(Optional<Telemetry> telemetry) {
        this.telemetry = telemetry;
        return this;
    }

	public AdminApiClientBuilder setRuntimeStatusUpdateEnabled(boolean runtimeStatusUpdateEnabled) {
		this.runtimeStatusUpdateEnabled = runtimeStatusUpdateEnabled;
		return this;
	}
}
