package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.joda.JodaModule;
import com.fasterxml.jackson.module.scala.DefaultScalaModule$;
import com.github.rholder.retry.*;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.nexla.admin.client.errors.ErrorTransform;
import com.nexla.admin.client.flownode.NexlaFlow;
import com.nexla.admin.client.flows.DataFlow;
import com.nexla.admin.client.pipeline.Pipeline;
import com.nexla.common.*;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.control.message.ControlEventType;
import com.nexla.telemetry.Telemetry;
import com.nexla.telemetry.utils.ExecutionMetricSet;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.*;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.bazaarvoice.jolt.JsonUtils.jsonToMap;
import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.admin.client.FindOrCreateDataSetResult.Result.CREATED;
import static com.nexla.common.NexlaConstants.PIPELINE_TYPE;
import static com.nexla.common.ResourceType.*;
import static com.nexla.common.StreamUtils.lhm;
import static com.nexla.common.StreamUtils.map;
import static com.nexla.common.datetime.DateTimeUtils.timed;
import static com.nexla.telemetry.utils.ExecutionTelemetryUtils.metricSet;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Collections.*;
import static java.util.Optional.of;
import static java.util.Optional.*;
import static java.util.concurrent.TimeUnit.*;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;
import static org.springframework.http.HttpMethod.*;
import static org.springframework.web.util.UriComponentsBuilder.fromHttpUrl;
import static org.springframework.web.util.UriComponentsBuilder.fromUriString;

public class AdminApiClient {

	private static final Logger LOGGER = LoggerFactory.getLogger(AdminApiClient.class);
	private static final Integer ACCESS_TOKEN_EXPIRE_SECS = 30 * 60;

	private static final Integer DEFAULT_DATA_CREDENTIALS_REFRESH_SECS = 60 * 60 * 3; // 3h
	private static final Integer DEFAULT_DATA_CREDENTIALS_EXPIRE_SECS = DEFAULT_DATA_CREDENTIALS_REFRESH_SECS;

	private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
	private static final int PER_PAGE = 100;

	private static final ParameterizedTypeReference<List<QuarantineSetting>> QUARANTINE_SETTING_TYPE_REF = new ParameterizedTypeReference<List<QuarantineSetting>>() {
	};
	private static final ParameterizedTypeReference<List<DataSet>> DATASET_TYPE_REF = new ParameterizedTypeReference<List<DataSet>>() {
	};
	private static final ParameterizedTypeReference<List<NotificationSetting>> NOTIFICATION_SETTING_TYPE_REF = new ParameterizedTypeReference<List<NotificationSetting>>() {
	};
	private static final ParameterizedTypeReference<List<ErrorTransform>> ERROR_TRANSFORM_TYPE_REF = new ParameterizedTypeReference<List<ErrorTransform>>() {
	};
	private static final ParameterizedTypeReference<List<Object>> TYPE_REFERENCE = new ParameterizedTypeReference<List<Object>>() {
	};
	private static final ParameterizedTypeReference<List<Owner>> OWNER_TYPE_REF = new ParameterizedTypeReference<List<Owner>>() {
	};

	static {
		Jdk8Module module = new Jdk8Module();
		module.configureAbsentsAsNulls(true);
		OBJECT_MAPPER.registerModule(module);
		OBJECT_MAPPER.registerModule(new JodaModule());
		OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	}

	private final LoadingCache<NotificationSettingKey, List<NotificationSetting>> notificationSettingCache;
	private final LoadingCache<NotificationSettingAllKey, List<NotificationSetting>> notificationSettingAllCache;
	private final LoadingCache<String, AdminApiToken> adminApiTokenCache;
	private final LoadingCache<Integer, Optional<DataSource>> dataSourceCache;
	private final LoadingCache<Integer, Optional<DataSink>> dataSinkCache;
	private final LoadingCache<Integer, Optional<DataSet>> dataSetCache;

	private final LoadingCache<Integer, Optional<NexlaFlow>> flowCache;
	private final LoadingCache<Resource, Optional<NexlaFlow>> flowByResourceCache;
	private final LoadingCache<Integer, Transform> transformCache;
	private final LoadingCache<Integer, CodeContainer> codeContainerCache;
	private final LoadingCache<Integer, Optional<Pipeline>> pipelineCache;
	private final LoadingCache<Integer, Org> orgCache;
	private final LoadingCache<Integer, Optional<DataCredentials>> dataCredentialsCache;
	private final LoadingCache<Integer, CustomDataFlow> customDataFlowCache;
	private final LoadingCache<Integer, Optional<CatalogConfig>> catalogConfigCache;

	public final String appName;
	protected final RestTemplate restTemplate;

	public final String apiCredentialsServer;
	public final String apiAccessKey;
	private final JsonMapper jsonMapper;

	private final String dataPlaneUid;
	private static Optional<Telemetry> telemetry;

	private final boolean expandResources;
	private final boolean runtimeStatusUpdateEnabled;

	protected AdminApiClient(
		String appName,
		String apiCredentialsServer,
		String apiAccessKey,
    String dataPlaneUid,
		String enrichmentUrl,
		RestTemplate restTemplate,
		Optional<Telemetry> telemetryOpt,
		boolean noCache,
		boolean expandResources,
		boolean runtimeStatusUpdateEnabled
	) {
		if (BaseAuthConfig.GLOBAL_ENRICHMENT_URL.isEmpty()) {
			BaseAuthConfig.GLOBAL_ENRICHMENT_URL = Optional.ofNullable(enrichmentUrl);
		}
		logger.info("Creating adminApiClient with dataPlaneUid: {}, runtimeStatusUpdateEnabled: {}", dataPlaneUid, runtimeStatusUpdateEnabled);
		this.restTemplate = restTemplate;
		this.appName = appName;
		this.apiCredentialsServer = apiCredentialsServer;
		this.apiAccessKey = apiAccessKey;
		this.dataPlaneUid = dataPlaneUid;
		this.runtimeStatusUpdateEnabled = runtimeStatusUpdateEnabled;
		this.expandResources = expandResources;
		telemetry = telemetryOpt;
		this.adminApiTokenCache = CacheBuilder.newBuilder()
			                          .expireAfterWrite(ACCESS_TOKEN_EXPIRE_SECS, SECONDS)
			                          .build(new CacheLoader<>() {
				                          @Override
				                          public AdminApiToken load(String key) {
					                          return doGetAccessToken();
				                          }
			                          });

		this.dataCredentialsCache = CacheBuilder.newBuilder()
			                            .maximumSize(noCache ? 0 : Integer.MAX_VALUE)
			                            .build(new CacheLoader<>() {
				                            @Override
				                            public Optional<DataCredentials> load(Integer key) {
					                            Optional<DataCredentials> entity = doGetDataCredential(key);
					                            return updateCondensed(key, entity, CREDENTIALS);
				                            }

			                            });

		this.dataSourceCache = CacheBuilder.newBuilder()
			                       .maximumSize(noCache ? 0 : Integer.MAX_VALUE)
			                       .build(new CacheLoader<>() {
				                       @Override
				                       public Optional<DataSource> load(Integer key) {
					                       Optional<DataSource> entity = doGetDataSource(key);
					                       return updateCondensed(key, entity, SOURCE);
				                       }
			                       });

		this.flowCache = CacheBuilder
									.newBuilder()
									.maximumSize(noCache ? 0 : Integer.MAX_VALUE)
			                        .build(new CacheLoader<>() {
				                      @Override
				                      public Optional<NexlaFlow> load(Integer key) {
					                      return doGetNexlaFlow(key);
				                      }
			                      });

		this.flowByResourceCache = CacheBuilder
				.newBuilder()
				.maximumSize(noCache ? 0 : Integer.MAX_VALUE)
				.build(new CacheLoader<>() {
					@Override
					public Optional<NexlaFlow> load(Resource key) {
						return doGetFlowByResource(key);
					}
				});

		this.dataSinkCache = CacheBuilder.newBuilder()
			                     .maximumSize(noCache ? 0 : Integer.MAX_VALUE)
			                     .build(new CacheLoader<>() {
				                     @Override
				                     public Optional<DataSink> load(Integer key) {
					                     Optional<DataSink> entity = doGetDataSink(key);
					                     return updateCondensed(key, entity, SINK);
				                     }
			                     });

		this.pipelineCache = CacheBuilder.newBuilder()
			.maximumSize(noCache ? 0 : Integer.MAX_VALUE)
			.build(new CacheLoader<>() {
				@Override
				public Optional<Pipeline> load(Integer key) {
					return doGetPipeline(key);
				}
			});

		this.dataSetCache = CacheBuilder.newBuilder()
			                    .maximumSize(noCache ? 0 : Integer.MAX_VALUE)
			                    .build(new CacheLoader<>() {
				                    @Override
				                    public Optional<DataSet> load(Integer key) {
					                    Optional<DataSet> entity = doGetDataSet(key);
					                    return updateCondensed(key, entity, DATASET);
				                    }
			                    });

		this.transformCache = CacheBuilder.newBuilder()
			                      .expireAfterWrite(5, MINUTES)
			                      .build(new CacheLoader<>() {
				                      @Override
				                      public Transform load(Integer key) {
					                      return doGetTransform(key);
				                      }
			                      });

		this.notificationSettingCache = CacheBuilder.newBuilder()
			                                .maximumSize(noCache ? 0 : Integer.MAX_VALUE)
			                                .expireAfterWrite(5, MINUTES)
			                                .build(new CacheLoader<>() {
				                                @Override
				                                public List<NotificationSetting> load(NotificationSettingKey key) {
					                                return doGetNotificationSettings(key);
				                                }
			                                });

		this.notificationSettingAllCache = CacheBuilder.newBuilder()
			                                   .maximumSize(noCache ? 0 : Integer.MAX_VALUE)
			                                   .expireAfterWrite(5, MINUTES)
			                                   .build(new CacheLoader<>() {
				                                   @Override
				                                   public List<NotificationSetting> load(NotificationSettingAllKey key) {
					                                   return doGetAllNotificationSettings(key);
				                                   }
			                                   });

		this.customDataFlowCache = CacheBuilder.newBuilder()
			                           .maximumSize(noCache ? 0 : Integer.MAX_VALUE)
			                           .expireAfterWrite(5, MINUTES)
			                           .build(new CacheLoader<>() {
				                           @Override
				                           public CustomDataFlow load(Integer key) {
					                           return doGetCustomDataFlow(key);
				                           }
			                           });

		this.catalogConfigCache = CacheBuilder.newBuilder()
				.maximumSize(noCache ? 0 : Integer.MAX_VALUE)
				.expireAfterWrite(5, MINUTES)
				.build(new CacheLoader<>() {
					@Override
					public Optional<CatalogConfig> load(Integer key) {
						return doGetCatalogConfig(key);
					}
				});

		this.codeContainerCache = CacheBuilder.newBuilder()
			                          .maximumSize(noCache ? 0 : Integer.MAX_VALUE)
			                          .expireAfterWrite(5, MINUTES)
			                          .build(new CacheLoader<>() {
				                          @Override
				                          public CodeContainer load(Integer key) {
					                          return doGetCodeContainer(key);
				                          }
			                          });

		this.orgCache = CacheBuilder.newBuilder()
			                .maximumSize(noCache ? 0 : Integer.MAX_VALUE)
			                .expireAfterWrite(5, MINUTES)
			                .build(new CacheLoader<>() {
				                @Override
				                public Org load(Integer key) {
					                return doGetOrg(key);
				                }
			                });

		this.jsonMapper = JsonMapper.builder()
			                  .addModule(DefaultScalaModule$.MODULE$)
			                  .addModule(new JodaModule())
			                  .addModule(new Jdk8Module().configureAbsentsAsNulls(true))
			                  .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
			                  .build();
	}

	/**
	 * Method should be called for every resource type stored in AdminApiClientCondensed
	 */
	private static <T> Optional<T> updateCondensed(Integer resourceId, Optional<T> entity, ResourceType resourceType) {
		Optional
			.ofNullable(AdminApiCondensedBuilder.INSTANCE)
			.ifPresent(cond -> {
				ControlEventType action = entity.isEmpty() ? ControlEventType.DELETE : ControlEventType.ACTIVATE;
				cond.updateCache(resourceId, resourceType,  action, entity.map(x -> StreamUtils.toResourceDto(x, emptySet())));
			});
		return entity;
	}

	private AdminApiToken doGetAccessToken() {

		HttpHeaders headers = new HttpHeaders();
		headers.add("Authorization", "Basic " + apiAccessKey);
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.setAccept(singletonList(MediaType.APPLICATION_JSON));
		headers.setContentLength(0);

		if (dataPlaneUid != null) {
			headers.add("X-Nexla-Dataplane", dataPlaneUid);
		}

		HttpEntity request = new HttpEntity<>(headers);

		AdminApiToken token;
		try {
			token = restTemplate.postForObject(apiCredentialsServer + "/token", request, AdminApiToken.class);
			return token;
		} catch (ResourceAccessException rae) {
			incAdminApiConnectionErrors();
			throw rae;
		}
	}

	@SneakyThrows
	public CodeContainer getCodeContainer(Integer codeContainerId) {
		return codeContainerCache.get(codeContainerId);
	}

	public DataSource getDataSourceByDataSet(int dataSetId) {
		var dataSet = getOriginDataSet(dataSetId);
		return getDataSource(dataSet.getDataSourceId().get()).get();
	}

	public DataSet getOriginDataSet(int dsId) {
		var dataSet = getDataSet(dsId).get();
		if (dataSet.getDataSourceId().isPresent()) {
			return dataSet;
		} else {
			List<DataSet> maybeParent = dataSet.getParentDatasets();
			if (maybeParent != null && maybeParent.size() > 0) {
				DataSet parent = maybeParent.get(0);
				return getOriginDataSet(parent.getId());
			} else {
				// null or empty dataset, nothing we can do
				LOGGER.warn("dataset-{} does not have parent datasets or they are null, returning original one", dsId);
				return dataSet;
			}
		}
	}

	@SneakyThrows
	public Optional<Pipeline> getPipeline(int sinkId) {
		return pipelineCache.get(sinkId);
	}

	@SneakyThrows
	public CustomDataFlow getCustomDataFlow(Integer id) {
		return customDataFlowCache.get(id);
	}

	@SneakyThrows
	public Optional<DataCredentials> getDataCredentials(int credsId) {
		return dataCredentialsCache.get(credsId);
	}

	@SneakyThrows
	public Optional<CatalogConfig> getCatalogConfig(int catalogConfigId) {
		return catalogConfigCache.get(catalogConfigId);
	}

	@SneakyThrows
	public Optional<DataSource> getDataSource(Integer dataSourceId) {
		return dataSourceCache.get(dataSourceId);
	}

	@SneakyThrows
	public Optional<DataSet> getDataSet(Integer dataSetId) {
		return dataSetCache.get(dataSetId);
	}

	@SneakyThrows
	public Transform getTransform(Integer transformId) {
		return transformCache.get(transformId);
	}

	@SneakyThrows
	public Optional<DataSink> getDataSink(Integer sinkId) {
		return dataSinkCache.get(sinkId);
	}

	@SneakyThrows
	public OwnerAndOrg getOwnerAndOrg(Resource resource) {
		try {
			switch (resource.type) {
				case SOURCE:
					return getDataSource(resource.id).get();
				case SINK:
					return getDataSink(resource.id).get();
				case DATASET:
					return getDataSet(resource.id).get();
				case CUSTOM_DATA_FLOW:
					return getCustomDataFlow(resource.id);
				case CREDENTIALS:
					return getDataCredentials(resource.id).get();
				case CATALOG_CONFIG:
					return getCatalogConfig(resource.id).get();
				default:
					throw new IllegalArgumentException("Unsupported resource type=" + resource.type);
			}
		} catch (Exception e) {
			LOGGER.error("Exception while fetching owner/org for source {}", resource, e);
			throw e;
		}
	}

	public void notifySourceRunId(Integer sourceId, Long runId) {
		LOGGER.info("notifySourceRunId sourceId = {} runid = {}", sourceId, runId);
		try {
			HttpEntity request = new HttpEntity<>(toJsonString(lhm("run_id", runId.toString())), getHttpHeaders());
			UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/data_sources/" + sourceId);
			restTemplate.put(builder.build().encode().toUri(), request);
		} catch (Exception e) {
			LOGGER.error("notifySourceRunId error with sourceId {} runID = {} - {}", sourceId, runId, e);
		}
	}

	public boolean updateResourceRuntimeStatus(Resource resource, RuntimeStatus status) {
		return updateResourceRuntimeStatus(resource.id, resource.type, status);
	}

	public boolean updateResourceRuntimeStatus(Integer resourceId, ResourceType resourceType, RuntimeStatus status) {
		LOGGER.info("updateResourceRuntimeStatus resourceId = {} resourceType = {} status = {} [enabled={}]", resourceId, resourceType, status, runtimeStatusUpdateEnabled);
		if (!runtimeStatusUpdateEnabled) {
			return false;
		}
		try {
			HttpEntity request = getHttpHeadersEntity();
			UriComponentsBuilder builder = fromHttpUrl(
					apiCredentialsServer + getResourceEndpoint(resourceType) + "/" + resourceId + "/runtime_status/" + status.getKey());

			ResponseEntity<Object> result = restTemplate.exchange(builder.build().encode().toUri(), PUT,
					request, Object.class);
			return result.getStatusCode().is2xxSuccessful();
		} catch (Exception e) {
			LOGGER.error("updateResourceRuntimeStatus error with resourceId = {} resourceType = {} status = {} - {}", resourceId, resourceType, status, e);
			return false;
		}
	}

	public void invalidate(Integer id, Optional<Integer> flowId, ResourceType resourceType) {
		Resource resource = new Resource(id, resourceType);
		flowByResourceCache.invalidate(resource);
		getCache(resourceType).invalidate(id);
		flowId.ifPresent(flowCache::invalidate);
	}

	private LoadingCache<Integer, ?> getCache(ResourceType resourceType) {
		switch (resourceType) {
			case SINK:
				return dataSinkCache;
			case SOURCE:
				return dataSourceCache;
			case DATASET:
				return dataSetCache;
			case CREDENTIALS:
				return dataCredentialsCache;
			default:
				throw new IllegalStateException("Not supported");
		}
	}

	private static final ExecutionMetricSet doGetPipelineMetrics = metricSet(AdminApiClient.class, "doGetPipeline");
	private Optional<Pipeline> doGetPipeline(Integer id) {
		try (var ignored = doGetPipelineMetrics.time()) {
			URI uri = fromHttpUrl(apiCredentialsServer + "/data_flows/data_sink/" + id + "?provisioning=1").build().encode().toUri();
			List<Pipeline> pipeline = retryIfException(() -> restTemplate
				.exchange(uri, GET, getHttpHeadersEntity(), new ParameterizedTypeReference<List<Pipeline>>() {
				})
				.getBody());
			return pipeline.stream().findFirst();
		} catch (org.springframework.web.client.HttpClientErrorException.NotFound e) {
			LOGGER.error("Pipeline {} not found", id);
			doGetPipelineMetrics.incError();
			return empty();
		}
	}

	public Optional<NexlaFlow> doGetNexlaFlowExpanded(Integer flowId) {
		try {
			UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/flows/" + flowId);
			updateExpandRequestParam(builder, true);
			URI uri = builder.build().encode().toUri();
			NexlaFlow nexlaFlow = retryIfException(() -> restTemplate
					.exchange(uri, GET, getHttpHeadersEntity(), new ParameterizedTypeReference<NexlaFlow>() {})
					.getBody());
			return Optional.ofNullable(nexlaFlow);
		} catch (org.springframework.web.client.HttpClientErrorException.NotFound e) {
			LOGGER.error("NexlaFlow {} not found", flowId);
			return empty();
		}
	}

	private static final ExecutionMetricSet doGetNexlaFlowMetrics = metricSet(AdminApiClient.class, "doGetNexlaFlow");
	private Optional<NexlaFlow> doGetNexlaFlow(Integer flowId) {
		try (var ignored = doGetNexlaFlowMetrics.time()) {
			URI uri = fromHttpUrl(apiCredentialsServer + "/flows/" + flowId).build().encode().toUri();
			NexlaFlow nexlaFlow = retryIfException(() -> restTemplate
				.exchange(uri, GET, getHttpHeadersEntity(), new ParameterizedTypeReference<NexlaFlow>() {})
				.getBody());
			return Optional.ofNullable(nexlaFlow);
		} catch (org.springframework.web.client.HttpClientErrorException.NotFound e) {
			LOGGER.error("NexlaFlow {} not found", flowId);
			doGetNexlaFlowMetrics.incError();
			return empty();
		}
	}

	@SneakyThrows
	public Optional<NexlaFlow> getNexlaFlow(Integer flowId) {
		return flowCache.get(flowId);
	}

	/**
	 * Retrieves datasource from AdminApi client.
	 * Should return empty Optional if source does not exist
	 * Should throw an error in case of failures
	 */
	private static final ExecutionMetricSet doGetDataSourceMetrics = metricSet(AdminApiClient.class, "doGetDataSource");
	@SuppressWarnings("AccessStaticViaInstance")
	private Optional<DataSource> doGetDataSource(Integer dataSourceId) {
		try (var ignored = doGetDataSourceMetrics.time()) {
			UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/data_sources" + "/" + dataSourceId);
			updateExpandRequestParam(builder, expandResources);
			URI uri = builder.build().encode().toUri();
			DataSource dataSource = retryIfException(() -> restTemplate
				                                               .exchange(uri, GET, getHttpHeadersEntity(), DataSource.class)
				                                               .getBody());
			return ofNullable(dataSource).map(ds -> ds.setObjectVersion(ds.CLASS_VERSION));
		} catch (org.springframework.web.client.HttpClientErrorException.NotFound e) {
			LOGGER.error("DataSource {} not found", dataSourceId);
			doGetDataSourceMetrics.incError();
			return empty();
		}
	}

	private CustomDataFlow doGetCustomDataFlow(Integer id) {
		URI uri = fromHttpUrl(apiCredentialsServer + "/custom_data_flows" + "/" + id).build().encode().toUri();
		return retryIfException(() -> restTemplate
			                              .exchange(uri, GET, getHttpHeadersEntity(), CustomDataFlow.class)
			                              .getBody());
	}

	private Optional<DataCredentials> doGetDataCredential(Integer key) {
		try {
			return Optional.ofNullable(doGetDataCredentials(key));
		} catch (
			HttpClientErrorException.NotFound e) {
			LOGGER.error("DataCredentials {} not found", key);
			return empty();
		}
	}

	private Optional<CatalogConfig> doGetCatalogConfig(Integer id) {
		try {
			URI uri = fromHttpUrl(apiCredentialsServer + "/catalog_configs/" + id).build().encode().toUri();
			return ofNullable(retryIfException(() -> restTemplate
					.exchange(uri, GET, getHttpHeadersEntity(), CatalogConfig.class)
					.getBody()));
		} catch(org.springframework.web.client.HttpClientErrorException.NotFound e) {
			LOGGER.error("CatalogConfig {} not found", id);
			return empty();
		}
	}

	@SneakyThrows
	public Map<Integer, CustomDataFlow> getCustomDataFlows() {
		URI uri = fromHttpUrl(apiCredentialsServer + "/custom_data_flows").build().encode().toUri();
		List<CustomDataFlow> dataSources = retryIfException(() -> restTemplate
			                                                          .exchange(uri, GET, getHttpHeadersEntity(), new ParameterizedTypeReference<List<CustomDataFlow>>() {
			                                                          })
			                                                          .getBody());
		return StreamEx.of(dataSources).mapToEntry(CustomDataFlow::getId, identity()).toMap();
	}

	private static final ExecutionMetricSet doGetDataCredentialsMetrics = metricSet(AdminApiClient.class, "doGetDataCredentials");
	private DataCredentials doGetDataCredentials(int credsId) {
		return doGetDataCredentialsMetrics.track(() -> {
			String uri = apiCredentialsServer + "/data_credentials/" + credsId;
			return retryIfException(() -> restTemplate
				.exchange(uri, GET, getHttpHeadersEntity(), DataCredentials.class)
				.getBody());
		});
	}

	public void updateDataCredentials(
		int credsId,
		Optional<Map<String, ?>> config,
		Optional<String> verifiedStatus
	) {
		Map<Object, Object> requestBody = StreamEx
			                                  .of(
				                                  config.map(c -> map("credentials", c)).orElse(emptyMap()),
				                                  verifiedStatus.map(status -> map("verified_status", status)).orElse(emptyMap())
			                                  )
			                                  .flatMapToEntry(objectObjectMap -> objectObjectMap)
			                                  .toMap();

		HttpEntity request = new HttpEntity<>(toJsonString(requestBody), getHttpHeaders());
		String url = apiCredentialsServer + "/data_credentials/" + credsId;
		dataCredentialsCache.invalidate(credsId);
		restTemplate.put(url, request);
	}

	@SneakyThrows
	public Org updateAccount(Integer resourceId, ResourceType resourceType, String action, AccountStatus status) {
		UpdateAccountRequest updateAccountRequest = new UpdateAccountRequest(action, status.name());
		String requestContent = OBJECT_MAPPER.writeValueAsString(updateAccountRequest);
		HttpEntity request = new HttpEntity<>(requestContent, getHttpHeaders());

		UriComponentsBuilder builder = fromHttpUrl(
			apiCredentialsServer + getResourceEndpoint(resourceType) + "/" + resourceId);

		ResponseEntity<Org> org = restTemplate.exchange(builder.build().encode().toUri(), PUT,
			request, Org.class);

		return org.getBody();
	}

	public DataMap getDataMap(Integer dataMapId) {
		URI uri = fromHttpUrl(apiCredentialsServer + "/data_maps/" + dataMapId)
			          .queryParam("expand", 1)
			          .build().encode().toUri();
		return retryIfException(() -> restTemplate
			                              .exchange(uri, GET, getHttpHeadersEntity(), DataMap.class)
			                              .getBody());
	}

	private static final ExecutionMetricSet doGetDataSetMetrics = metricSet(AdminApiClient.class, "doGetDataSet");
	@SuppressWarnings("AccessStaticViaInstance")
	private Optional<DataSet> doGetDataSet(int dataSetId) {
		try (var ignored = doGetDataSetMetrics.time()) {
			UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/data_sets/" + dataSetId);
			updateExpandRequestParam(builder, expandResources);
			URI uri = builder.build().encode().toUri();
			DataSet dataSet = retryIfException(() -> restTemplate
				                                         .exchange(uri, GET, getHttpHeadersEntity(), DataSet.class)
				                                         .getBody());
			return Optional.ofNullable(dataSet).map(ds -> ds.setObjectVersion(ds.CLASS_VERSION));
		} catch (org.springframework.web.client.HttpClientErrorException.NotFound e) {
			LOGGER.error("DataSet {} not found", dataSetId);
			doGetDataSetMetrics.incError();
			return empty();
		}
	}

	public boolean updateResourceStatus(Integer resourceId, ResourceType resourceType, String action) {
		HttpEntity request = getHttpHeadersEntity();
		UriComponentsBuilder builder = fromHttpUrl(
			apiCredentialsServer + getResourceEndpoint(resourceType) + "/" + resourceId + "/" + action.toLowerCase());

		ResponseEntity<Object> result = restTemplate.exchange(builder.build().encode().toUri(), PUT,
			request, Object.class);
		return result.getStatusCode().is2xxSuccessful();
	}

	@SneakyThrows
	String writeDataSetAsStringForAdminApi(DataSet dataSet) {
		Integer objectVersion = dataSet.getObjectVersion();
		dataSet.setObjectVersion(null);
		String requestBody = OBJECT_MAPPER.writeValueAsString(dataSet);
		dataSet.setObjectVersion(objectVersion);
		return requestBody;
	}

	@SneakyThrows
	public FindOrCreateDataSetResult findOrCreateDataSet(DataSet dataset) {

		String requestBody = writeDataSetAsStringForAdminApi(dataset);
		LOGGER.info("Request body: {}", requestBody);

		HttpEntity request = new HttpEntity<>(requestBody, getHttpHeaders());

		UriComponentsBuilder builder = fromHttpUrl(
			apiCredentialsServer + "/data_sets")
			                               .queryParam("use_source_owner", 1)
			                               .queryParam("detected", 1);

		try {
			String dataSetCreateResponseStr =
				restTemplate
					.exchange(builder.build().encode().toUri(), POST, request, String.class)
					.getBody();

			DataSet dataSetCreateResponse = StreamUtils.jsonUtil().stringToType(dataSetCreateResponseStr, DataSet.class);
			return new FindOrCreateDataSetResult(CREATED, dataSetCreateResponse.getId());
		} catch (HttpStatusCodeException e) {
			if (e.getStatusCode() == HttpStatus.CONFLICT) {
				int existingDataSetId = Integer.parseInt(jsonToMap(e.getResponseBodyAsString()).get("data_set_id").toString());
				return new FindOrCreateDataSetResult(FindOrCreateDataSetResult.Result.FOUND, existingDataSetId);
			} else {
				LOGGER.info("Exception while creating data set: {}", e.getResponseBodyAsString(), e);
				throw e;
			}
		}
	}

	@SneakyThrows
	public Integer createDataSource(DataSource dataSource) {
		DataSourceRequest createRequest = new DataSourceRequest(
			dataSource.getName(),
			ofNullable(dataSource.getOwner()).map(Owner::getId),
			ofNullable(dataSource.getOrg()).map(Org::getId),
			ofNullable(dataSource.getConnectionType()),
			ofNullable(dataSource.getDataSinkId()),
			dataSource.getSourceConfig());
		String requestContent = OBJECT_MAPPER.writeValueAsString(createRequest);
		HttpEntity request = new HttpEntity<>(requestContent, getHttpHeaders());

		UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/data_sources");

		ResponseEntity<DataSource> dataSourceCreateResponse =
			restTemplate.exchange(builder.build().encode().toUri(), POST, request, DataSource.class);
		LOGGER.info("Created Data source with id " + dataSourceCreateResponse.getBody().getId());
		return dataSourceCreateResponse.getBody().getId();
	}

	@SneakyThrows
	public boolean updateDataSink(DataSink dataSink) {

		DataSinkRequest createRequest = new DataSinkRequest(
			dataSink.getName(),
			ofNullable(dataSink.getConnectionType()),
			ofNullable(dataSink.getSinkSchedule()),
			ofNullable(dataSink.getSinkConfig())
		);

		String requestContent = OBJECT_MAPPER.writeValueAsString(createRequest);
		HttpEntity request = new HttpEntity<>(requestContent, getHttpHeaders());

		UriComponentsBuilder builder = fromHttpUrl(
			apiCredentialsServer + "/data_sinks" + "/" + dataSink.getId());

		ResponseEntity<Object> result = restTemplate.exchange(builder.build().encode().toUri(), PUT, request, Object.class);
		invalidate(dataSink.getId(), Optional.of(dataSink.getOriginNodeId()), SINK);
		return result.getStatusCode().is2xxSuccessful();
	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@AllArgsConstructor
	class UpdateDatasetRuntimeConfigRequest {
		@JsonProperty("runtime_config")
		private Map<String, String> runtimeConfig;
	}

	@SneakyThrows
	public DataSet updateDataSetRuntimeConfig(DataSet dataset, Map<String, String> runtimeConfig) {
		UpdateDatasetRuntimeConfigRequest updateRequest = new UpdateDatasetRuntimeConfigRequest(runtimeConfig);
		String requestContent = OBJECT_MAPPER.writeValueAsString(updateRequest);

		logger.info("Update dataset {} runtime config with {}", dataset.getId(), requestContent);

		HttpEntity request = new HttpEntity<>(requestContent.getBytes(UTF_8), getHttpHeaders());
		UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/data_sets/" + dataset.getId());

		try {
			ResponseEntity<DataSet> datasetUpdateResponse =
				restTemplate.exchange(builder.build().encode().toUri(),HttpMethod.PUT, request, DataSet.class);
			invalidate(dataset.getId(), Optional.of(dataset.getOriginNodeId()), DATASET);
			return datasetUpdateResponse.getBody();
		} catch (Exception e) {
			LOGGER.error("Exception while updating dataset runtime config", e);
			if (e instanceof HttpStatusCodeException) {
				LOGGER.error("Response body={}", ((HttpStatusCodeException) e).getResponseBodyAsString());
			}
			return dataset;
		}
	}

	@SneakyThrows
	public DataSet updateDataSetSchema(DataSet dataset) {
		DataSetRequest updateRequest = new DataSetRequest(
			dataset.getName(), ofNullable(dataset.getSourceSchema()), ofNullable(dataset.getOutputSchema()));
		String requestContent = OBJECT_MAPPER.writeValueAsString(updateRequest);
		HttpEntity request = new HttpEntity<>(requestContent.getBytes(UTF_8), getHttpHeaders());

		UriComponentsBuilder builder = fromHttpUrl(
			apiCredentialsServer + "/data_sets/" + dataset.getId())
			                               .queryParam("expand", 1);

		try {
			ResponseEntity<DataSet> datasetUpdateResponse = restTemplate.exchange(builder.build().encode().toUri(),
				HttpMethod.PUT, request, DataSet.class);
			invalidate(dataset.getId(), Optional.of(dataset.getOriginNodeId()), DATASET);
			return datasetUpdateResponse.getBody();
		} catch (Exception e) {
			LOGGER.info("Exception while updating data set", e);
			if (e instanceof HttpStatusCodeException) {
				LOGGER.info("Response body={}", ((HttpStatusCodeException) e).getResponseBodyAsString());
			}
			return dataset;
		}
	}

	@SneakyThrows
	public void putDataSetSamples(List<NexlaMessage> samples, int dataSetId) {
		// Use a different object mapper than other functions in this file so we include nulls in the samples
		String requestContent = jsonMapper.writeValueAsString(samples);
		HttpEntity request = new HttpEntity<>(requestContent.getBytes(UTF_8), getHttpHeaders());

		UriComponentsBuilder builder = fromHttpUrl(
			apiCredentialsServer + "/data_sets/" + dataSetId + "/samples");

		try {
			restTemplate.exchange(builder.build().encode().toUri(), HttpMethod.PUT, request, String.class);
		} catch (Exception e) {
			LOGGER.info("Exception while updating data set {}", dataSetId, e);
			if (e instanceof HttpStatusCodeException) {
				LOGGER.info("Response body={}", ((HttpStatusCodeException) e).getResponseBodyAsString());
			}
		}

	}

	public CodeContainer doGetCodeContainer(Integer codeContainerId) {
		try {
			URI uri = fromHttpUrl(apiCredentialsServer + "/code_containers/" + codeContainerId).build().encode().toUri();
			return retryIfException(() -> restTemplate
				                              .exchange(uri, GET, getHttpHeadersEntity(), CodeContainer.class)
				                              .getBody());
		} catch (RestClientException e) {
			LOGGER.error("error while fetching code container, codeContainerId={}", codeContainerId, e);
			throw new RuntimeException(e);
		}
	}

	public String adminApiGet(String path) {
		String uri = apiCredentialsServer + "/" + path;
		return retryIfException(() -> restTemplate
			                              .exchange(uri, GET, getHttpHeadersEntity(), String.class)
			                              .getBody());
	}


	private static final ExecutionMetricSet doGetDataSinkMetrics = metricSet(AdminApiClient.class, "doGetDataSink");
	@SuppressWarnings("AccessStaticViaInstance")
	private Optional<DataSink> doGetDataSink(Integer sinkId) {
		try (var ignored = doGetDataSinkMetrics.time()) {
			HttpEntity request = getHttpHeadersEntity();
			UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/data_sinks/" + sinkId);
			updateExpandRequestParam(builder, expandResources);
			URI uri = builder.build().encode().toUri();
			DataSink dataSink = retryIfException(() -> restTemplate
				                                           .exchange(uri, GET, request, DataSink.class)
				                                           .getBody());
			return Optional.ofNullable(dataSink).map(ds -> ds.setObjectVersion(ds.CLASS_VERSION));
		} catch (org.springframework.web.client.HttpClientErrorException.NotFound e) {
			LOGGER.error("DataSink {} not found", sinkId);
			doGetDataSinkMetrics.incError();
			return empty();
		}
	}

	private List<DataSink> parseDataSinks(List<Object> sinks) {
		return sinks.stream()
			       .map(resource -> OBJECT_MAPPER.convertValue(resource, DataSink.class))
			       .collect(toList());
	}

	@SneakyThrows
	public List<DataSink> getFileTypeDataSinks() {
		return parseDataSinks(getAllResources(SINK, ImmutableMap.of("sink_type", getFileTypeResourceFilter())));
	}

	private String getFileTypeResourceFilter() {
		return ConnectionType.CONNECTION_TYPE_MAP.values().stream()
			.filter(ConnectionType::isFile)
			.map(ConnectionType::name)
			.collect(joining(","));
	}
	
	@SneakyThrows
	public List<DataSource> getFileTypeDataSources() {
		return parseDataSources(getAllResources(SOURCE, ImmutableMap.of("source_type", getFileTypeResourceFilter())));
	}

	public List<DataSource> parseDataSources(List<Object> sources) {
		return sources.stream()
			       .map(resource -> OBJECT_MAPPER.convertValue(resource, DataSource.class))
			       .collect(toList());
	}

	public Integer createNotification(AdminApiNotification notification) {
		LOGGER.info(toJsonString(notification));

		HttpEntity request = new HttpEntity<>(notification, getHttpHeaders());
		UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/notifications");

		try {
			ResponseEntity<AdminApiNotification> notificationCreateResponse = restTemplate
				                                                                  .exchange(builder.build().encode().toUri(), POST, request, AdminApiNotification.class);
			LOGGER.info("Created Notification with id {}", notificationCreateResponse.getBody().getId());
			return notificationCreateResponse.getBody().getId();
		} catch (HttpClientErrorException e) {
			LOGGER.info("Exception while creating notification" + e.getResponseBodyAsString(), e);
			return 0;
		}

	}

	public Owner getUser(Integer userId) {
		try {
			URI uri = fromHttpUrl(apiCredentialsServer + "/users/" + userId).build().encode().toUri();
			return retryIfException(() -> restTemplate
				                              .exchange(uri, GET, getHttpHeadersEntity(), Owner.class)
				                              .getBody());
		} catch (RestClientException e) {
			LOGGER.error("error while fetching User, userId={}", userId, e);
			throw e;
		}
	}

	public List<Owner> getAllUsers() {
		String uri = apiCredentialsServer + "/users?access_role=all";
		return retryIfException(() -> restTemplate
			                              .exchange(uri, GET, getHttpHeadersEntity(), OWNER_TYPE_REF)
			                              .getBody());
	}

	public HttpStatus resourceAuth(String authHeader, AuthResource authResouce) {

		HttpHeaders headers = new HttpHeaders();
		headers.add("Authorization", authHeader);
		headers.setContentType(MediaType.APPLICATION_JSON);

		if (dataPlaneUid != null) {
			headers.add("X-Nexla-Dataplane", dataPlaneUid);
		}

		String type = authResouce.getResourceType().toString();
		String id = authResouce.getId().toString();

		HashMap<String, String> body = Maps.newHashMap();
		body.put("resource_type", type);
		body.put("resource_id", id);
		if (authResouce.getAccessMode() != null) {
			body.put("access_mode", authResouce.getAccessMode());
		}

		HttpEntity request = new HttpEntity<>(body, headers);
		UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/resource_authorize");
		try {
			ResponseEntity<Object> response = restTemplate.exchange(builder.build().encode().toUri(), POST, request, Object.class);
			return response.getStatusCode();
		} catch (RestClientException e) {
			LOGGER.error("error while authenticating resource, type={} id={}", type, id, e);
			throw e;
		}
	}

	@SneakyThrows
	public List<NotificationSetting> getNotificationSettingsCaching(
		Set<Integer> ownerId,
		Resource resource,
		NotificationEventType eventType
	) {
		return notificationSettingCache.get(new NotificationSettingKey(ownerId, resource, eventType));
	}

	public void invalidateNotificationSettingCache(Map<String, Object> resourceJson) {
		LinkedHashMap<String, Object> ownerMap = (LinkedHashMap) resourceJson.get("owner");
		Integer ownerId = (Integer) ownerMap.get("id");
		Set<NotificationSettingKey> notificationSettingKeys = notificationSettingCache.asMap().keySet().stream()
				.filter(entry -> entry.getOwnerId().contains(ownerId)).collect(Collectors.toSet());
		notificationSettingKeys.forEach(notificationSettingKey -> {
			LOGGER.info("Invalidating cache for notificationSettingKey: {}", notificationSettingKey);
			notificationSettingCache.invalidate(notificationSettingKey);
		});
	}

	private List<NotificationSetting> doGetNotificationSettings(NotificationSettingKey key) {
		HttpEntity request = new HttpEntity<>(ImmutableMap.of("owner_id", key.ownerId, "resource_id", key.resource.id), getHttpHeaders());
		return getNotificationSettingList(request, key.resource.type, key.eventType, empty(), POST, "/notification_setting/list");
	}

	@SneakyThrows
	public List<NotificationSetting> getAllNotificationSettingsCaching(ResourceType resourceType, NotificationEventType eventType, String status) {
		return notificationSettingAllCache.get(new NotificationSettingAllKey(resourceType, eventType, status));
	}

	public List<NotificationSetting> doGetAllNotificationSettings(NotificationSettingAllKey key) {
		String endpoint = "/notification_settings/all";
		return retryIfException(() -> getNotificationSettingList(
			getHttpHeadersEntity(), key.resourceType, key.eventType, of(key.status), GET, endpoint));
	}

	private List<NotificationSetting> getNotificationSettingList(
		HttpEntity request,
		ResourceType resourceType,
		NotificationEventType eventType,
		Optional<String> status,
		HttpMethod httpMethod,
		String endpoint
	) {
		UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + endpoint)
			                               .queryParam("resource_type", resourceType.toString())
			                               .queryParam("event_type", eventType.toString());
		status.ifPresent(val -> builder.queryParam("status", val));

		ResponseEntity<List<NotificationSetting>> notifications = restTemplate.exchange(builder.build().encode().toUri(), httpMethod,
			request, NOTIFICATION_SETTING_TYPE_REF);

		return notifications.getBody();
	}

	public List<QuarantineSetting> getAllQuarantineSettings() {
		try {
			List<QuarantineSetting> settings = Lists.newArrayList();
			int page = 1;
			while (true) {
				URI uri = fromHttpUrl(apiCredentialsServer + "/quarantine_settings/all")
						.queryParam("page", page)
						.queryParam("per_page", PER_PAGE)
						.build()
						.encode()
						.toUri();
				page++;
				List<QuarantineSetting> result = retryIfException(() -> restTemplate
						.exchange(uri, GET, getHttpHeadersEntity(), QUARANTINE_SETTING_TYPE_REF)
						.getBody());
				if (result == null || result.isEmpty()) {
					break;
				}
				settings.addAll(result);
			}
			return settings;
		} catch (RestClientException e) {
			LOGGER.error("error while fetching quarantine settings", e);
			throw e;
		}
	}

	@SneakyThrows
	public DataFlow getDataFlow(Integer dataSetId) {
		try {
			URI uri = fromHttpUrl(apiCredentialsServer + "/data_flows/" + dataSetId)
				          .queryParam("expand", 1)
				          .build().encode().toUri();
			return retryIfException(() -> restTemplate
				                              .exchange(uri, GET, getHttpHeadersEntity(), DataFlow.class)
				                              .getBody());
		} catch (RestClientException e) {
			LOGGER.error("Error fetching data flow", e);
			throw e;
		}
	}

	public Boolean updateDataSetSharedNotification(Set<String> email, Integer dataSetId) {
		HttpEntity request = new HttpEntity<>(singletonMap("email", email), getHttpHeaders());

		UriComponentsBuilder builder =
			fromHttpUrl(apiCredentialsServer + "/data_sets" + "/" + dataSetId + "/sharers/shared");

		LOGGER.info(toJsonString(email));

		try {
			restTemplate.exchange(builder.build().encode().toUri(), PUT, request, Object.class);
			LOGGER.info("Updated DataSet status to shared");
			return true;
		} catch (HttpClientErrorException e) {
			LOGGER.info("Exception while updating DataSet status" + e.getResponseBodyAsString(), e);
			return false;
		}
	}

	public List<ErrorTransform> getErrorTransforms() {
		try {
			URI uri = fromHttpUrl(apiCredentialsServer + "/dashboard_transforms/all").build().encode().toUri();
			return retryIfException(() -> restTemplate
				                              .exchange(uri, GET, getHttpHeadersEntity(), ERROR_TRANSFORM_TYPE_REF)
				                              .getBody());
		} catch (Exception e) {
			LOGGER.error("Error fetching all error transforms", e);
			return emptyList();
		}
	}

	@SneakyThrows
	public List<Org> getAllOrgs() {
		return getAllResources(ORG, emptyMap()).stream()
			       .map(resource -> OBJECT_MAPPER.convertValue(resource, Org.class))
			       .collect(toList());
	}

	@SneakyThrows
	public Org getOrg(Integer orgId) {
		return orgCache.get(orgId);
	}

	private static final ExecutionMetricSet doGetOrgMetrics = metricSet(AdminApiClient.class, "doGetOrgMetrics");
	private Org doGetOrg(Integer orgId) {
		return doGetOrgMetrics.track(() -> {
			URI uri = fromHttpUrl(apiCredentialsServer + "/orgs/" + orgId)
				.queryParam("expand", 1)
				.build().encode().toUri();
			return retryIfException(() -> restTemplate
				.exchange(uri, GET, getHttpHeadersEntity(), Org.class)
				.getBody());
		});
	}

	private void updateExpandRequestParam(UriComponentsBuilder builder, boolean expand) {
		if (expand) {
			builder.queryParam("expand", 1);
		}
	}

	@SneakyThrows
	private List<Object> getAllResources(ResourceType resourceType, Map<String, String> filter) {

		UriComponentsBuilder uriBuilder = fromUriString(apiCredentialsServer)
			                                  .path(getResourceEndpoint(resourceType))
			                                  .path("/all");

		filter.forEach((k, v) -> uriBuilder.queryParam(k, v));
		String url = uriBuilder.toUriString();

		List<Object> resources = Lists.newArrayList();
		int page = 1;

		while (true) {
			URI uriWithPage = fromUriString(url)
				                  .queryParam("page", page)
				                  .queryParam("per_page", PER_PAGE).build()
				                  .encode().toUri();
			page++;
			List<Object> result = retryIfException(() -> restTemplate
				                                             .exchange(uriWithPage, GET, getHttpHeadersEntity(), TYPE_REFERENCE)
				                                             .getBody());
			if (result == null || result.isEmpty()) {
				break;
			}
			resources.addAll(result);
		}
		return resources;
	}

	public static String getResourceEndpoint(ResourceType resourceType) {
		switch (resourceType) {
			case SOURCE:
				return "/data_sources";
			case DATASET:
				return "/data_sets";
			case SINK:
				return "/data_sinks";
			case ORG:
				return "/orgs";
			case USER:
				return "/users";
			case FLOW:
				return "/flows";
			case CREDENTIALS:
				return "/data_credentials";
			default:
				throw new IllegalStateException("Control should not go here");
		}
	}

	@SneakyThrows
	protected <T> HttpEntity<T> getHttpHeadersEntity() {
		return new HttpEntity<>(getHttpHeaders());
	}

	@SneakyThrows
	private HttpHeaders getHttpHeaders() {
		AdminApiToken adminApiToken = adminApiTokenCache.get("access_token");

		HttpHeaders headers = new HttpHeaders();
		headers.add("Authorization", adminApiToken.getTokenType() + " " + adminApiToken.getAccessToken());
		headers.add("User-Agent", appName);

		if (dataPlaneUid != null) {
			headers.add("X-Nexla-Dataplane", dataPlaneUid);
		}

		headers.setContentType(MediaType.APPLICATION_JSON);
		return headers;
	}

	private static final ExecutionMetricSet doGetTransformMetrics = metricSet(AdminApiClient.class, "doGetTransform");
	private Transform doGetTransform(Integer transformId) {
		try (var ignored = doGetTransformMetrics.time()) {
			URI uri = fromHttpUrl(apiCredentialsServer + "/transforms/" + transformId)
				          .queryParam("expand", 1)
				          .build().encode().toUri();
			return retryIfException(() -> restTemplate
				                              .exchange(uri, GET, getHttpHeadersEntity(), Transform.class)
				                              .getBody());
		} catch (org.springframework.web.client.HttpClientErrorException.NotFound e) {
			LOGGER.error("Transform {} not found", transformId);
			doGetTransformMetrics.incError();
			throw e;
		}
	}

	@SneakyThrows
	private Map<Integer, DataSinkCondensed> doGetDataSetAndSinksCondensed() {
		return timed(() -> {
			HttpEntity request = getHttpHeadersEntity();
			URI uri = fromHttpUrl(apiCredentialsServer + "/data_sinks/all/data_set").build().encode().toUri();
			List<DataSinkCondensed> entities = retryIfException(() -> restTemplate
				                                                          .exchange(uri, GET, request, new ParameterizedTypeReference<List<DataSinkCondensed>>() {
				                                                          })
				                                                          .getBody());
			return StreamEx.of(entities).mapToEntry(e -> e.dataSinkId, identity()).toMap();
		}, "getDataSetAndSinksCondensed");
	}

	@SneakyThrows
	private Map<Integer, DataSetCondensed> doGetDataSetsCondensed() {
		return timed(() -> {
			HttpEntity request = getHttpHeadersEntity();
			URI uri = fromHttpUrl(apiCredentialsServer + "/data_sets/all/condensed").build().encode().toUri();
			List<DataSetCondensed> dataSets = retryIfException(() -> restTemplate
				                                                         .exchange(uri, GET, request, new ParameterizedTypeReference<List<DataSetCondensed>>() {
				                                                         })
				                                                         .getBody());
			return StreamEx.of(dataSets).mapToEntry(d -> d.id, identity()).toMap();
		}, "getDataSetsCondensed");
	}

	@SneakyThrows
	private Map<Integer, DataSource> doGetDataSourcesCondensed(Optional<ResourceStatus> resourceStatus) {
		return timed(() -> {
			HttpEntity request = getHttpHeadersEntity();
			UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/data_sources/all/condensed");
			resourceStatus.ifPresent(status -> builder.queryParam("status", status));
			URI uri = builder.build().encode().toUri();
			List<DataSource> dataSources = retryIfException(() -> restTemplate
				                                                      .exchange(uri, GET, request, new ParameterizedTypeReference<List<DataSource>>() {
				                                                      })
				                                                      .getBody());
			return StreamEx.of(dataSources).mapToEntry(DataSource::getId, identity()).toMap();
		}, "getDataSourcesCondensed");
	}

	@SneakyThrows
	public List<DataSet> getAllDataSetsForSourceWithSamples(Integer sourceId) {
		URI uri = fromHttpUrl(apiCredentialsServer + "/data_sets/all")
			          .queryParam("include_samples", 1)
			          .queryParam("data_source_id", sourceId)
			          .queryParam("expand", 1)
			          .build().encode().toUri();
		return retryIfException(() -> restTemplate
			                              .exchange(uri, GET, getHttpHeadersEntity(), DATASET_TYPE_REF)
			                              .getBody());
	}

	public List<Integer> getDataSetsBySink(Integer sinkId) {
		Integer dataSetId = getDataSink(sinkId).get().getDataSetId();
		DataSet dataSet = getDataSet(dataSetId).get();
		List<Integer> dataSetIds = Lists.newArrayList();
		dataSetIds.add(dataSet.getId());

		while (CollectionUtils.isNotEmpty(dataSet.getParentDatasets())) {
			dataSet = getDataSet(dataSet.getParentDatasets().get(0).getId()).get();
			dataSetIds.add(0, dataSet.getId());
		}
		return dataSetIds;
	}

	private static final ExecutionMetricSet getAllResourcesMetrics = metricSet(AdminApiClient.class, "getAllResources").withTimeLogger(LOGGER);
	public Set<Resource> getAllResources() {
		return getAllResourcesMetrics.track(() -> {
			Set<Resource> allResources = Sets.newHashSet();

			dataSourcesIdsByStatus(java.util.Optional.empty())
				.forEach(x -> allResources.add(new Resource(x, ResourceType.SOURCE)));

			dataSetsIdsByStatus(java.util.Optional.empty())
				.forEach(x -> allResources.add(new Resource(x, ResourceType.DATASET)));

			dataSinksIdsByStatus(java.util.Optional.empty())
				.forEach(x -> allResources.add(new Resource(x, ResourceType.SINK)));

			return allResources;
		});
	}

	private static final ExecutionMetricSet dataSetsIdsByStatusMetrics = metricSet(AdminApiClient.class, "dataSetsIdsByStatus");
	public Set<Integer> dataSetsIdsByStatus(Optional<ResourceStatus> resourceStatus) {
		return dataSetsIdsByStatusMetrics.track(() -> {
			UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/data_sets/all/ids");
			resourceStatus.ifPresent(val -> builder.queryParam("status", val));
			URI uri = builder.build().encode().toUri();
			return retryIfException(() -> restTemplate
				.exchange(uri, GET, getHttpHeadersEntity(), new ParameterizedTypeReference<Set<Integer>>() {
				})
				.getBody());
		});
	}

	private static final ExecutionMetricSet dataSinksIdsByStatusMetrics = metricSet(AdminApiClient.class, "dataSinksIdsByStatus");
	public Set<Integer> dataSinksIdsByStatus(Optional<ResourceStatus> resourceStatus) {
		return dataSinksIdsByStatusMetrics.track(() -> {
			UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/data_sinks/all/ids");
			resourceStatus.ifPresent(val -> builder.queryParam("status", val));
			URI uri = builder.build().encode().toUri();
			return retryIfException(() -> restTemplate
				.exchange(uri, GET, getHttpHeadersEntity(), new ParameterizedTypeReference<Set<Integer>>() {
				})
				.getBody());
		});
	}

	private static final ExecutionMetricSet dataSourcesIdsByStatusMetrics = metricSet(AdminApiClient.class, "dataSourcesIdsByStatus");
	public Set<Integer> dataSourcesIdsByStatus(Optional<ResourceStatus> resourceStatus) {
		return dataSourcesIdsByStatusMetrics.track(() -> {
			UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/data_sources/all/ids");
			resourceStatus.ifPresent(val -> builder.queryParam("status", val));
			URI uri = builder.build().encode().toUri();
			return retryIfException(() -> restTemplate
				.exchange(uri, GET, getHttpHeadersEntity(), Set.class)
				.getBody());
		});
	}

	static <T> T retryIfException(Callable<T> callable) {
		return retryIfException(callable, 5, SECONDS.toMillis(5));
	}

	@SneakyThrows
	static <T> T retryIfException(Callable<T> callable, int attemptNumber, long maxAttemptDelayMs) {
		try {
			return RetryerBuilder
				       .<T>newBuilder()
				       .retryIfException(throwable -> !(throwable instanceof HttpClientErrorException.NotFound))
				       .withWaitStrategy(WaitStrategies.randomWait(maxAttemptDelayMs, MILLISECONDS))
				       .withStopStrategy(StopStrategies.stopAfterAttempt(attemptNumber))
				       .withRetryListener(new RetryListener() {
					       @Override
					       public <V> void onRetry(Attempt<V> attempt) {
						       if (attempt.hasException()) {
							       Class<?> excClass = attempt.getExceptionCause().getClass();

							       if (HttpClientErrorException.class.isAssignableFrom(excClass)) {
								       HttpClientErrorException hce = ((HttpClientErrorException) attempt.getExceptionCause());
								       incAdminApiInteractionErrors(hce.getRawStatusCode());
							       } else if (HttpServerErrorException.class.isAssignableFrom(excClass)) {
								       HttpServerErrorException hse = ((HttpServerErrorException) attempt.getExceptionCause());
								       incAdminApiInteractionErrors(hse.getRawStatusCode());
							       } else {
								       LOGGER.warn("AdminApiClient got unexpected exception: {}", excClass.getCanonicalName(), attempt.getExceptionCause());
								       incAdminApiRetryErrors();
							       }
						       }
					       }
				       })
				       .build()
				       .call(callable);
		} catch (ExecutionException | RetryException e) {
			LOGGER.warn("Failed to retry operation: {}", e.getMessage());
			throw e.getCause() != null ? e.getCause() : e;
		}
	}

	private static void incAdminApiInteractionErrors(int errorCode) {
		telemetry.ifPresent(value -> value.recordCounter(AdminApiClient.class.getName() + "_" + errorCode));
	}

	private static void incAdminApiRetryErrors() {
		telemetry.ifPresent(value -> value.recordCounter(AdminApiClient.class.getName() + "_retry_errors_total"));
	}

	private static void incAdminApiConnectionErrors() {
		telemetry.ifPresent(value -> value.recordCounter(AdminApiClient.class.getName() + "_connection_errors_total"));
	}


	/**
	 * Get the flow type of the resource when it was created.
	 * @param resource to check for
	 * @return its FlowType
	 */
	@SneakyThrows
	public Optional<FlowType> getFlowType(Resource resource) {
		try {
			final Optional<NexlaFlow> flow = getFlowByResource(resource);
			return flow.flatMap(nexlaFlow -> getSourceFlowType(resource, nexlaFlow))
					   .or(() -> flow.map(nexlaFlow -> nexlaFlow.flows.get(0).flowType));
		} catch (Exception e) {
			LOGGER.warn("There is no flow or the flow is corrupt for resource {}", resource, e);
			return Optional.empty();
		}
	}

	@SneakyThrows
	private Optional<FlowType> getSourceFlowType(Resource resource, NexlaFlow flow) {
		try {
			Map<String, Object> sourceConfig = flow.getDataSources().get(0).getSourceConfig();
			return Optional.ofNullable(sourceConfig.get(PIPELINE_TYPE)).map(a -> FlowType.fromString(a.toString()));
		} catch (Exception e) {
			LOGGER.warn("There is no flow or the flow is corrupt for resource {}: [{}]", resource, flow, e);
			return Optional.empty();
		}
	}

	@SneakyThrows
	public Optional<NexlaFlow> getFlowByResource(Resource resource) {
		return flowByResourceCache.get(resource);
	}

	private static final ExecutionMetricSet doGetFlowByResourceMetrics = metricSet(AdminApiClient.class, "doGetFlowByResource");
	private Optional<NexlaFlow> doGetFlowByResource(Resource resource) {
		try (var ignored = doGetFlowByResourceMetrics.time()) {
			String url = StreamEx.of(apiCredentialsServer, getResourceEndpoint(resource.type), resource.id, "flow")
					.joining("/");
			URI uri = fromHttpUrl(url).build().encode().toUri();

			return Optional.of(retryIfException(() -> restTemplate
				                                          .exchange(uri, GET, getHttpHeadersEntity(), new ParameterizedTypeReference<NexlaFlow>() {
				                                          })
				                                          .getBody()));
		} catch (org.springframework.web.client.HttpClientErrorException.NotFound e) {
			LOGGER.error("Flow for resource {} not found", resource);
			doGetFlowByResourceMetrics.incError();
			return empty();
		}
	}

	// ========================== Temporary methods for validating resourceJson objects

	public Map<String, Object> getDataSourceRaw(Integer dataSourceId) {
		return getDataSourceRaw(dataSourceId, expandResources);
	}

	private static final ExecutionMetricSet getDataSourceRawMetrics = metricSet(AdminApiClient.class, "getDataSourceRaw");
	public Map<String, Object> getDataSourceRaw(Integer dataSourceId, boolean expand) {
		return getDataSourceRawMetrics.track(() -> {
			UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/data_sources" + "/" + dataSourceId);
			updateExpandRequestParam(builder, expand);
			URI uri = builder.build().encode().toUri();
			return retryIfException(() -> restTemplate
				.exchange(uri, GET, getHttpHeadersEntity(), Map.class)
				.getBody());
		});
	}

	public Map<String, Object> getDataSinkRaw(Integer sinkId) {
		return getDataSinkRaw(sinkId, expandResources);
	}

	private static final ExecutionMetricSet getDataSinkRawMetrics = metricSet(AdminApiClient.class, "getDataSinkRaw");
	public Map<String, Object> getDataSinkRaw(Integer sinkId, boolean expand) {
		return getDataSinkRawMetrics.track(() -> {
			UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/data_sinks/" + sinkId);
			updateExpandRequestParam(builder, expand);
			URI uri = builder.build().encode().toUri();
			return retryIfException(() -> restTemplate
				.exchange(uri, GET, getHttpHeadersEntity(), Map.class)
				.getBody());
		});
	}

	public Map<String, Object> getDataSetRaw(Integer dataSetId) {
		return getDataSetRaw(dataSetId, expandResources);
	}

	private static final ExecutionMetricSet getDataSetRawMetrics = metricSet(AdminApiClient.class, "getDataSetRaw");
	public Map<String, Object> getDataSetRaw(Integer dataSetId, boolean expand) {
		return getDataSetRawMetrics.track(() -> {
			UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/data_sets/" + dataSetId);
			updateExpandRequestParam(builder, expand);
			URI uri = builder.build().encode().toUri();
			return retryIfException(() -> restTemplate
				.exchange(uri, GET, getHttpHeadersEntity(), Map.class)
				.getBody());
		});
	}

	public Map<String, Object> getDataCredentialstRaw(Integer credsId) {
		return getDataCredentialstRaw(credsId, expandResources);
	}

	private static final ExecutionMetricSet getDataCredentialstRawMetrics = metricSet(AdminApiClient.class, "getDataCredentialstRaw");
	public Map<String, Object> getDataCredentialstRaw(Integer credsId, boolean expand) {
		return getDataCredentialstRawMetrics.track(() -> {
			UriComponentsBuilder builder = fromHttpUrl(apiCredentialsServer + "/data_credentials/" + credsId);
			updateExpandRequestParam(builder, expand);
			URI uri = builder.build().encode().toUri();
			return retryIfException(() -> restTemplate
				.exchange(uri, GET, getHttpHeadersEntity(), Map.class)
				.getBody());
		});
	}
}
