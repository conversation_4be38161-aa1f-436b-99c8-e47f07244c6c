package com.nexla.admin.client.flownode;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.common.ConnectionType;
import lombok.Data;
import org.joda.time.LocalDateTime;

import java.util.List;
import java.util.Map;

import static com.nexla.common.datetime.DateTimeUtils.ADMIN_API_DATE_TIME_FORMAT;

@Data
public class FlowNodeDatasource {

    @JsonProperty("id")
    public final int id;

    @JsonProperty("owner_id")
    public final Integer ownerId;

    @JsonProperty("org_id")
    public final Integer orgId;

    @JsonProperty("flow_node_id")
    public final Integer flowNodeId;

    @JsonProperty("origin_node_id")
    public final Integer originNodeId;

    @JsonProperty("name")
    public final String name;

    @JsonProperty("description")
    public final String description;

    @JsonProperty("source_config")
    private Map<String, Object> sourceConfig;

    @JsonProperty("status")
    public final ResourceStatus status;

    @JsonProperty("tags")
    public final List<String> tags;

    @JsonProperty("copied_from_id")
    public final Integer copiedFromId;

    @JsonProperty("created_at")
    @JsonFormat(pattern = ADMIN_API_DATE_TIME_FORMAT)
    private LocalDateTime createdAt;

    @JsonProperty("updated_at")
    @JsonFormat(pattern = ADMIN_API_DATE_TIME_FORMAT)
    private LocalDateTime updatedAt;

    @JsonProperty("data_credentials_id")
    public final Integer dataCredentialsId;

    @JsonProperty("data_sink_id")
    public final Integer dataSinkId;

    @JsonProperty("source_type")
    public final ConnectionType sourceType;

    @JsonProperty("connector_type")
    public final ConnectionType connectorType;

    @JsonProperty("connection_type")
    public final ConnectionType connectionType;
}