package com.nexla.admin.client.flownode;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * main class of the data flows 2.0
 */
@Data
public class NexlaFlow {
    @JsonProperty("flows")
    public final List<AdminApiFlow> flows;

    @JsonProperty("data_sources")
    public final List<FlowNodeDatasource> dataSources;

    @JsonProperty("data_sets")
    public final List<FlowNodeDataset> dataSets;

    @JsonProperty("data_sinks")
    public final List<FlowNodeDatasink> dataSinks;

}