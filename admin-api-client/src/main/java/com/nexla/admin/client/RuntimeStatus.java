package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum RuntimeStatus {
    IDLE("idle"),
    PROCESSING("processing");

    private final String key;

    RuntimeStatus(String key) {
        this.key = key;
    }

    @JsonCreator
    public static RuntimeStatus fromString(String key) {
        return RuntimeStatus.valueOf(key.toUpperCase());
    }

    @JsonValue
    public String getKey() {
        return key;
    }
}