package com.nexla.admin.client.flownode;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.admin.client.ResourceStatus;
import lombok.Data;
import org.joda.time.LocalDateTime;

import java.util.List;

import static com.nexla.common.datetime.DateTimeUtils.ADMIN_API_DATE_TIME_FORMAT;

@Data
public class FlowNodeDataset {

    @JsonProperty("id")
    public final int id;

    @JsonProperty("owner_id")
    public final Integer ownerId;

    @JsonProperty("org_id")
    public final Integer orgId;

    @JsonProperty("flow_node_id")
    public final Integer flowNodeId;

    // aka the main flow id
    @JsonProperty("origin_node_id")
    public final Integer originNodeId;

    @JsonProperty("name")
    public final String name;

    @JsonProperty("description")
    public final String description;

    @JsonProperty("status")
    public final ResourceStatus status;

    // the ones having this id are the datasets detected by Nexla
    @JsonProperty("data_source_id")
    public final Integer dataSourceId;

    @JsonProperty("parent_data_set_id")
    public final Integer parentDataSetId;

    @JsonProperty("tags")
    public final List<String> tags;

    @JsonProperty("copied_from_id")
    public final Integer copiedFromId;

    @JsonProperty("code_container_id")
    public final Integer codeContainerId;

    @JsonProperty("created_at")
    @JsonFormat(pattern = ADMIN_API_DATE_TIME_FORMAT)
    private LocalDateTime createdAt;

    @JsonProperty("updated_at")
    @JsonFormat(pattern = ADMIN_API_DATE_TIME_FORMAT)
    private LocalDateTime updatedAt;

    @JsonProperty("data_sink_ids")
    public final List<Integer> dataSinkIds;

}