package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.NexlaMessage;
import com.nexla.connector.config.FlowType;
import com.nexla.telemetry.jmx.SimpleJmxMetrics;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.joda.time.LocalDateTime;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.datetime.DateTimeUtils.ADMIN_API_DATE_TIME_FORMAT;

@Setter
@Getter
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@JsonInclude(Include.NON_NULL)
public class DataSet implements OwnerAndOrg, FlowNodeResource, WithClassVersion {
	public static final int CLASS_VERSION;
	static {
		CLASS_VERSION = WithClassVersion.calculateVersion(DataSet.class);
		SimpleJmxMetrics.gauge(() -> CLASS_VERSION, DataSet.class, "class", "version");
	}

	@JsonProperty("source_schema")
	private NexlaSchema sourceSchema;

	@JsonProperty("flow_node_id")
	public Integer flowNodeId;

	@JsonProperty("output_schema")
	private NexlaSchema outputSchema;

	@JsonProperty("output_validation_schema")
	private NexlaSchema outputValidationSchema;

	private Integer id;
	private String description;
	private String name;
	private Integer version;

	@JsonProperty("output_schema_validation_enabled")
	private Boolean outputSchemaValidationEnabled;

	@JsonProperty("data_source_id")
	private Optional<Integer> dataSourceId;

	@JsonProperty("parent_data_set_id")
	private Integer parentDataSetId;

	@JsonProperty("data_samples")
	private List<NexlaMessage> dataSamples;

	@JsonProperty("transform_id")
	private Integer transformId;

	@JsonProperty("transform")
	private TransformHistory transformHistory;

	@JsonProperty("runtime_config")
	private Map<String, String> runtimeConfig;

	@JsonProperty("source_path")
	private SourcePath sourcePath;

	@JsonProperty("source_schema_id")
	private Integer sourceSchemaId;

	@JsonProperty("sample_service_id")
	// Corresponds to Partition for detected schemas.. this is used as an index
	// into sample calls to sample service
	private Integer sampleServiceId;

	@JsonProperty("data_sinks")
	private List<DataSink> dataSinks;

	@JsonProperty("sharers")
	private List<Owner> sharers;

	@JsonProperty("external_sharers")
	private List<Owner> externalSharers;

	@JsonProperty("parent_data_sets")
	private List<DataSet> parentDatasets;

	@JsonProperty("parent_data_set_ids")
	private List<Integer> parentDatasetIds;

	@JsonProperty("owner")
	private Owner owner;

	@JsonProperty("org")
	private Org org;

	private ResourceStatus status;

	@JsonProperty("updated_at")
	@JsonFormat(pattern = ADMIN_API_DATE_TIME_FORMAT)
	private LocalDateTime updatedAt;

	@JsonProperty("created_at")
	@JsonFormat(pattern = ADMIN_API_DATE_TIME_FORMAT)
	private LocalDateTime createdAt;

	@JsonProperty("origin_node_id")
	public Integer originNodeId;

	@JsonProperty("flow_type")
	public FlowType flowType;

	@JsonProperty("referenced_resource_ids")
	private Optional<ReferencedResourceIds> referencedResourceIds = Optional.empty();

	@JsonProperty("custom_config")
	public Map<String, Object> customConfig;

	@JsonProperty(OBJECT_VERSION_JSON_PROPERTY)
	private Integer objectVersion = -1;

	@Override
	public DataSet setObjectVersion(Integer objectVersion) {
		this.objectVersion = objectVersion;
		return this;
	}

	@Override
	@JsonIgnore
	public int getClassVersion() {
		return CLASS_VERSION;
	}

}
