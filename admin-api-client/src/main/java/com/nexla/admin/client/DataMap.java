package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString(of = {"id", "mapPrimaryKey", "useVersioning", "emitDataDefault", "dataModelVersion"})
public class DataMap {

	private Integer id;

	@JsonProperty("data_map")
	private List<Map<String, String>> dataMap;

	@JsonProperty("data_defaults")
	private Map<String, String> dataDefaults;

	@JsonProperty("emit_data_default")
	private Boolean emitDataDefault;

	@JsonProperty("use_versioning")
	private Boolean useVersioning;

	@JsonProperty("map_primary_key")
	private String mapPrimaryKey;

	@JsonProperty("data_model_version")
	private String dataModelVersion;

	@JsonProperty("data_sink_id")
	private Integer dataSinkId;

	@JsonProperty("map_entry_schema")
	private NexlaSchema schema;

	@JsonProperty("owner_id")
	private Integer ownerId;

	public DataMap() {
		this.useVersioning = false;
		this.emitDataDefault = false;
	}
}
