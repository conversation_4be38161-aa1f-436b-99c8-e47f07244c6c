package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public class ScriptConfig {

	public static String SCRIPT_TYPE_FILE = "FILE";
	public static String SCRIPT_TYPE_SQL = "SQL";

	public final String repo;
	public final String branch;
	public final String scriptGithubPath;
	public final Optional<String> metaDir;
	public final Map<String, Object> parameters;
	public final Optional<List<ScriptConfigCredential>> credentials;
	public final String type;

	public ScriptConfig(
		@JsonProperty("repo") String repo,
		@JsonProperty("branch") String branch,
		// absolute path to meta dir on storage file system
		@JsonProperty("meta_dir") Optional<String> metaDir,
		@JsonProperty("script_github_path") String scriptGithubPath,
		@JsonProperty("parameters") Map<String, Object> parameters,
		@JsonProperty("credentials") Optional<List<ScriptConfigCredential>> credentials,
		@JsonProperty("script_type") String type
	) {
		this.repo = repo;
		this.branch = branch;
		this.scriptGithubPath = scriptGithubPath;
		this.parameters = parameters;
		this.metaDir = metaDir;
		this.credentials = credentials;
		this.type = Optional.ofNullable(type).orElse(SCRIPT_TYPE_FILE);
	}

}
