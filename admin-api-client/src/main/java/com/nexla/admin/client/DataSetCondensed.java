package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import org.joda.time.LocalDateTime;

import java.util.List;
import java.util.Optional;

import static com.nexla.common.datetime.DateTimeUtils.ADMIN_API_DATE_TIME_FORMAT;

@AllArgsConstructor
public class DataSetCondensed implements OwnerAndOrg {
	@JsonProperty("id")
	public final int id;
	@JsonProperty("data_source_id")
	public final Optional<Integer> sourceId;
	@JsonProperty("status")
	public final ResourceStatus status;
	@JsonProperty("sharers")
	public final List<Owner> sharers;
	@JsonProperty("parent_data_set_ids")
	public final List<Integer> parentDataSetIds;

	@JsonProperty("updated_at")
	@JsonFormat(pattern = ADMIN_API_DATE_TIME_FORMAT)
	public LocalDateTime updatedAt;

	public final Owner owner;

	public final Org org;

	@Override
	public Owner getOwner() {
		return owner;
	}

	@Override
	public Org getOrg() {
		return org;
	}

	@Override
	public Integer getId() {
		return id;
	}
}