package com.nexla.admin.client;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.nexla.admin.utils.SourceUtils;
import com.nexla.common.Resource;
import com.nexla.common.ResourceType;
import com.nexla.control.message.ControlEventType;
import com.nexla.telemetry.utils.ExecutionMetricSet;
import com.nexla.telemetry.utils.ExecutionTelemetryUtils;
import lombok.SneakyThrows;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static com.nexla.telemetry.utils.ExecutionTelemetryUtils.metricSet;

public class AdminApiClientCondensedCached extends AdminApiClientCondensed {
	private final Cache<Integer, Map<String, Object>> dataSourcesCondensed;
	private final Cache<Integer, Map<String, Object>> dataSetsCondensed;
	private final Cache<Integer, Map<String, Object>> dataSinksCondensed;
	private final Cache<Integer, Map<String, Object>> dataCredentialsCondensed;
	private final ConcurrentMap<Resource, ResourceStatus> resourceStatuses;
	private final AdminApiClient adminApiClient;
	private volatile boolean dataSourcesEmpty = true;
	private volatile boolean dataSetsEmpty = true;
	private volatile boolean dataSinksEmpty = true;
	private volatile boolean dataCredentialsEmpty = true;
	private volatile boolean resourceStatusesEmpty = true;

	protected AdminApiClientCondensedCached(AdminApiClient adminApiClient, boolean useOldCondensedMethods) {
		super(adminApiClient, useOldCondensedMethods);
		this.adminApiClient = adminApiClient;
		this.dataSourcesCondensed = CacheBuilder.newBuilder().build();
		this.dataCredentialsCondensed = CacheBuilder.newBuilder().build();
		this.dataSetsCondensed = CacheBuilder.newBuilder().build();
		this.dataSinksCondensed = CacheBuilder.newBuilder().build();
		this.resourceStatuses = new ConcurrentHashMap<>();

		final ExecutionMetricSet defaultMetrics = ExecutionTelemetryUtils.metricSet(AdminApiClientCondensedCached.class, "default");
		defaultMetrics.scheduledGauge("dataSourcesCondensed.size", () -> (double) dataSourcesCondensed.size());
		defaultMetrics.scheduledGauge("dataSetsCondensed.size", () -> (double) dataSetsCondensed.size());
		defaultMetrics.scheduledGauge("dataSinksCondensed.size", () -> (double) dataSinksCondensed.size());
		defaultMetrics.scheduledGauge("dataCredentialsCondensed.size", () -> (double) dataCredentialsCondensed.size());
		defaultMetrics.scheduledGauge("resourceStatuses.size", () -> (double) resourceStatuses.size());
	}

	/**
	 * Method should be called from AdminApiClient for every resource type stored in AdminApiClientCondensed
	 */
	private static final ExecutionMetricSet updateCacheMetrics = metricSet(AdminApiClientCondensedCached.class, "updateCache");
	@SneakyThrows
	public void updateCache(Integer id,
							ResourceType resourceType,
							ControlEventType eventType,
							Optional<Map<String, Object>> resourceJson) {
		updateCacheMetrics.track(() -> {
			updateCondensedMap(id, resourceType, eventType, resourceJson);
			updateResourceStatusMap(id, resourceType, eventType);
		});
	}

	private void updateCondensedMap(Integer id,
									ResourceType resourceType,
									ControlEventType eventType,
									Optional<Map<String, Object>> resourceJson) {
		getCondensedMap(resourceType)
				.ifPresent(condensedMap -> {
					switch (eventType) {
						case DELETE:
							condensedMap.invalidate(id);
							break;
						case CREATE:
						case UPDATE:
						case PAUSE:
						case ACTIVATE:
							resourceJson
								.filter(r -> validate(resourceType, r))
								.ifPresentOrElse(
									res -> condensedMap.put(id, res),
									// we get there after adminApiClient is invalidated, just poll is enough to cause
									// adminApiClientCondensed update
									() -> refetchResource(id, resourceType));
							break;
					}
				});
	}

	private boolean validate(ResourceType resourceType, Map<String, Object> r) {
		switch (resourceType) {
			case SOURCE:
				return SourceUtils.dtoToResourceChecked(r, DataSource.class) != null;
			case DATASET:
				return SourceUtils.dtoToResourceChecked(r, DataSet.class) != null;
			case SINK:
				return SourceUtils.dtoToResourceChecked(r, DataSink.class) != null;
			default:
				return true;
		}
	}

	private static final ExecutionMetricSet refetchResourceMetrics = metricSet(AdminApiClientCondensedCached.class, "refetchResource");
	private void refetchResource(Integer id, ResourceType resourceType) {
		refetchResourceMetrics.incCounter(resourceType.toString());
		switch (resourceType) {
			case SOURCE:
				adminApiClient.getDataSource(id);
				break;
			case SINK:
				adminApiClient.getDataSink(id);
				break;
			case DATASET:
				adminApiClient.getDataSet(id);
				break;
			case CREDENTIALS:
				adminApiClient.getDataCredentials(id);
				break;
			default:    // there are no other condensed maps
		}
	}


	private Optional<Cache<Integer, Map<String, Object>>> getCondensedMap(ResourceType resourceType) {
		switch (resourceType) {
			case SINK:
				return dataSinksEmpty ? Optional.empty() : Optional.of(dataSinksCondensed);
			case SOURCE:
				return dataSourcesEmpty ? Optional.empty() : Optional.of(dataSourcesCondensed);
			case DATASET:
				return dataSetsEmpty ? Optional.empty() : Optional.of(dataSetsCondensed);
			case CREDENTIALS:
				return dataCredentialsEmpty ? Optional.empty() : Optional.of(dataCredentialsCondensed);
			default:
				throw new IllegalStateException("Not supported");
		}
	}

	private void updateResourceStatusMap(Integer id, ResourceType resourceType, ControlEventType eventType) {
		Resource resource = new Resource(id, resourceType);
		switch (eventType) {
			case DELETE:
				resourceStatuses.remove(resource);
				break;
			case CREATE:
				resourceStatuses.put(resource, ResourceStatus.INIT);
				break;
			case PAUSE:
				resourceStatuses.put(resource, ResourceStatus.PAUSED);
				break;
			case ACTIVATE:
				resourceStatuses.put(resource, ResourceStatus.ACTIVE);
				break;
			default:
		}
	}

	/**
	 * Cached analog for super::getAllResourcesByStatus method.
	 * NOT USED (DISABLED) IN 2.16.0 version
	 */
	public Map<Resource, ResourceStatus> getAllResourcesByStatus() {
		if (true) {
			// TODO Ensure there are no issues in NTM before starting to use this method
			return super.getAllResourcesByStatus();
		}
		if (resourceStatusesEmpty) {
			synchronized (resourceStatuses) {
				if (resourceStatusesEmpty) {
					super.getAllResourcesByStatus().forEach(resourceStatuses::putIfAbsent);
					resourceStatusesEmpty = false;
				}
			}
		}
		return Collections.unmodifiableMap(resourceStatuses);
	}

	/**
	 * Cached analog for super::getAllDataCredentialsRaw method.
	 * NOT USED (DISABLED) IN 2.16.0 version
	 */
	@Override
	protected List<Map<String, Object>> getAllDataCredentialsRaw() {
		if (dataCredentialsEmpty) {
			synchronized (dataCredentialsCondensed) {
				if (dataCredentialsEmpty) {
					var condensedMap = this.dataCredentialsCondensed.asMap();
					super.doGetDataCredentialsRaw().forEach(condensedMap::putIfAbsent);
					dataCredentialsEmpty = false;
				}
			}
		}
		return new ArrayList<>(dataCredentialsCondensed.asMap().values());
	}

	/**
	 * Cached analog for super::getDataSetsCondensedRaw method.
	 * NOT USED (DISABLED) IN 2.16.0 version
	 */
	@Override
	protected Map<Integer, Map<String, Object>> getDataSetsCondensedRaw() {
		if (this.dataSetsEmpty) {
			synchronized (dataSetsCondensed) {
				if (dataSetsEmpty) {
					var condensedMap = this.dataSetsCondensed.asMap();
					super.getDataSetsCondensedRaw().forEach(condensedMap::putIfAbsent);
					this.dataSetsEmpty = false;
				}
			}
		}
		return dataSetsCondensed.asMap();
	}

	/**
	 * Cached analog for super::dataSourceCondensedMap method.
	 * NOT USED (DISABLED) IN 2.16.0 version
	 */
	@Override
	protected ConcurrentMap<Integer, Map<String, Object>> dataSourceCondensedMap() {
		if (dataSourcesEmpty) {
			synchronized (dataSourcesCondensed) {
				if (dataSourcesEmpty) {
					var condensedMap = this.dataSourcesCondensed.asMap();
					super.dataSourceCondensedMap().forEach(condensedMap::putIfAbsent);
					this.dataSourcesEmpty = false;
				}
			}
		}
		return dataSourcesCondensed.asMap();
	}

	/**
	 * Cached analog for super::dataSinkCondensedMap method.
	 * NOT USED (DISABLED) IN 2.16.0 version
	 */
	@Override
	protected ConcurrentMap<Integer, Map<String, Object>> dataSinkCondensedMap() {
		if (dataSinksEmpty) {
			synchronized (dataSinksCondensed) {
				if (dataSinksEmpty) {
					var condensedMap = this.dataSinksCondensed.asMap();
					super.dataSinkCondensedMap().forEach(condensedMap::putIfAbsent);
					this.dataSinksEmpty = false;
				}
			}
		}
		return dataSinksCondensed.asMap();
	}
}