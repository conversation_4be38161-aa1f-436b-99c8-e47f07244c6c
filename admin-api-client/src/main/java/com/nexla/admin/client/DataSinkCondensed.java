package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.ConnectionType;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.IngestionMode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Map;
import java.util.Optional;

@AllArgsConstructor
@Getter
@ToString
public class DataSinkCondensed implements FlowNodeResource {
	@JsonProperty("id")
	public final int dataSinkId;
	@JsonProperty("data_set_id")
	public final Optional<Integer> dataSetId;
	@JsonProperty("status")
	public final ResourceStatus dataSinkStatus;

	// aka the main flow id
	@JsonProperty("origin_node_id")
	public Integer originNodeId;

	@JsonProperty("sink_config")
	public Map<String, Object> sinkConfig;

	@JsonProperty("flow_type")
	public FlowType flowType;

	@JsonProperty("sink_type")
	private ConnectionType connectionType;

	@JsonProperty("ingestion_mode")
	public IngestionMode ingestionMode;

}