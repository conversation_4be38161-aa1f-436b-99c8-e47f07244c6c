package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.ConnectionType;
import lombok.Data;

import java.util.Map;
import java.util.Optional;

@Data
public class DataSourceRequest {

	private final String name;

	@JsonProperty("owner_id")
	private final Optional<Integer> owner;
	@JsonProperty("org_id")
	private final Optional<Integer> org;
	@JsonProperty("source_type")
	private final Optional<String> connectionType;
	@JsonProperty("data_sink_id")
	private final Optional<Integer> dataSinkId;
	@JsonProperty("source_config")
	private Map<String, Object> sourceConfig;

	public DataSourceRequest(
		String name,
		Optional<Integer> owner,
		Optional<Integer> org,
		Optional<ConnectionType> connectionType,
		Optional<Integer> dataSinkId,
		Map<String, Object> sourceConfig) {
		this.owner = owner;
		this.org = org;
		this.name = name;
		this.dataSinkId = dataSinkId;
		this.connectionType = connectionType
			.map(Object::toString)
			.map(String::toLowerCase);
		this.sourceConfig = sourceConfig;
	}

}