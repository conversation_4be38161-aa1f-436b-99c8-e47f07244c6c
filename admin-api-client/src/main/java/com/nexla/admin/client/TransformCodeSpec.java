package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class TransformCodeSpec {
    private static String FLINK_SQL = "flink_sql";

    @JsonProperty("code_type")
    private final String codeType;

    private final String language;

    private final String encoding;

    private final String script;

    private Map<String, String> options = new HashMap<>();

    public boolean isSqlTransform() {
        return FLINK_SQL.equals(codeType);
    }
}
