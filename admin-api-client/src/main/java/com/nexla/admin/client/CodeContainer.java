package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Optional;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CodeContainer {

	private final int id;

	@JsonProperty(value = "code_type")
	private final String codeType;

	@JsonProperty(value = "code_encoding")
	private final String codeEncoding;

	@JsonProperty(value = "code")
	private final String code;

	@JsonProperty(value = "runtime_data_credentials")
	private final RuntimeDataCredentials runtimeDataCredentials;

	@JsonProperty(value = "code_config")
	private final Optional<CodeConfig> codeConfig;

	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class CodeConfig {
		private final Optional<String> packages;

		@JsonProperty(value = "extra_data")
		private final Optional<String> extraData;
	}
}
