package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.ConnectionType;
import lombok.Data;

import java.util.Map;
import java.util.Optional;

@Data
public class DataSinkRequest {

	@JsonProperty("name")
	private final String name;

	@JsonProperty("sink_type")
	private final Optional<String> sinkType;

	@JsonProperty("sink_schedule")
	private final Optional<String> sinkSchedule;

	@JsonProperty("sink_config")
	private final Optional<Map<String, Object>> sinkConfig;

	public DataSinkRequest(
		String name,
		Optional<ConnectionType> sinkType,
		Optional<String> sinkSchedule,
		Optional<Map<String, Object>> sinkConfig
	) {
		this.name = name;
		this.sinkType = sinkType
			.map(Object::toString)
			.map(String::toLowerCase);
		this.sinkSchedule = sinkSchedule.filter(e -> !e.isEmpty());
		this.sinkConfig = sinkConfig;
	}
}