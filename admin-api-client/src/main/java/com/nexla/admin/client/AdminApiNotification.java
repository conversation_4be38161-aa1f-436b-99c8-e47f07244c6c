package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.ResourceType;
import com.nexla.common.notify.NotificationLevel;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(Include.NON_NULL)
public class AdminApiNotification {

	/*
	 * level yes one of DEBUG, INFO, WARN, ERROR message_id no system message id
	 * message yes message string, visible to API user resource_type yes if
	 * resource_id present, otherwise no one of SOURCE, PUB,SUB,SET resource_id
	 * yes if resource_type present, otherwise no valid resource id owner_id yes
	 * if resource not present, otherwise no id of user being notified org_id
	 * optional, ignored unless owner_id present org id of user being notified
	 */
	private Integer id;
	private NotificationLevel level;
	private Integer messageId;
	private String message;
	@JsonProperty("resource_type")
	private ResourceType resourceType;
	@JsonProperty("resource_id")
	private Integer resourceId;
	@JsonProperty("owner_id")

	private Integer ownerId;
	@JsonProperty("org_id")
	private Integer orgid;

	private Long ts;
}

