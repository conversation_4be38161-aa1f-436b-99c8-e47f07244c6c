package com.nexla.admin.client.flows;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.admin.client.DataCredentials;
import lombok.Data;

import java.util.List;

@Data
public class DataFlow {

	// TODO wait when backend is compatible with admin api protocol (sundeep?)
//	@JsonProperty("data_sources")
//	private final List<DataSource> dataSources;
//	@JsonProperty("data_sinks")
//	private final List<DataSink> dataSinks;
	@JsonProperty("data_credentials")
	private final List<DataCredentials> dataCredentials;
	private final List<DataFlowElement> flows;

}
