package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class DataSetAndSource {
	public final int id;
	@JsonProperty("data_source_id")
	public final int dataSourceId;
	@JsonProperty("status")
	public final ResourceStatus dataSetStatus;
	@JsonProperty("data_source_status")
	public final ResourceStatus dataSourceStatus;
}