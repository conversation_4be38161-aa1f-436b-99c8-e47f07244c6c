package com.nexla.admin.client.flownode;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.IngestionMode;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AdminApiFlow {

    public final int id;

    @JsonProperty("parent_node_id")
    public final Integer parentNodeId;

    @JsonProperty("origin_node_id")
    public final Integer originNodeId;

    @JsonProperty("data_source_id")
    public final Integer dataSourceId;

    public final ResourceStatus status;

    @JsonProperty("flow_type")
    public final FlowType flowType;

    @JsonProperty("ingestion_mode")
    private final IngestionMode ingestionMode;

    @JsonProperty("children")
    public final List<FlowNodeElement> children;
}