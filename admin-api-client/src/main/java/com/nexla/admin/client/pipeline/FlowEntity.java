package com.nexla.admin.client.pipeline;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.common.ConnectionType;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class FlowEntity {

	public final int id;

	@JsonProperty("connection_type")
	public final ConnectionType connectionType;

	public final ResourceStatus status;
}
