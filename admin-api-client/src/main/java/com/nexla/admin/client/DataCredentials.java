package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.nexla.common.ConnectionType;
import com.nexla.common.CredentialType;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Optional;

@Getter
@Setter
@EqualsAndHashCode
@ToString
public class DataCredentials implements OwnerAndOrg {

	private int id;

	@JsonProperty("credentials_type")
	private CredentialType credentialsType;
	@JsonProperty("credentials_enc")
	private String credentialsEnc;
	@JsonProperty("credentials_enc_iv")
	private String credentialsEncIv;
	@JsonProperty("credentials_version")
	private String credentialsVersion;

	@JsonProperty("verified_status")
	private String verifiedStatus;

	@JsonProperty("owner")
	private Owner owner;
	@JsonProperty("org")
	private Org org;

	@Override
	public Integer getId() {
		return id;
	}

	@JsonSetter("credentials_type")
	public void setCredentialTypeAndConnectionType(String credentialsType) {
		try {
			try {
				ConnectionType connectionType = ConnectionType.fromString(credentialsType.toUpperCase());
				this.credentialsType = connectionType != ConnectionType.UNKNOWN ? connectionType : NonConnectionTypeCredentialType.valueOf(credentialsType.toUpperCase());
			} catch (IllegalArgumentException e){
				this.credentialsType = NonConnectionTypeCredentialType.valueOf(credentialsType.toUpperCase());
			}
		} catch (Exception e) {
			// admin-api can potentially have credential type that we do not yet have; default these to unknown so
			// data credentials can still be deserialized
			this.credentialsType = ConnectionType.UNKNOWN;
		}
	}

	public enum NonConnectionTypeCredentialType implements CredentialType {
		GENERIC;

		@Override
		public Optional<ConnectionType> asConnectionType() {
			return Optional.empty();
		}
	}
}
