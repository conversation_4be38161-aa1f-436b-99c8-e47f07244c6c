package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class UserTier {
	private final Integer id;
	private final String name;

	@JsonProperty("display_name")
	private final String displayName;

	@JsonProperty("record_count_limit")
	private final Long recordCountLimit;

	@JsonProperty("data_source_count_limit")
	private final Integer dataSourceCountLimit;

	@JsonProperty("record_count_limit_time")
	private final TierRecordCountLimitTime recordCountLimitTime;

	@JsonProperty("trial_period_days")
	private final Integer trialPeriodDays;
}
