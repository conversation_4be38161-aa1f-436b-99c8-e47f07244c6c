package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.ConnectionType;
import lombok.Data;

import java.util.Map;

@Data
public class QuarantineSetting {

	private final Integer id;

	@JsonProperty("resource_type")
	private final String resourceType;

	@JsonProperty("resource_id")
	private final Integer resourceId;

	@JsonProperty("credentials_type")
	private final ConnectionType connectionType;

	@JsonProperty("data_credentials")
	private final DataCredentials dataCredentials;

	@JsonProperty("config")
	private final Map<String, String> config;

	@JsonProperty("owner")
	private final Owner owner;

	@JsonProperty("org")
	private final Org org;
}
