package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.joda.time.DateTime;

import java.util.Optional;

@Data
public class Owner {

	private final Integer id;

	@JsonProperty("full_name")
	private final String fullName;

	private final String email;

	@JsonProperty("org_id")
	private final Integer orgId;

	@JsonProperty("team_id")
	private final Integer teamId;

	@JsonProperty("notified_at")
	private final String notifiedAt;

	@JsonProperty("user_tier")
	private final Optional<UserTier> userTier;

	@JsonProperty("rate_limited")
	private final boolean rateLimited;

	@JsonProperty("created_at")
	private final DateTime createdAt;

	private final AccountStatus status;

	@JsonProperty("email_verified_at")
	private final Optional<DateTime> emailVerifiedAt;

	public static Owner getOwnerById(Integer id) {
		return new Owner(id, null, null, null, null, null,
			null, false, null, null, null);
	}

}
