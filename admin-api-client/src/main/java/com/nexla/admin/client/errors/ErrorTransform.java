package com.nexla.admin.client.errors;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.admin.client.Org;
import com.nexla.admin.client.Owner;
import com.nexla.common.ResourceType;
import lombok.Data;

@Data
public class ErrorTransform {
	private final Integer id;
	private final Owner owner;
	private final Org org;

	@JsonProperty("resource_type")
	private final ResourceType resourceType;

	@JsonProperty("resource_id")
	private final Integer resourceId;

	@JsonProperty("error_transform")
	private final Transform transform;
}
