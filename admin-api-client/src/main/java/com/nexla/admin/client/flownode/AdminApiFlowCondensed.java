package com.nexla.admin.client.flownode;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.IngestionMode;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AdminApiFlowCondensed {

    public final int id;
    @JsonProperty("origin_node_id")
    public final Integer originNodeId;
    @JsonProperty("parent_node_id")
    public final Integer parentNodeId;
    @JsonProperty("data_source_id")
    public final Integer dataSourceId;

    public final ResourceStatus status;

    @JsonProperty("flow_type")
    public final FlowType flowType;

    @JsonProperty("ingestion_mode")
    private final IngestionMode ingestionMode;
}