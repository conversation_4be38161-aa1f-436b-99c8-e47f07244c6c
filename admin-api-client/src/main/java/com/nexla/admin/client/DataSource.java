package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.*;
import com.nexla.common.ConnectionType;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.IngestionMode;
import com.nexla.telemetry.jmx.SimpleJmxMetrics;
import java.util.Collections;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.joda.time.LocalDateTime;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.datetime.DateTimeUtils.ADMIN_API_DATE_TIME_FORMAT;

@Getter
@Setter
@EqualsAndHashCode
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataSource implements OwnerAndOrg, WithCreds, WithConnectionType, FlowNodeResource, WithClassVersion {
	public static final int CLASS_VERSION;
	static {
		CLASS_VERSION = WithClassVersion.calculateVersion(DataSource.class);
		SimpleJmxMetrics.gauge(() -> CLASS_VERSION, DataSource.class, "class", "version");
	}

	@JsonProperty("script_config")
	private Optional<ScriptConfig> scriptConfig = Optional.empty();

	@JsonProperty("credentials_type")
	private String credentialsType;

	@JsonProperty("flow_node_id")
	private Integer flowNodeId;

	@JsonProperty("data_credentials")
	private DataCredentials dataCredentials;


	@JsonProperty("credentials_enc")
	private String credentialsEncrypted;

	@JsonProperty("credentials_enc_iv")
	private String credentialsEncryptedIv;

	@JsonProperty("data_sets")
	private List<DataSet> datasets;

	private ResourceStatus status;

	@JsonProperty("source_format")
	private String sourceFormat;

	@JsonProperty("ingest_method")
	private String ingestMethod;

	@JsonProperty("source_config")
	private Map<String, Object> sourceConfig;

	private String name;

	private Integer id;

	private Integer version;

	@JsonProperty("source_url")
	private String sourceUrl;

	@JsonProperty("source_type")
	private ConnectionType connectionType;

	@JsonProperty("data_sink_id")
	private Integer dataSinkId;

	@JsonProperty("data_sink")
	private DataSink dataSink;

	@JsonProperty("owner")
	private Owner owner;

	@JsonProperty("org")
	private Org org;

	@JsonProperty("created_at")
	@JsonFormat(pattern = ADMIN_API_DATE_TIME_FORMAT)
	private LocalDateTime createdAt;

	@JsonProperty("code_container_id")
	private Integer codeContainerId;

	@JsonProperty("run_ids")
	private List<DataSourceRunId> runIds;

	@Override
	public Owner getOwner() {
		return owner;
	}

	@Override
	public Org getOrg() {
		return org;
	}

	// aka the main flow id
	@JsonProperty("origin_node_id")
	public Integer originNodeId;

	@JsonProperty("flow_type")
	public FlowType flowType;

	// added because we need to track original flow type after parsed to enum,
	// once we add this capability to FlowType class we can remove this property
	@JsonIgnore
	public String originalFlowType;

	@JsonSetter("flow_type")
	public void setFlowTypes(String flowType){
		this.flowType = FlowType.fromString(flowType);
		this.originalFlowType = flowType;
	}

	@JsonProperty("ingestion_mode")
	private IngestionMode ingestionMode;

	@JsonProperty("referenced_resource_ids")
	private Optional<ReferencedResourceIds> referencedResourceIds = Optional.empty();

	@JsonProperty("flow_triggers")
	private List<FlowTrigger> flowTriggers = Collections.emptyList();

	public String getStringConfigParam(String key) {
		Object obj = sourceConfig.get(key);
		return obj == null ? null : obj.toString();
	}

	@JsonProperty(OBJECT_VERSION_JSON_PROPERTY)
	private Integer objectVersion = -1;

	@Override
	public DataSource setObjectVersion(Integer objectVersion) {
		this.objectVersion = objectVersion;
		return this;
	}

	@Override
	@JsonIgnore
	public int getClassVersion() {
		return CLASS_VERSION;
	}
}
