package com.nexla.admin.client.pipeline;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.ConnectionType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@Data
public class PDataSource {

	private Integer id;

	@JsonProperty("credentials_type")
	private String credentialsType;

	@JsonProperty("source_type")
	private ConnectionType connectionType;

}
