package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.common.ConnectionType;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Getter
@Setter
public class CustomDataFlow implements OwnerAndOrg {

	@JsonProperty
	private Integer id;

	@JsonProperty
	private String name;

	@JsonProperty
	private String description;

	@JsonProperty("flow_type")
	private String flowType;

	@JsonProperty
	private Owner owner;

	@JsonProperty
	private Org org;

	@JsonProperty
	private ResourceStatus status;

	@JsonProperty
	private Boolean managed;

	@JsonProperty
	private Map<String, Object> config;

}
