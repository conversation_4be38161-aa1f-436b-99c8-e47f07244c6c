package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum ResourceStatus {

	ACTIVE("active"),
	PAUSED("paused"),
	RATE_LIMITED("rate_limited"),
	INIT("init");

	private String key;

	ResourceStatus(String key) {
		this.key = key;
	}

	@JsonCreator
	public static ResourceStatus fromString(String key) {
		return ResourceStatus.valueOf(key.toUpperCase());
	}

	@JsonValue
	public String getKey() {
		return key;
	}

}
