package com.nexla.admin.client;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AdminApiCondensedBuilder {
	private static final Logger logger = LoggerFactory.getLogger(AdminApiCondensedBuilder.class);
	public static volatile AdminApiClientCondensed INSTANCE;

	private boolean useOldCondensedMethods = true;
	private boolean cached = true;

	public AdminApiClientCondensed create(AdminApiClient adminApiClient) {
		if (INSTANCE == null) {
			synchronized (AdminApiCondensedBuilder.class) {
				if (INSTANCE == null) {
					INSTANCE = cached
							? new AdminApiClientCondensedCached(adminApiClient, useOldCondensedMethods)
							: new AdminApiClientCondensed(adminApiClient, useOldCondensedMethods);
				} else {
					logger.warn("Trying to create already initialized AdminApiCondensed instance");
				}
			}
		}
		return INSTANCE;
	}

	public AdminApiCondensedBuilder setUseOldCondensedMethods(boolean useOldCondensedMethods) {
		this.useOldCondensedMethods = useOldCondensedMethods;
		return this;
	}

	public AdminApiCondensedBuilder setCached(boolean cached) {
		this.cached = cached;
		return this;
	}
}
