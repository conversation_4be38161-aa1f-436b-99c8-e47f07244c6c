package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.joda.time.LocalDateTime;

import static com.nexla.common.datetime.DateTimeUtils.ADMIN_API_DATE_TIME_FORMAT;

@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CatalogConfig implements OwnerAndOrg {

    private Integer id;

    @JsonProperty
    private Owner owner;

    @JsonProperty
    private Org org;

    @JsonProperty
    private String name;

    @JsonProperty
    private String description;

    @JsonProperty
    private String status;

    @JsonProperty("config")
    public Config config;

    @JsonProperty
    private String mode;

    @JsonProperty("job_id")
    public Integer jobId;

    @JsonProperty("updated_at")
    @JsonFormat(pattern = ADMIN_API_DATE_TIME_FORMAT)
    private LocalDateTime updatedAt;

    @JsonProperty("created_at")
    @JsonFormat(pattern = ADMIN_API_DATE_TIME_FORMAT)
    private LocalDateTime createdAt;
}
