package com.nexla.admin.client.pipeline;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.DataSource;
import lombok.AllArgsConstructor;
import lombok.ToString;

import java.util.List;

@ToString
@AllArgsConstructor
public class Pipeline {

	@JsonProperty("data_sets")
	public final List<PDataset> dataSets;

	@JsonProperty("data_source")
	public final PDataSource dataSource;

	@JsonProperty("data_sink")
	public final DataSink dataSink;
}
