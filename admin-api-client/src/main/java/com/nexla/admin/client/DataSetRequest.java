package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Optional;

public class DataSetRequest {

	private final String name;

	@JsonProperty("source_schema")
	private final Optional<NexlaSchema> sourceSchema;

	@JsonProperty("output_schema")
	private final Optional<NexlaSchema> outputSchema;

	public DataSetRequest(
		String name,
		@JsonProperty("source_schema") Optional<NexlaSchema> sourceSchema,
		@JsonProperty("output_schema") Optional<NexlaSchema> outputSchema
	) {
		this.name = name;
		this.outputSchema = outputSchema;
		this.sourceSchema = sourceSchema;
	}
}
