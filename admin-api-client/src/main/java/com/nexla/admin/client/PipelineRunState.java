package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum PipelineRunState {

	NOT_STARTED, // this state is not stored in DB
	IN_PROGRESS, // this state is stored when connector is up
	STOPPED, // this state is terminal and can be reached from IN_PROGRESS
	ERROR, // this state is terminal and can be reached from IN_PROGRESS
	DONE; // this state is terminal and can be reached from IN_PROGRESS

	@JsonCreator
	public static PipelineRunState fromString(String key) {
		return PipelineRunState.valueOf(key.toUpperCase());
	}
}
