package com.nexla.admin.client.errors;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nexla.admin.client.Org;
import com.nexla.admin.client.Owner;
import lombok.Data;

@Data
public class Transform {
	private final Integer id;
	private final String name;

	private final Boolean reusable;
	private final Owner owner;
	private final Org org;
	private final String description;

	@JsonProperty("code_type")
	private final String codeType;

	@JsonProperty("output_type")
	private final String outputType;

	@JsonProperty("code")
	private final String code;
}
