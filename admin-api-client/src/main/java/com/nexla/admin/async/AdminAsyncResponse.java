package com.nexla.admin.async;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import java.util.List;
import java.util.Map;

@JsonTypeInfo(
	use = JsonTypeInfo.Id.NAME,
	property = "requestType"
)
@JsonSubTypes({
	@JsonSubTypes.Type(value = DataSourceAdminResponse.class, name = "SOURCE"),
	@JsonSubTypes.Type(value = DataCredsAdminResponse.class, name = "DATA_CREDENTIALS"),
	@JsonSubTypes.Type(value = DataSinkAdminResponse.class, name = "SINK"),
	@JsonSubTypes.Type(value = DataSetAdminResponse.class, name = "DATASET")
})
@JsonIgnoreProperties(ignoreUnknown = true)
public interface AdminAsyncResponse<T> {

	Integer getPages();

	Integer getPage();

	Integer getPageSize();

	String getMessageId();

	List<T> getResponse();

	@JsonIgnore
	AdminRequestType getRequestType();

	Map<String, String> getContext();

}
