package com.nexla.admin.async;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.nexla.admin.client.ResourceStatus;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AdminAsyncRequest {

	public final String messageId;

	public final AdminRequestType requestType;

	public final List<AdminRequestFieldFilter> fieldFilters;

	public final Optional<AdminRequestFieldFilter> connectionTypeFilter;
	public final Optional<AdminRequestFieldFilter> flowTypeFilter;

	public final Optional<Integer> orgId;

	public final Map<String, String> context;

	public final int pageSize;

	public final Optional<ResourceStatus> status;

}
