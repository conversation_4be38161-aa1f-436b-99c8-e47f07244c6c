package com.nexla.admin.async;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AdminApiClientCondensed;
import com.nexla.admin.client.DataSource;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class DataSourceAdminResponse implements AdminAsyncResponse<Map<String, Object>> {

	public final AdminRequestType requestType = AdminRequestType.SOURCE;

	public final Integer pages;

	public final Integer page;

	public final Integer pageSize;

	public final String messageId;

	public final List<Map<String, Object>> response;

	public final Map<String, String> context;

	public List<DataSource> responseParsed() {
		return response
			.stream()
			.map(x -> AdminApiClientCondensed.mapToResource(x, DataSource.class))
			.collect(Collectors.toList());
	}

}
