package com.nexla.admin.async;

import com.nexla.admin.client.AdminApiClientCondensed;
import com.nexla.admin.client.DataSet;
import com.nexla.admin.client.DataSource;
import lombok.Data;
import scala.Int;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class DataSetAdminResponse implements AdminAsyncResponse<Map<String, Object>> {

	public final Integer pages;

	public final Integer page;

	public final Integer pageSize;

	public final String messageId;

	public final List<Map<String, Object>> response;
	public final AdminRequestType requestType = AdminRequestType.DATASET;

	public final Map<String, String> context;

	public List<DataSet> responseParsed() {
		return response
			.stream()
			.map(x -> AdminApiClientCondensed.mapToResource(x, DataSet.class))
			.collect(Collectors.toList());
	}

}
