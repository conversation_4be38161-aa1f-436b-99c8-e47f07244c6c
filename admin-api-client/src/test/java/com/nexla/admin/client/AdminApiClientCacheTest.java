package com.nexla.admin.client;

import com.nexla.common.NotificationEventType;
import com.nexla.common.Resource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.nexla.common.ResourceType.SOURCE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.same;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AdminApiClientCacheTest {

    private static final String API_CREDENTIAL_SERVER = "http://apiCredentialsServer";
    private static final String TOKEN = "token";

    @Test
    public void shouldInvalidateNotificationSettingCache() {

        RestTemplate restTemplate = mock(RestTemplate.class);
        AdminApiClientBuilder adminApiClientBuilder = new AdminApiClientBuilder();
        AdminApiClient adminApiClient = adminApiClientBuilder.create(API_CREDENTIAL_SERVER, TOKEN, restTemplate);

        AdminApiToken adminApiToken = new AdminApiToken("", "", 0);
        when(restTemplate.postForObject(anyString(), any(HttpEntity.class), same(AdminApiToken.class), (Object) any())).thenReturn(adminApiToken);

        NotificationSetting notificationSetting = new NotificationSetting();
        ResponseEntity<List<NotificationSetting>> responseEntity = ResponseEntity.ok(List.of(notificationSetting));
        when(restTemplate.exchange(any(URI.class), same(HttpMethod.POST), any(HttpEntity.class), any(ParameterizedTypeReference.class))).thenReturn(responseEntity);

        Set<Integer> ownerId = Stream.of(100).collect(Collectors.toSet());
        Resource resource = new Resource(1, SOURCE);
        NotificationEventType eventType = NotificationEventType.ACTIVATE;

        Map<String, Object> ownerJson = new LinkedHashMap<>();
        ownerJson.put("id", 100);
        Map<String, Object> resourceJson = new LinkedHashMap<>();
        resourceJson.put("owner", ownerJson);
        resourceJson.put("status", "PAUSED");

        adminApiClient.getNotificationSettingsCaching(ownerId, resource, eventType);
        adminApiClient.invalidateNotificationSettingCache(resourceJson);
        adminApiClient.getNotificationSettingsCaching(ownerId, resource, eventType);

        verify(restTemplate, times(2)).exchange(any(URI.class), same(HttpMethod.POST), any(HttpEntity.class), any(ParameterizedTypeReference.class));
    }
}