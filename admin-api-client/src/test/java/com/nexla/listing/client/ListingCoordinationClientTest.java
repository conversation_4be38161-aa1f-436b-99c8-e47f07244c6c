package com.nexla.listing.client;

import com.github.rholder.retry.RetryException;
import com.nexla.control.coordination.SinkOffset;
import com.nexla.test.UnitTests;
import edu.emory.mathcs.backport.java.util.Collections;
import org.junit.Before;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.AppUtils.authorizationHeader;
import static org.mockito.ArgumentMatchers.any;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.client.ExpectedCount.manyTimes;
import static org.springframework.test.web.client.ExpectedCount.once;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;


@RunWith(MockitoJUnitRunner.class)
@Category(UnitTests.class)
public class ListingCoordinationClientTest {

    final String LISTING_URL = "http://listing.url";
    final String USERNAME = "username";
    final String PASSWORD = "password";
    final String TOPIC = "topic";
    final String PARTITION = "partition";
    final Long OFFSET = 1000L;
    final List<SinkOffset> SINK_OFFSETS = Collections.singletonList(new SinkOffset(0, TOPIC, PARTITION, OFFSET));
    final Integer SINK_ID = 1234;
    private RestTemplate restTemplate;

    MockRestServiceServer server;

    private ListingCoordinationClient coordinationClient;

    @Before
    public void setUp() {
        restTemplate = Mockito.spy(new RestTemplate());
        coordinationClient = new ListingCoordinationClient(LISTING_URL, USERNAME, PASSWORD, restTemplate);
        server = MockRestServiceServer.bindTo(restTemplate).build();
    }

    @Test(expected = RetryException.class)
    public void shouldNotRetryWhenNotFoundWhenGetOffsets() throws URISyntaxException {
        server.expect(once(), requestTo(LISTING_URL + "/sink/" + SINK_ID + "/offset")).andExpect(method(HttpMethod.GET))
                .andRespond(withStatus(HttpStatus.NOT_FOUND));
        try {
            coordinationClient.getSinkOffsets(SINK_ID, 0L, null);
        } finally {
            verify(restTemplate, times(1)).exchange(
                    eq(new URI(LISTING_URL + "/sink/" + SINK_ID + "/offset")),
                    eq(HttpMethod.GET),
                    eq(new HttpEntity<>(authorizationHeader(USERNAME, PASSWORD))),
                    any(ParameterizedTypeReference.class)
            );
        }
    }

    @Test(expected = RetryException.class)
    public void shouldNotRetryWhenNotFoundWhenUpdateOffsets() throws URISyntaxException {
        server.expect(manyTimes(), requestTo(LISTING_URL + "/sink/" + SINK_ID + "/offset")).andExpect(method(HttpMethod.POST))
                .andRespond(withStatus(HttpStatus.NOT_FOUND));
        try {
            coordinationClient.updateSinkOffsets(SINK_ID, SINK_OFFSETS);
        } finally {
            verify(restTemplate, times(1)).postForEntity(
                    eq(new URI(LISTING_URL + "/sink/" + SINK_ID + "/offset").toString()),
                    eq(new HttpEntity<>(toJsonString(SINK_OFFSETS), authorizationHeader(USERNAME, PASSWORD))),
                    any(Class.class)
            );
        }
    }
}
