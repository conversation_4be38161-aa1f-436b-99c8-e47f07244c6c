package com.nexla.probe.gdrive;

import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.ByteArrayContent;
import com.google.api.client.http.FileContent;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.drive.Drive;
import com.google.api.services.drive.DriveScopes;
import com.google.api.services.drive.model.File;
import com.google.api.services.drive.model.FileList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataCredentials;
import com.nexla.common.*;
import com.nexla.common.io.CloseableInputStream;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.probe.ExceptionResolution;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.rest.RestAuthConfig;
import com.nexla.file.service.FileConnectorService;
import com.nexla.file.service.FileDetails;
import com.nexla.listing.client.ListingClient;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.kafka.common.config.AbstractConfig;
import org.apache.kafka.common.config.ConfigException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.nexla.common.ListingResourceType.FILE;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.probe.ExceptionResolution.RETHROW;
import static com.nexla.common.probe.ProbeControllerConstants.*;
import static com.nexla.connector.ConnectorService.AuthResponse.SUCCESS;
import static com.nexla.connector.ConnectorService.AuthResponse.authError;
import static com.nexla.connector.config.file.DirScanningMode.FILES;
import static com.nexla.parser.ParserUtils.EXTENSION_TO_MIME;
import static org.apache.commons.lang3.StringUtils.removeEnd;
import static org.apache.commons.lang3.StringUtils.removeStart;

public class GDriveConnectorService extends FileConnectorService<BaseAuthConfig> {
	public static final String FILE_FIELDS = "id, name, createdTime, modifiedTime, size, md5Checksum, mimeType";
	private static final String SHARED_WITH_ME = "sharedWithMe";

	private static final String SHARED_WITH_ME_DISPLAY_NAME = "Shared With Me";
	public static Map<Pair<String, String>, String> PARENTID_WITH_NAME_TO_ID = Maps.newConcurrentMap();
	private static Map<String, String> FILEID_TO_NAMES = Maps.newConcurrentMap();
	private static Map<Integer, Drive> CRED_TO_SERVICE = Maps.newConcurrentMap();

	private static Set<String> SHARED_DRIVE_IDS = Sets.newConcurrentHashSet();
	private static Logger logger = LoggerFactory.getLogger(GDriveConnectorService.class);

	public GDriveConnectorService(AdminApiClient adminApiClient, ListingClient listingClient, String credentialsDecryptKey) {
		super(adminApiClient, listingClient, credentialsDecryptKey);
	}

	private <T, U> U withClientRetriable(Integer credsId, Function<Drive, U> consumer) {
		try {
			Drive client = CRED_TO_SERVICE.computeIfAbsent(credsId, cId -> createGdrive(credsId));
			return consumer.apply(client);
		} catch (Exception e) {
			if (e.getMessage().contains("authError")) {
				listingClient.refreshRestToken(credsId);
				// invalidate cache to get fresh config after refresh
				adminApiClient.invalidate(credsId, Optional.empty(), ResourceType.CREDENTIALS);
				Drive client = CRED_TO_SERVICE.compute(credsId, (ignored, prev) -> createGdrive(credsId));
				return consumer.apply(client);
			}
			throw e;
		}
	}

	@SneakyThrows
	private Drive createGdrive(Integer credsId) {
		DataCredentials creds = adminApiClient.getDataCredentials(credsId).get();
		Map<String, String> credsMap = NexlaDataCredentials.getCreds(credentialsDecryptKey, creds.getCredentialsEnc(), creds.getCredentialsEncIv());

		Boolean isServiceAccount = Optional.ofNullable(credsMap.get("is_service_account")).map(Boolean::valueOf).orElse(false);

		if (isServiceAccount) {
			GoogleCredential credential = GoogleCredential
										.fromStream(new ByteArrayInputStream(credsMap.get("json_creds").getBytes()))
										.createScoped(List.of(DriveScopes.DRIVE));
			logger.info("Google service creds [creds-{}] created", credsId);
			Drive driveService = new Drive.Builder(
										GoogleNetHttpTransport.newTrustedTransport(),
					GsonFactory.getDefaultInstance(),
					credential
								).setApplicationName("creds-" + credsId).build();
			cacheSharedDriveNames(driveService);

			return driveService;
		} else {
			RestAuthConfig restConfig = new RestAuthConfig(credsMap, credsId);
			GoogleCredential credential = new GoogleCredential().setAccessToken(restConfig.vendorAccessToken);

			logger.info("Google [creds-{}]: token vendor last refreshed at={}", credsId, restConfig.vendorLastRefreshedAt);

			Drive driveService = new Drive.Builder(
					GoogleNetHttpTransport.newTrustedTransport(),
					GsonFactory.getDefaultInstance(), credential
			).setApplicationName("creds-" + credsId)
					.build();

			cacheSharedDriveNames(driveService);

			return driveService;
		}
	}

	@Override
	public void initLogger(ResourceType resourceType, Integer resourceId, Optional<Integer> taskId) {
		logger = new NexlaLogger(logger, new NexlaLogKey(resourceType, resourceId, taskId));
	}

	public static String getFileName(Drive driveService, String fileId) {
		return FILEID_TO_NAMES.computeIfAbsent(fileId, fId -> getFile(driveService, fId).getName());
	}

	@Override
	@SneakyThrows
	public InputStream readInputStreamInternal(FileConnectorAuth config, String file) {
		return withClientRetriable(config.getAuthConfig().getCredsId(), driveService -> readInputStreamInternal(file, config, driveService));
	}

	@SneakyThrows
	private CloseableInputStream readInputStreamInternal(String file, FileConnectorAuth config, Drive driveService) {
		java.io.File tempFile = Files.createTempFile("", "").toFile();

		String fileId = fetchFileId(driveService, config.getPath(), file);

		String mimeType = getFile(driveService, fileId).getMimeType();
		OutputStream outputStream = new FileOutputStream(tempFile);

		if (MIME_GOOGLE_SPREADSHEET.equals(mimeType)) {
			driveService
					.files()
					.export(fileId, MIME_OPEN_XLSX)
					.executeMediaAndDownloadTo(outputStream);
		} else {
			driveService
					.files()
					.get(fileId)
					.setSupportsAllDrives(true)
					.setSupportsTeamDrives(true)
					.executeMediaAndDownloadTo(outputStream);
		}

		return new CloseableInputStream(new FileInputStream(tempFile))
				.onClose(tempFile::delete);
	}

	private String getFileId(String file, Drive driveService) {
		String[] path = file.split("/");
		String fileId = path[path.length - 1];
		return fileId;
	}

	@SneakyThrows
	private static File getFile(Drive driveService, String fileId) {
		return driveService
				.files()
				.get(fileId)
				.setFields(FILE_FIELDS)
				.setSupportsAllDrives(true)
				.setSupportsTeamDrives(true)
				.execute();
	}

	@Override
	@SneakyThrows
	public boolean doesFileExistsInternal(FileConnectorAuth config, String key) {
		return withClientRetriable(config.getAuthConfig().getCredsId(), driveService -> {
			Pair<Boolean, String> appendMode = isAppendMode(key);

			if (appendMode.getLeft()) {
				return fileExistsByName(driveService, config.getPath(), appendMode.getRight());
			}

			// it is used in 2 places: key can be both id and relative path here
			// check id
			try {
				StringUtils.isNotEmpty(getFile(driveService, key).getId());
				return true;
			} catch (Exception e) {
			}

			// check relative path
			String relativePath = removeStart(key, config.getPath());
			String[] splitedPath = config.getPath().split("/");
			Optional<String> driveId = Optional.empty();
			if (splitedPath.length > 0 && SHARED_DRIVE_IDS.contains(splitedPath[0])) {
				driveId = Optional.of(splitedPath[0]);
			}
			List<String> files = getFilesRecursive(driveService, splitedPath[splitedPath.length - 1], Optional.empty(), driveId);
			return files.contains(relativePath);
		});
	}

	private List<String> getFilesRecursive(Drive driveService, String path, Optional<String> parentName, Optional<String> driveId) {
		List<File> files = readFiles(driveService, getTraverseQuery(path), null, driveId.orElse(null)).toList();
		List<String> result = Lists.newArrayList();
		for (File x : files) {
			if (MIME_GDRIVE_FOLDER.equals(x.getMimeType())) {
				Optional<String> pName = parentName.isPresent() ? parentName.map(f -> f + "/" + x.getName()) : Optional.ofNullable(x.getName());
				result.addAll(getFilesRecursive(driveService, x.getId(), pName, driveId));
			}
			String prefix = parentName.map(f -> f + "/").orElse("");
			result.add(prefix + x.getName());
		}
		return result;
	}

	@Override
	@SneakyThrows
	public FileDetails writeInternal(FileConnectorAuth config, String key, java.io.File file) {
		Drive driveService = withClientRetriable(config.getAuthConfig().getCredsId(), ds -> ds);
		String s = removeStart(removeEnd(config.getPath(), "/"), "/");
		List<String> path = Lists.newArrayList(s.split("/"));
		String parentId = path.get(path.size() - 1);

		String displayPath = removeStart(key, config.getPath());
		List<String> filePath = Lists.newArrayList(displayPath.split("/"));
		String fileName = filePath.remove(filePath.size() - 1);
		for (String f : filePath) {
			parentId = getOrCreateFile(driveService, parentId, f, MIME_GDRIVE_FOLDER);
			path.add(parentId);
		}

		String fileId = writeStreamInternal(parentId, fileName, file, driveService);
		path.add(fileId);

		return new FileDetails(fileId, Optional.empty(), Optional.of(displayPath));
	}

	@Override
	public FileDetails writeInternal(FileConnectorAuth c, String key, InputStream inputStream) {
		java.io.File tempFile = copyToTempFile(inputStream);
		try {
			return writeInternal(c, key, tempFile);
		} finally {
			tempFile.delete();
		}
	}

	@SneakyThrows
	public static String getOrCreateFile(Drive driveService, String parentValue, String nameValue, String mime) {
		return PARENTID_WITH_NAME_TO_ID.computeIfAbsent(
				Pair.of(parentValue, nameValue),
				p -> getOrCreateFileInternal(driveService, p, mime));
	}

	@SneakyThrows
	private static String getOrCreateFileInternal(Drive driveService, Pair<String, String> p, String mime) {
		String parent = p.getKey();
		String name = p.getValue();
		List<File> searchFiles = searchByParentAndName(driveService, parent, name);

		return StreamEx.of(searchFiles)
				.filter(x -> mime.equals(x.getMimeType()))
				.findAny()
				.map(File::getId)
				.orElseGet(() -> createFile(driveService, parent, name, mime));
	}

	@SneakyThrows
	private static String createFile(Drive driveService, String parent, String name, String mime) {
		File fileMetadata = new File();
		fileMetadata.setName(name);
		fileMetadata.setParents(Collections.singletonList(parent));
		fileMetadata.setMimeType(mime);
		return driveService.files().create(fileMetadata)
				.setFields("id")
				.setSupportsAllDrives(true)
				.setSupportsTeamDrives(true)
				.execute()
				.getId();
	}

	@SneakyThrows
	private java.io.File copyToTempFile(InputStream inputStream) {
		java.io.File tempFile = Files.createTempFile("", "").toFile();
		FileUtils.copyInputStreamToFile(inputStream, tempFile);
		return tempFile;
	}

	@SneakyThrows
	private String writeStreamInternal(String parent, String filename, java.io.File file, Drive driveService) {
		File fileMetadata = new File();

		fileMetadata.setName(filename);
		fileMetadata.setParents(Arrays.asList(parent));

		String mimeType = Optional.ofNullable(EXTENSION_TO_MIME.get(FilenameUtils.getExtension(filename)))
				.orElse("application/octet-stream");

		FileContent mediaContent = new FileContent(mimeType, file);
		return driveService.files().create(fileMetadata, mediaContent)
				.setSupportsAllDrives(true)
				.setSupportsTeamDrives(true)
				.execute()
				.getId();
	}

	@Override
	@SneakyThrows
	public AuthResponse authenticate(BaseAuthConfig authConfig) {
		try {
			return withClientRetriable(authConfig.getCredsId(), this::doAuth);
		} catch (Throwable e) {
			logger.error("[creds-{}]", authConfig.getCredsId(), e);
			return authError(e);
		}
	}

	@SneakyThrows
	private AuthResponse doAuth(Drive driveService) {
		driveService.files()
				.list()
				.setSupportsAllDrives(true)
				.setSupportsTeamDrives(true)
				.execute();
		return SUCCESS;
	}

	@Override
	public StreamEx<NexlaBucket> listBuckets(AbstractConfig config) {
		return StreamEx.empty();
	}

	@Override
	@SneakyThrows
	public StreamEx<NexlaFile> listTopLevelBuckets(AbstractConfig c) {
		FileSourceConnectorConfig config = (FileSourceConnectorConfig) c;
		return withClientRetriable(config.authConfig.getCredsId(), driveService -> listTopLevelBucketsInternal(config, driveService));
	}

	@SneakyThrows
	public static boolean isFolder(String fileId, Drive drive) {
		String mimeType = getFile(drive, fileId).getMimeType();

		return MIME_GDRIVE_FOLDER.equals(mimeType);
	}

	@SneakyThrows
	private StreamEx<NexlaFile> listTopLevelBucketsInternal(FileSourceConnectorConfig config, Drive driveService) {
		String[] path = config.path.split("/");
		String fileId = path.length > 0 ? path[path.length - 1] : "root";

		if (SHARED_WITH_ME.equals(fileId)) {
			return StreamEx.of(createSharedWithMeFolder())
					.append(listSharedWithMeFolder(driveService, path));
		}

		File fileRequested = getFile(driveService, fileId);

		boolean isFolder = MIME_GDRIVE_FOLDER.equals(fileRequested.getMimeType());

		List<NexlaFile> files;
		if (isFolder) {

			if ("root".equals(fileId)) {

				List<NexlaFile> sharedFolders = listSharedFolders(driveService, path);

				NexlaFile sharedWithMeFolder = createSharedWithMeFolder();

				NexlaFile root = createNexlaFile(driveService, path, fileRequested, null);

				return StreamEx.of(CollectionUtils.union(List.of(root), CollectionUtils.union(sharedFolders, List.of(sharedWithMeFolder))));
			}

			boolean isSharedDrive = SHARED_DRIVE_IDS.contains(path[0]);
			String driveId = isSharedDrive ? path[0] : fileRequested.getDriveId();

			files = readFiles(driveService, getTraverseQuery(fileId), null, driveId)
					.map(file -> createNexlaFile(driveService, path, file, null))
					.collect(Collectors.toList());

		} else {
			files = List.of(createNexlaFile(driveService, path, fileRequested, null));
		}

		return StreamEx.of(files);
	}

	private static List<NexlaFile> listSharedFolders(Drive driveService, String[] path) throws IOException {
		return driveService
				.drives()
				.list()
				.execute()
				.getDrives()
				.stream()
				.map(drive -> Pair.of(drive.getName(), getFile(driveService, drive.getId())))
				.map(pair -> createNexlaFile(driveService, path, pair.getRight(), pair.getLeft()))
				.collect(Collectors.toList());
	}

	private StreamEx<NexlaFile> listSharedWithMeFolder(Drive driveService, String[] path) {
		var query = "sharedWithMe = true";
		return readFiles(driveService, query, null)
				.map(file -> createNexlaFile(driveService, path, file, file.getName()));
	}

	/**
	 * This is necessary because driveService.files() returns incorrect name for shared drives and this leads to
	 * an inconsistent folder name in the UI when we have more than one server instance
	 */
	@SneakyThrows
	private void cacheSharedDriveNames(Drive driveService) {
		driveService
				.drives()
				.list()
				.execute()
				.getDrives()
				.forEach(drive -> {
					SHARED_DRIVE_IDS.add(drive.getId());
					FILEID_TO_NAMES.put(drive.getId(), drive.getName());
				});
	}

	private static NexlaFile createNexlaFile(Drive driveService, String[] path, File file, String displayName) {
		ListingResourceType mimeType = MIME_GDRIVE_FOLDER.equals(file.getMimeType()) ? ListingResourceType.FOLDER : FILE;

		NexlaFile nexlaFile = new NexlaFile(file.getId(), file.getSize(), null, file.getMd5Checksum(), file.getCreatedTime().getValue(), file.getModifiedTime().getValue(), mimeType);
		Map<String, Object> metadata = Maps.newHashMap();

		String fileName = StringUtils.isNotBlank(displayName) ? displayName : file.getName();

		if (!SHARED_DRIVE_IDS.contains(file.getId())) {
			FILEID_TO_NAMES.put(file.getId(), fileName);
		}

		String resultName = StreamEx.of(path)
					.append(file.getId())
					.filter(StringUtils::isNotEmpty)
					.map(id -> {
						if (SHARED_WITH_ME.equals(id)) {
							return id;
						}
						return getFileName(driveService, id);
					})
					.joining("/");

		String resultPath = StreamEx.of(path)
				.append(file.getId())
				.filter(StringUtils::isNotEmpty)
				.joining("/");

		metadata.put(ID, file.getId());
		metadata.put(TREEIFY_PATH, resultPath);
		metadata.put(DISPLAY_PATH, resultName);
		metadata.put(MIME_TYPE, file.getMimeType());

		nexlaFile.setMetadata(Optional.of(metadata));
		return nexlaFile;
	}

	public static NexlaFile createSharedWithMeFolder() {
		NexlaFile nexlaFile = new NexlaFile(SHARED_WITH_ME, 0L, null, null, null, null, ListingResourceType.FOLDER);

		Map<String, Object> metadata = Maps.newHashMap();

		metadata.put(ID, SHARED_WITH_ME);
		metadata.put(TREEIFY_PATH, SHARED_WITH_ME);
		metadata.put(DISPLAY_PATH, SHARED_WITH_ME_DISPLAY_NAME);
		metadata.put(MIME_TYPE,  ListingResourceType.FOLDER);

		nexlaFile.setMetadata(Optional.of(metadata));

		return nexlaFile;
	}

	protected String getTraverseQuery(String parent) {
		String parentFolderId = parent == null ? "root" : parent;
		return "parents = '" + parentFolderId + "' and trashed = false and explicitlyTrashed = false";
	}

	@Override
	@SneakyThrows
	public StreamEx<NexlaFile> listBucketContents(AbstractConfig c) {
		FileSourceConnectorConfig config = (FileSourceConnectorConfig) c;
		StreamEx<NexlaFile> nexlaFiles = listTopLevelBuckets(c)
				.map(x -> {
					// TREEIFY_PATH is used only in probe treeify call of listTopLevelBuckets
					// remove to not put extra information to DB
					x.getMetadata().ifPresent(meta -> meta.remove(TREEIFY_PATH));
					return x;
				});
		if (config.dirScanningMode == FILES) {
			return nexlaFiles.filter(file -> file.getType() == FILE);
		}
		return nexlaFiles;

	}

	private StreamEx<File> readFiles(Drive service, String query, String pageToken) {
		return readFiles(service, query, pageToken, null);
	}

	@SneakyThrows
	private StreamEx<File> readFiles(Drive service, String query, String pageToken, String driveId) {

		Drive.Files.List listQuery = service.files().list()
				.setQ(query)
				.setPageToken(pageToken)
				.setFields("nextPageToken, files(" + FILE_FIELDS + ")")
				.setIncludeItemsFromAllDrives(true)
				.setSupportsAllDrives(true)
				.setSupportsTeamDrives(true);

		if (driveId != null) {
			listQuery.setDriveId(driveId).setCorpora("drive");
		}

		FileList result = listQuery.execute();
		List<File> files = result.getFiles();

		if (files == null || files.isEmpty()) {
			return StreamEx.empty();
		} else {
			String nextPageToken = result.getNextPageToken();
			if (StringUtils.isEmpty(nextPageToken)) {
				return StreamEx.of(files);
			} else {
				StreamEx<File> appendStream = readFiles(service, query, nextPageToken);
				return StreamEx.of(files).append(appendStream);
			}
		}
	}

	@Override
	@SneakyThrows
	public boolean checkWriteAccess(AbstractConfig c) {
		FileSourceConnectorConfig config = (FileSourceConnectorConfig) c;
		return withClientRetriable(config.authConfig.getCredsId(), driveService -> checkWriteAccessInternal(driveService));
	}

	@Override
	@SneakyThrows
	public FileDetails overwriteFileInternal(FileConnectorAuth config, String key, java.io.File file){
		Drive driveService = withClientRetriable(config.getAuthConfig().getCredsId(), ds -> ds);

		String fileId = fetchFileId(driveService, config.getPath(), key);

		byte[] fileContent = FileUtils.readFileToByteArray(file);

		String type = Files.probeContentType(file.toPath());

		ByteArrayContent content = ByteArrayContent.fromString(type, new String(fileContent));

		driveService.files().update(fileId, null, content).execute();

		String displayPath = getFile(driveService, fileId).getName();

		return new FileDetails(fileId, Optional.empty(), Optional.of(displayPath));
	}

	@SneakyThrows
	private Boolean checkWriteAccessInternal(Drive driveService) {
		File fileMetadata = new File();
		fileMetadata.setName("nexla.test");

		java.io.File tempFile = java.io.File.createTempFile("probe-gdrive", ".tmp");
		BufferedWriter writer = new BufferedWriter(new FileWriter(tempFile));
		writer.write("nexla");
		writer.close();

		FileContent mediaContent = new FileContent("plain/text", tempFile);
		File file = driveService.files().create(fileMetadata, mediaContent)
				.setFields("id")
				.setSupportsAllDrives(true)
				.setSupportsTeamDrives(true)
				.execute();

		driveService.files()
				.delete(file.getId())
				.setSupportsAllDrives(true)
				.setSupportsTeamDrives(true)
				.execute();

		return true;
	}

	private Pair<Boolean, String> isAppendMode(String fileName) {
		var appendModeStr = "APPEND_MODE:";

		if (fileName.startsWith(appendModeStr)) {
			return Pair.of(true, fileName.replace(appendModeStr, ""));
		}

		return Pair.of(false, fileName);
	}

	private String fetchFileId(Drive driveService, String path, String fileName) {
		Pair<Boolean, String> appendMode = isAppendMode(fileName);

		if (appendMode.getLeft()) {
			return getFileByName(driveService, path, appendMode.getRight())
					.map(File::getId)
					.orElseThrow(() -> new RuntimeException("File not found! Name="+ fileName));
		}

		return getFileId(fileName, driveService);
	}

	private Boolean fileExistsByName(Drive driveService, String path, String fullFileName) {
		return getFileByName(driveService, path, fullFileName).isPresent();
	}

	private static Optional<File> getFileByName(Drive driveService, String path, String fullFileName) {
		String[] splitPath = path.split("/");
		String relativePath = removeStart(fullFileName, path);
		String parentId = splitPath[splitPath.length - 1];
		Optional<File> fileOutput = Optional.empty();

		for (String name : relativePath.split("/")) {
			Optional<File> parentOptional = searchByParentAndName(driveService, parentId, name)
					.stream()
					.findFirst();

			if (parentOptional.isEmpty()) {
				return Optional.empty();
			}

			parentId = parentOptional.get().getId();
			fileOutput = parentOptional;
		}

		return fileOutput;
	}

	@SneakyThrows
	private static List<File> searchByParentAndName(Drive driveService, String parentId, String name) {
		return driveService
				.files()
				.list()
				.setQ("parents = '" + parentId + "' and trashed = false and explicitlyTrashed = false and name = '" + name + "'")
				.setFields("files(id, name, mimeType)")
				.setSupportsAllDrives(true)
				.setSupportsTeamDrives(true)
				.execute()
				.getFiles();
	}

	@Override
	protected Optional<ExceptionResolution> findResolution(Throwable e) {
		if (e instanceof ConfigException) {
			return Optional.of(RETHROW);
		}

		return Optional.empty();
	}
}
